module.exports = {
  extends: [
    'eslint:recommended',
    'taro/react',
    'prettier',
    'plugin:prettier/recommended',
    'eslint-config-prettier',
    'plugin:@typescript-eslint/recommended',
  ],
  parserOptions: {
    ecmaVersion: 2021, // 设置ECMAScript版本
    sourceType: 'module', // 设置代码模块类型
  },
  rules: {
    'import/no-commonjs': 'off',
    'react/jsx-uses-react': 'off',
    'react/react-in-jsx-scope': 'off',
    'react-hooks/exhaustive-deps': 'off', // 关闭规则
    'prettier/prettier': 'error',
    'no-undef': 'off',
    'react/prop-types': 'off',
    'no-use-before-define': 'off',
    '@typescript-eslint/no-use-before-define': ['error'],
    'arrow-body-style': 'off',
    'prefer-arrow-callback': 'off',
    '@typescript-eslint/no-empty-interface': 'off',
    '@typescript-eslint/no-var-requires': 'off',
    '@typescript-eslint/no-explicit-any': 'off',
    '@typescript-eslint/ban-ts-comment': 'off',
  },
  plugins: ['prettier', '@typescript-eslint'],
};
