# 机场综合管理平台微信小程序

## 开发

### 常规命令

```shell
# 安装依赖
$ pnpm install

# 启动
$ pnpm dev:weapp

# 构建打包 默认会开启包依赖分析  如果需要关闭 可在config/prod.ts中关闭
$ pnpm production:weapp

#自动导入接口
$ pnpm swaggerUi
```

### 项目结构

```
.
├── README.md
├── package-lock.json
├── package.json
├── .env.dev 开发环境
├── .env.prod 生产环境
├── .env.test
├── public
├── templates 接口模版
├── config 项目各环境配置
├── .vscode
│   ├── settings.json
│   └── tsx.code-snippets
├── theme
│   ├── antdTheme.ts
│   └── index.ts tailwind的自定义配置
├── src
│   ├── app.less
│   ├── app.ts
│   ├── pages
│   ├── subpackagesA 子包A
│   ├── subpackagesB 子包B
│   ├── app.config.ts 小程序项目配置
│   ├── index.html
│   ├── api   接口自动化生成
│       ├── apiConfig.ts  请求接口配置
│       ├── servies
│       └── index.ts
│   └── utils
└── tsconfig.json

```

### 项目风格介绍

1. 整体样式请使用 tailwind，所以自定义 class 请慎重且注意环境污染问题，taroui的基础base不适用小程序，taro的px写法需要提前在config定义

2. 请求内置了配置 taro-request

3. 如果使用 taro ui 的话，然后在 app.tsx 引入index.scss注册全局即可

4. 请整体工程使用 pnpm

5. 整体较高模版请维护在.vscode/\*

6. 默认配置多环境 env 文件，在打包命令中可以使用--mode 来指定相关 env 文件

7. 项目组件风格请保持一致，components/index.tsx 或者 components.tsx 请勿混用，子包需要存放在主包的components的内容需要创建主文件夹隔离！！！！components/子包/\*\*

8. 项目接口文件 src/api/\*,除了 axiosConfig 需要自定义之外，其他都使用 pnpm swaggerUi 之后进行操作，初次使用可以参考现有 api，但是导入正确 api 之后请删除原本 demo 中的 api，子包可以根据自己的方式来实现，但请注意大小，不要乱改主包配置

9. 项目中使用的接口数据类型定义请使用 api 自动生成的类型，鼠标悬浮即可获取类型引用

10. 如果你觉得你写不好类型定义并且很想用 any 的时候，请慎重

11. 项目提交规范严格按照[type]: xxxxx 等格式严格校验

12. 项目提交 eslint 以及 ts 的 error 都需处理，代码提交 commit 中有函数校验，校验不过 git 将驳回

13. 子包中的 CustomTabBar 为分包的自定义的底部导航栏，可以根据需要进行修改，这只是提供一个思路，或者是一个页面内存放几个tabbar的组件进行切换，由开发自主决定

14. 为兼容部分旧小程序，本次版本未更新为taro4.x版本，可能存在兼容问题，所以使用taro3.x

15. 使用中如有疑问、改进建议、好的 idea 请提交 issues 或者 直接联系 <EMAIL>

### TODO：

1. 锂电池作为子包引入完成初版，但是锂电池中有less文件引入混乱和font字体库svg超限等问题，后续考虑是否解决，目前不影响功能，只是控制台会有提示

2. 整个小程序与设计师沟通统一标准为390的设计稿，各子项目根据情况做适配

3. 整个分支流程按照 新开发从master切分支，之后依次提交dev -》test-》uat ，最终上线由uat平推master，禁止将dev推送给test和uat等扰乱流程操作，分支名按照[type]-master-xxxx等方式创建分支

4. 禁止提交代码绕过git校验，会在合并审查的时候驳回
