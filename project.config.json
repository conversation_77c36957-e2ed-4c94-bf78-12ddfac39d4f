{"miniprogramRoot": "dist/", "projectname": "myApp", "description": "taro-wx-demo", "appid": "wxfea11f0ae0c4696e", "setting": {"urlCheck": true, "es6": true, "enhance": true, "compileHotReLoad": false, "postcss": false, "minified": false, "compileWorklet": false, "uglifyFileName": false, "uploadWithSourceMap": true, "packNpmManually": false, "packNpmRelationList": [], "minifyWXSS": true, "minifyWXML": true, "localPlugins": false, "disableUseStrict": false, "useCompilerPlugins": false, "condition": false, "swc": false, "disableSWC": true, "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}}, "compileType": "miniprogram", "simulatorPluginLibVersion": {}, "libVersion": "3.8.3", "srcMiniprogramRoot": "dist/", "packOptions": {"ignore": [], "include": []}, "condition": {}, "editorSetting": {"tabIndent": "insertSpaces", "tabSize": 2}}