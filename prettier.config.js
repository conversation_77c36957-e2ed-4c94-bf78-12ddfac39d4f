module.exports = {
  // 每行代码最大长度
  printWidth: 80,
  // 一个tab代表几个空格数
  tabWidth: 2,
  // 是否使用tab进行缩进，默认为false，表示用空格进行缩减
  useTabs: false,
  // 声明后带分号
  semi: true,
  // 使用单引号
  singleQuote: true,
  // 使用单引号
  jsxSingleQuote: false,
  // > 放在末尾
  bracketSpacing: true,
  // > 放在末尾
  jsxBracketSameLine: false,
  // 箭头函数参数只有一个参数时省略括号
  arrowParens: 'avoid',
  trailingComma: 'all',
  endOfLine: 'auto',
};
