{"name": "amp-miniapp", "version": "1.0.0", "private": true, "description": "机场综合管理平台-微信小程序", "templateInfo": {"name": "default", "typescript": true, "css": "Less", "framework": "React"}, "scripts": {"dev:weapp": "taro build --type weapp  --watch --mode dev", "production:weapp": "taro build --type weapp  --mode prod", "test:weapp": "taro build --type weapp  --mode test", "lint": "eslint --ext .js,.jsx,.ts,.tsx ./src", "lint:fix": "eslint --fix --ext .js,.jsx,.ts,.tsx ./src", "format": "prettier --write 'src/**/*.{js,jsx,ts,tsx,css,md,json}' --config ./prettier.config.js", "prepare": "husky install", "swaggerUi": "node swaggerUi", "preinstall": "npx only-allow pnpm"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"src/**/*.(js|ts|jsx|tsx)": "eslint --max-warnings 0"}, "config": {"commitizen": {"path": "node_modules/cz-customizable"}, "cz-customizable": {"config": "config/.cz-config.js"}}, "browserslist": ["last 3 versions", "Android >= 4.1", "ios >= 8"], "author": "", "dependencies": {"@babel/runtime": "7.21.5", "@nutui/nutui-react-taro": "2.7.0", "@tarojs/components": "3.6.21", "@tarojs/helper": "3.6.21", "@tarojs/plugin-framework-react": "3.6.21", "@tarojs/plugin-html": "3.6.21", "@tarojs/plugin-platform-weapp": "3.6.21", "@tarojs/react": "3.6.21", "@tarojs/runtime": "3.6.21", "@tarojs/shared": "3.6.21", "@tarojs/taro": "3.6.21", "crypto-es": "2.1.0", "dayjs": "1.11.13", "react": "18.0.0", "react-dom": "18.0.0", "taro-code": "^4.0.1", "zustand": "^5.0.4"}, "devDependencies": {"@babel/core": "7.8.0", "@commitlint/cli": "18.4.3", "@commitlint/config-conventional": "18.4.3", "@pmmmwh/react-refresh-webpack-plugin": "0.5.5", "@tarojs/cli": "3.6.21", "@tarojs/taro-loader": "3.6.21", "@tarojs/test-utils-react": "0.1.1", "@tarojs/webpack5-runner": "3.6.21", "@types/node": "18.15.11", "@types/react": "18.0.0", "@types/webpack-env": "1.13.6", "@typescript-eslint/eslint-plugin": "6.2.0", "@typescript-eslint/parser": "6.2.0", "autoprefixer": "10.4.16", "babel-preset-taro": "3.6.21", "commitizen": "4.3.0", "commitlint-config-cz": "0.13.3", "cz-customizable": "7.0.0", "eslint": "8.56.0", "eslint-config-prettier": "9.1.0", "eslint-config-taro": "3.6.21", "eslint-plugin-import": "2.12.0", "eslint-plugin-prettier": "5.1.2", "eslint-plugin-react": "7.8.2", "eslint-plugin-react-hooks": "4.2.0", "husky": "8.0.0", "lint-staged": "15.2.0", "postcss": "8.4.18", "postcss-rem-to-responsive-pixel": "6.0.1", "prettier": "3.1.1", "react-refresh": "0.11.0", "stylelint": "14.4.0", "swagger-typescript-api": "13.0.3", "tailwindcss": "3.4.0", "ts-node": "10.9.1", "tsconfig-paths-webpack-plugin": "4.0.1", "typescript": "5.2.2", "webpack": "5.78.0", "webpack-bundle-analyzer": "4.10.1"}}