import { Image, View, Text } from '@tarojs/components';
// import LogoEntry from '@/assets/img/logo_entry.png';
import EntryImg1 from '@/assets/img/entry_img1.png';
import EntryImg2 from '@/assets/img/entry_img2.png';
// import EntryImg3 from '@/subpackagesSqcli/assets/img/entry_img3.png';
import Taro from '@tarojs/taro';
import { useEffect, useMemo, useState } from 'react';
import api from '@/api';
import {
  getDataFromLocalStorage,
  saveDataToLocalStorage,
  TOKENBASENAME,
} from '@/utils';
import './index.less';

const colorList = ['#F0F7FF', '#F0FFFD', '#FFF0F0', '#FFF8F0'];
const borderColorList = ['#D6E5FF', '#D6FFF7', '#FFD6D6', '#FFEBD6'];
const bgImgList = [EntryImg1, EntryImg2];

const Entry = () => {
  const [animateItems, setAnimateItems] = useState(false);
  const [menuList, setMenuList] = useState<any[]>([]);

  useEffect(() => {
    getMenuList();
    // 页面加载后触发动画
    setTimeout(() => {
      setAnimateItems(true);
    }, 100);
  }, []);
  const getMenuList = async () => {
    const res = (await api.ampLoginApis.menuUsingGet()) as any;
    setMenuList((res.data?.menu || []).filter(item => item.clientType === 1));
  };
  const toSubPackage = (e: any) => {
    Taro.reLaunch({ url: `${e.routeUrl}` });
    saveDataToLocalStorage(
      `${e.subsystemCode}-${TOKENBASENAME}`,
      getDataFromLocalStorage(TOKENBASENAME),
    );
  };

  const entryList = useMemo(() => {
    if (!menuList.length) return [];
    return menuList.map((item, index) => ({
      title: item.name,
      icon: bgImgList[index] || bgImgList[0],
      onClick: () => toSubPackage(item),
      color: colorList[index] || colorList[0],
      borderColor: borderColorList[index] || borderColorList[0],
      delay: 0,
    }));
  }, [menuList]);
  return (
    <View className="index-page">
      <View className="entry-container">
        <View className="header">
          <View className="title-container">
            <Text className="title-text">拉萨机场</Text>
            <Text className="dot-separator">·</Text>
            <Text className="gradient-text">运行管理平台</Text>
          </View>
          <View className="subtitle">统一登录、在线协同，助力机场工作人员</View>
        </View>
        <View className="cards-container">
          {entryList.map((item, index) => (
            <View
              key={index}
              className={`entry-card ${animateItems ? 'animate-in' : ''}`}
              style={{
                backgroundColor: item.color,
                borderColor: item.borderColor,
                animationDelay: `${item.delay}ms`,
              }}
              onClick={() => item.onClick()}
            >
              <Image src={item.icon} className="card-icon" />
              <View className="card-title">{item.title}</View>
            </View>
          ))}

          {/* 更多系统建设中卡片 */}
          <View
            className={`entry-card coming-soon-card ${
              animateItems ? 'animate-in' : ''
            }`}
            style={{
              backgroundColor: '#F8F9FA',
              borderColor: '#E9ECEF',
              animationDelay: `${entryList.length * 100}ms`,
            }}
          >
            <View className="coming-soon-icon">
              <Text className="icon-text"></Text>
            </View>
            <View className="card-title">更多系统</View>
            <View className="card-subtitle">正在建设中...</View>
          </View>
        </View>
      </View>
    </View>
  );
};

export default Entry;
