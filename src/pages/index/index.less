.gradient-text {
  color: transparent;
  background: linear-gradient(45deg, #396ff7, #4ebcf7);
  background-clip: text;
  -webkit-background-clip: text;
  font-size: 24px;
  font-weight: bold;
  display: inline-block;
}

.text-secondary {
  color: #404245;
}

.index-page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #fff;
  padding: 0 20px;
}

.entry-container {
  flex: 1;
  padding-top: 20px; /* 给导航栏留出空间 */
  display: flex;
  flex-direction: column;
  background-color: #fff;
}

.header {
  text-align: center;
  padding: 30px 0;
  animation: fadeIn 0.8s ease-in-out;
}

.title-container {
  margin-bottom: 12px;
}

.title-text {
  font-size: 24px;
  font-weight: 600;
  color: #1d1f20;
}

.dot-separator {
  margin: 0 6px;
  font-size: 24px;
  font-weight: 600;
  color: #1d1f20;
}

.subtitle {
  font-size: 14px;
  color: #404245;
  opacity: 0.8;
}

.cards-container {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  margin-top: 20px;
}

.entry-card {
  width: 47%;
  border-radius: 16px;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px 16px;
  margin-bottom: 20px;
  box-sizing: border-box;
  border: 1px solid transparent;
  transform: translateY(20px);
  opacity: 0;
  transition: all 0.2s ease-in-out;
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.03);

  &:active {
    transform: scale(0.97);
  }
}

.animate-in {
  transform: translateY(0);
  opacity: 1;
}

.card-icon {
  width: 60px;
  height: 60px;
  margin-bottom: 12px;
  transition: transform 0.3s ease;
}

.entry-card:hover .card-icon {
  transform: scale(1.05);
}

.card-title {
  font-size: 16px;
  font-weight: 500;
  color: #1d1f20;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 更多系统建设中卡片样式 */
.coming-soon-card {
  position: relative;
  opacity: 0.8;

  &:active {
    transform: scale(1) !important; /* 禁用点击缩放效果 */
  }
}

.coming-soon-icon {
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 12px;
  border-radius: 50%;
  background-color: #f1f3f4;
}

.icon-text {
  font-size: 28px;
  line-height: 1;
}

.card-subtitle {
  font-size: 12px;
  color: #8e9196;
  margin-top: 4px;
  font-weight: 400;
}
