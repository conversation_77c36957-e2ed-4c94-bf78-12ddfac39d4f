import { View, WebView } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { useEffect } from 'react';

const WebViewOne = () => {
  const params: any = Taro.getCurrentInstance()?.router?.params;

  useEffect(() => {
    Taro.showLoading();
  }, [params.url]);

  const handleLoad = () => {
    Taro.hideLoading();
  };

  const handleError = () => {
    Taro.hideLoading();
    Taro.showModal({
      title: '加载失败',
    });
  };

  return (
    <View>
      <WebView src={params.url} onLoad={handleLoad} onError={handleError} />
    </View>
  );
};

export default WebViewOne;
