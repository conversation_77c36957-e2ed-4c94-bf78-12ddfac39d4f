@light-blue: #396ff7;
@gray-bg: #f7f9ff;
@primary-color: #396ff7;
@danger-color: #ff4757;
@success-color: #2ed573;
@warning-color: #ffa502;

.my-page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #fff;
}

.my-container {
  flex: 1;
  padding: 0 20px;
  padding-top: 20px;
  box-sizing: border-box;
  background-color: #fff;
}

.loading,
.error-message {
  text-align: center;
  padding: 50px 0;
  color: #666;
  font-size: 16px;
}

.user-info-section {
  background-color: #fff;
  border-radius: 20px;
  padding: 28px 24px;
  margin-bottom: 24px;
  box-shadow: 0 8px 24px rgba(57, 111, 247, 0.08);
  border: 1px solid rgba(57, 111, 247, 0.06);
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}

.avatar-container {
  margin-bottom: 20px;
  position: relative;

  &::after {
    content: '';
    position: absolute;
    top: -6px;
    left: -6px;
    right: -6px;
    bottom: -6px;
    border: 2px solid rgba(57, 111, 247, 0.1);
    border-radius: 50%;
    background: linear-gradient(
      45deg,
      rgba(57, 111, 247, 0.1),
      rgba(90, 124, 250, 0.1)
    );
  }
}

.avatar {
  width: 88px;
  height: 88px;
  border-radius: 50%;
  border: 3px solid @primary-color;
  position: relative;
  z-index: 1;
}

.user-details {
  width: 100%;
}

.user-name {
  font-size: 22px;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 6px;
  text-align: center;
  display: block;
}

.user-role {
  font-size: 15px;
  color: @primary-color;
  margin-bottom: 24px;
  text-align: center;
  display: block;
  font-weight: 500;
}

.user-info-item {
  display: flex;
  padding: 14px 16px;
  margin-bottom: 8px;
  background-color: #f8f9ff;
  border-radius: 12px;
  border: 1px solid rgba(57, 111, 247, 0.05);
  transition: all 0.3s ease;

  &:hover {
    background-color: rgba(57, 111, 247, 0.03);
    transform: translateY(-1px);
  }

  &:last-child {
    margin-bottom: 0;
  }
}

.label {
  font-size: 14px;
  color: #5a6c7d;
  width: 60px;
  flex-shrink: 0;
  font-weight: 600;
}

.value {
  font-size: 14px;
  color: #2c3e50;
  flex: 1;
  font-weight: 500;
}

.function-section {
  background-color: #fff;
  border-radius: 20px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 8px 24px rgba(57, 111, 247, 0.08);
  border: 1px solid rgba(57, 111, 247, 0.06);
}

.function-title {
  font-size: 18px;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 20px;
  padding-left: 12px;
  border-left: 4px solid @primary-color;
}

.function-list {
  margin-top: 12px;
}

.function-item {
  display: flex;
  align-items: center;
  padding: 16px 12px;
  border-radius: 12px;
  margin-bottom: 8px;
  background-color: #f8f9ff;
  border: 1px solid rgba(57, 111, 247, 0.05);
  transition: all 0.3s ease;

  &:hover {
    background-color: rgba(57, 111, 247, 0.03);
    transform: translateY(-1px);
  }

  &:last-child {
    margin-bottom: 0;
  }
}

.function-item-text {
  font-size: 15px;
  color: #2c3e50;
  font-weight: 500;
}

.logout-section {
  margin-top: 40px;
  padding-bottom: 20px;
}

.logout-button {
  width: 100%;
  height: 52px;
  line-height: 52px;
  background: linear-gradient(135deg, #ff6b7a 0%, #ff8a95 100%);
  color: #fff;
  border-radius: 16px;
  font-size: 16px;
  font-weight: 600;
  text-align: center;
  border: none;
  box-shadow: 0 6px 20px rgba(255, 71, 87, 0.3);
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      90deg,
      transparent,
      rgba(255, 255, 255, 0.2),
      transparent
    );
    transition: left 0.5s ease;
  }

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 71, 87, 0.4);

    &::before {
      left: 100%;
    }
  }

  &:active {
    transform: translateY(0);
    box-shadow: 0 4px 15px rgba(255, 71, 87, 0.3);
  }
}
