import { View, Image, Text, Button } from '@tarojs/components';
import { useEffect, useState } from 'react';
import Taro from '@tarojs/taro';
import { UserCenterTenantVO } from '@/api/servies/ampUcLogin';
import api from '@/api/index';
import { AES, loginOut } from '@/utils/tools';
import './index.less';

const MyPage = () => {
  const [userInfo, setUserInfo] = useState<UserCenterTenantVO | null>(null);
  const [loading, setLoading] = useState(true);

  // 获取用户信息
  const fetchUserInfo = async () => {
    try {
      setLoading(true);
      const res = await api.ampLoginApis.getUserInfoUsingGet();
      if (res && res.data && res.code === 200) {
        setUserInfo(res.data);
      }
      setLoading(false);
    } catch (error) {
      console.error('获取用户信息失败', error);
      setLoading(false);
    }
  };

  // 退出登录
  const handleLogout = async () => {
    try {
      Taro.showLoading({ title: '退出中...' });
      await api.ampLoginApis.logoutUsingGet();
      Taro.hideLoading();
      Taro.showToast({
        title: '退出成功',
        icon: 'success',
        duration: 1500,
        success: () => {
          // 清除本地存储的token等信息
          Taro.removeStorageSync('token');
          // 跳转到登录页
          setTimeout(() => {
            loginOut();
          }, 1000);
        },
      });
    } catch (error) {
      console.error('退出登录失败', error);
      Taro.hideLoading();
      Taro.showToast({
        title: '退出失败',
        icon: 'error',
      });
    }
  };

  useEffect(() => {
    fetchUserInfo();
  }, []);

  return (
    <View className="my-page">
      <View className="my-container">
        {/* 用户信息部分 */}
        <View className="user-info-section">
          {loading ? (
            <View className="loading">加载中...</View>
          ) : userInfo ? (
            <>
              <View className="avatar-container">
                <Image
                  className="avatar"
                  src={
                    userInfo.user?.avatar ||
                    'https://joeschmoe.io/api/v1/random'
                  }
                />
              </View>
              <View className="user-details">
                <Text className="user-name">
                  {userInfo.user?.employeeName ||
                    userInfo.user?.name ||
                    '未设置姓名'}
                </Text>
                <Text className="user-role">{userInfo.tenantName || ''}</Text>
                <View className="user-info-item">
                  <Text className="label">工号：</Text>
                  <Text className="value">
                    {userInfo.user?.jobNumber || '未设置'}
                  </Text>
                </View>
                <View className="user-info-item">
                  <Text className="label">部门：</Text>
                  <Text className="value">
                    {userInfo.user?.organizationName || '未设置'}
                  </Text>
                </View>
                <View className="user-info-item">
                  <Text className="label">手机：</Text>
                  <Text className="value">
                    {userInfo.user?.phone
                      ? AES.decrypt(userInfo.user?.phone)
                      : '未设置'}
                  </Text>
                </View>
                <View className="user-info-item">
                  <Text className="label">邮箱：</Text>
                  <Text className="value">
                    {userInfo.user?.emailAddress || '未设置'}
                  </Text>
                </View>
              </View>
            </>
          ) : (
            <View className="error-message">获取用户信息失败，请重试</View>
          )}
        </View>

        {/* 退出登录按钮 */}
        <View className="logout-section">
          <Button className="logout-button" onClick={handleLogout}>
            退出登录
          </Button>
        </View>
      </View>
    </View>
  );
};

export default MyPage;
