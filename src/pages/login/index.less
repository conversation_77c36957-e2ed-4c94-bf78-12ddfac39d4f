.login-page {
  width: 100vw;
  height: 100vh;
  background: linear-gradient(135deg, #2b5876 0%, #4e4376 100%);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  position: relative;

  .login-banner {
    width: 100%;
    height: 45vh;
    position: relative;
    overflow: hidden;
    background: linear-gradient(135deg, #2b5876 0%, #4e4376 100%);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);

    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      height: 40px;
      background: #fff;
      border-radius: 50% 50% 0 0;
    }

    .logo-container {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      display: flex;
      flex-direction: column;
      align-items: center;
      z-index: 1;
      width: 100%;
      animation: logoFadeIn 1.2s ease-out forwards;
      padding: 0 20px;
    }

    .banner-logo {
      width: 240px;
      height: 90px;
      margin-bottom: 0;
      border-radius: 16px;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.25);
      transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
      animation: logoFloat 3s ease-in-out infinite;
      background: rgba(255, 255, 255, 0.98);
      padding: 12px;
      backdrop-filter: blur(10px);

      &:active {
        transform: scale(0.95);
      }
    }
  }

  .login-form {
    padding: 20px 30px;
    background: #ffffff;
    position: relative;
    display: flex;
    flex-direction: column;
    animation: fadeInUp 0.8s ease-out forwards;
    border-radius: 20px;
    margin-top: -25px;
    flex: 1;
    z-index: 10;

    .form-title {
      font-size: 28px;
      font-weight: bold;
      color: #333;
      margin-bottom: 35px;
      text-align: center;
      background: linear-gradient(to right, #2b5876, #4e4376);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }

    .input-item {
      margin-bottom: 25px;
      position: relative;
      border-bottom: 1px solid #eaeaea;
      padding-bottom: 12px;
      padding-left: 5px;
      transition: all 0.3s ease;

      &:focus-within {
        border-bottom: 1px solid #2b5876;
      }

      .icon {
        width: 32px;
        height: 32px;
        margin-right: 15px;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .input {
        flex: 1;
        height: 46px;
        font-size: 16px;
        border: none;
        background: transparent;
      }

      .captcha-img {
        width: 110px;
        height: 44px;
        border-radius: 6px;
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
        transition: all 0.3s ease;

        &:active {
          transform: scale(0.95);
        }
      }
    }

    .login-btn {
      width: 100%;
      height: 50px;
      border-radius: 25px;
      margin-top: 40px;
      background: linear-gradient(135deg, #2b5876 0%, #4e4376 100%);
      color: white;
      font-size: 18px;
      font-weight: bold;
      border: none;
      box-shadow: 0 5px 15px rgba(78, 67, 118, 0.3);
      transition: all 0.3s ease;

      &:active {
        transform: scale(0.98);
        box-shadow: 0 3px 10px rgba(78, 67, 118, 0.2);
      }
    }
  }

  .footer {
    padding: 16px 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin-top: auto;

    .footer-text {
      color: #999;
      font-size: 12px;
      margin-bottom: 4px;
    }
  }

  @keyframes logoFadeIn {
    from {
      opacity: 0;
      transform: translate(-50%, -60%) scale(0.8);
    }
    to {
      opacity: 1;
      transform: translate(-50%, -50%) scale(1);
    }
  }

  @keyframes logoFloat {
    0%,
    100% {
      transform: translateY(0);
    }
    50% {
      transform: translateY(-8px);
    }
  }

  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
}
