import { View, Text, Image, Input, Button } from '@tarojs/components';
import { useState, useEffect } from 'react';
import Taro from '@tarojs/taro';
import CryptoES from 'crypto-es';
import api from '@/api';
import { saveDataToLocalStorage, TOKENBASENAME } from '@/utils';
import Logo from '@/assets/img/logo.png';
import { encodeFuc } from '@/utils/tools';
import './index.less';

// 常量定义
const bladePatchca = 'bladePatchca';
const BasicCode = 'password_auth_mode:123456';
const passwordCode = 'password';
const sysLoginCaptchaEnable = 'sys_login_captcha_enable';

// 定义响应类型
interface ApiResponse {
  code?: number;
  data?: any;
  message?: string;
}

const Login = () => {
  // 状态管理
  const [username, setUsername] = useState<string>('');
  const [password, setPassword] = useState<string>('');
  const [captchaCode, setCaptchaCode] = useState<string>('');
  const [captchaData, setCaptchaData] = useState<{
    originalImageBase64?: string;
    token?: string;
  }>({});
  const [loginConfig, setLoginConfig] = useState<Record<string, string>>({
    [sysLoginCaptchaEnable]: '1',
  });
  const [loading, setLoading] = useState<boolean>(false);

  // 获取登录配置
  const getLoginConfig = () => {
    api.ampLoginApis.getLoginCfgUsingGet().then((res: ApiResponse) => {
      if (res?.code === 200) {
        saveDataToLocalStorage(`Base-loginCfg`, res.data ?? {}, true);
        setLoginConfig(res.data ?? {});
      }
    });
  };

  // 获取验证码
  const getCaptcha = () => {
    setCaptchaCode('');
    api.ampLoginApis
      .getUsingPost({
        captchaType: bladePatchca,
      })
      .then((res: ApiResponse) => {
        console.log('res', res);

        if (res?.code === 200) {
          setCaptchaData(res?.data ?? {});
        }
      });
  };

  // 登录处理
  const handleLogin = (values: any) => {
    setLoading(true);
    api.ampLoginApis
      .apiOauthTokenCreate(
        {
          ...values,
          grant_type: passwordCode,
          password: encodeFuc(values.password),
        },
        {
          headers: {
            Authorization: `Basic ${CryptoES.enc.Base64.stringify(
              CryptoES.enc.Utf8.parse(BasicCode),
            )}`,
          },
        },
      )
      .then((res: any) => {
        if (res?.access_token) {
          saveDataToLocalStorage(TOKENBASENAME, res?.access_token ?? '');
          Taro.showToast({
            title: '登录成功',
            icon: 'success',
            duration: 2000,
          });

          // 登录成功后跳转到首页
          setTimeout(() => {
            Taro.reLaunch({
              url: '/pages/index/index',
            });
          }, 2000);
          return;
        }
        getCaptcha();
        Taro.showToast({
          title: res?.message ?? '登录失败',
          icon: 'none',
          duration: 2000,
        });
      })
      .finally(() => {
        setLoading(false);
      });
  };

  // 验证码校验
  const handleCheckCaptcha = (values: any) => {
    const captchaVal = {
      captchaVerification: values.captchaVerification,
      token: captchaData.token,
      captchaType: bladePatchca,
    };

    api.ampLoginApis.checkUsingPost(captchaVal).then((res: ApiResponse) => {
      if (res?.code === 200) {
        handleLogin(Object.assign(captchaVal, values));
        return;
      }
      getCaptcha();
    });
  };

  // 提交登录表单
  const onSubmit = () => {
    if (!username) {
      Taro.showToast({
        title: '请输入用户名',
        icon: 'none',
        duration: 2000,
      });
      return;
    }

    if (!password) {
      Taro.showToast({
        title: '请输入密码',
        icon: 'none',
        duration: 2000,
      });
      return;
    }

    if (loginConfig[sysLoginCaptchaEnable] === '1' && !captchaCode) {
      Taro.showToast({
        title: '请输入验证码',
        icon: 'none',
        duration: 2000,
      });
      return;
    }

    const values = {
      username,
      password,
      captchaVerification: captchaCode,
    };

    loginConfig[sysLoginCaptchaEnable] === '1'
      ? handleCheckCaptcha(values)
      : handleLogin(values);
  };

  // 初始化加载
  useEffect(() => {
    getLoginConfig();
    getCaptcha();
  }, []);

  return (
    <View className="login-page">
      <View className="login-banner">
        <View className="logo-container">
          <Image className="banner-logo" src={Logo} mode="aspectFit" />
        </View>
      </View>

      <View className="login-form">
        <View className="form-title">欢迎登录</View>

        <View className="input-item flex items-center">
          <Input
            className="input"
            type="text"
            placeholder="请输入用户名"
            value={username}
            onInput={e => setUsername(e.detail.value)}
          />
        </View>

        <View className="input-item flex items-center">
          <Input
            className="input"
            type="text"
            password
            placeholder="请输入密码"
            value={password}
            onInput={e => setPassword(e.detail.value)}
          />
        </View>

        {loginConfig?.[sysLoginCaptchaEnable] === '1' && (
          <View className="input-item flex items-center">
            <Input
              className="input"
              type="text"
              placeholder="请输入验证码"
              value={captchaCode}
              onInput={e => setCaptchaCode(e.detail.value)}
            />
            {captchaData?.originalImageBase64 ? (
              <Image
                onClick={() => getCaptcha()}
                className="captcha-img"
                src={`data:image/png;base64,${captchaData.originalImageBase64}`}
              />
            ) : null}
          </View>
        )}

        <View style={{ flex: 1 }} />

        <Button
          className="login-btn flex justify-center items-center"
          loading={loading}
          onClick={onSubmit}
        >
          登录
        </Button>

        <View className="footer">
          <Text className="footer-text">Copyright © 2025 西藏机场集团</Text>
          <Text className="footer-text">All Rights Reserved</Text>
        </View>
      </View>
    </View>
  );
};

export default Login;
