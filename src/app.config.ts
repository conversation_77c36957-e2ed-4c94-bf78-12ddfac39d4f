export default defineAppConfig({
  // entryPagePath: 'subpackagesHt/pages/home/<USER>',
  //主包
  pages: [
    'pages/index/index',
    'pages/login/index',
    'pages/webViewOne/index',
    'pages/my/index',
  ],
  subpackages: [
    //高舱
    {
      root: 'subpackagesHt',
      pages: [
        'pages/entry/index',
        'pages/home/<USER>',
        'pages/me/index',
        'pages/product/employee-card-list/index',
        'pages/product/employee-card-list/detail/index',
        'pages/product/employee-card-list/passenger-info/index',
        'pages/product/employee-card-list/confirm/index',
        'pages/product/lounge-list/passenger-info/index',
        'pages/payment/index',
        'pages/payment/result/index',
        'pages/verification/index',
        'pages/verification/code-input/index',
        'pages/verification/code-input/order-select/index',
        'pages/verification/code-input/confirm/index',
        'pages/verification/code-input/success/index',
        'pages/me/employee-card/index',
        'pages/me/coupon/index',
        'pages/me/coupon/barcode/index',
        'pages/me/complete-info/index',
        'pages/me/record/index',
        'pages/me/profile/index',
        'pages/me/change-password/index',
        'pages/order/index',
        'pages/order/detail/index',
        'pages/verification/employee-card-verification/index',
      ],
    },
    //锂电池
    {
      root: 'subpackagesSqcli',
      pages: [
        'pages/authRedirect/authRedirect',
        'pages/entry/entry',
        'pages/index/index',
        'pages/login/login',
        'pages/user-center/user-center',
        'pages/update-password/update-password',
        'pages/user-info/user-info',
        'pages/battery-details/battery-details',
        'pages/create/create',
        'pages/update-userInfo/update-userInfo',
        'pages/webview-page/webview-page',
        'pages/sign/sign',
      ],
    },
  ],
  window: {
    backgroundTextStyle: 'light',
    navigationBarBackgroundColor: '#fff',
    navigationBarTitleText: '拉萨机场运行管理平台',
    navigationBarTextStyle: 'black',
  },
  tabBar: {
    color: '#999',
    selectedColor: '#396ff7',
    backgroundColor: '#fff',
    borderStyle: 'black',
    list: [
      {
        pagePath: 'pages/index/index',
        text: '首页',
      },
      {
        pagePath: 'pages/my/index',
        text: '我的',
      },
    ],
  },
});
