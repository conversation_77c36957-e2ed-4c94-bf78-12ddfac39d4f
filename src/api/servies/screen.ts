/* eslint-disable */
/* tslint:disable */
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

export interface ModelsAppName {
  /** 平台名称 */
  name?: string;
  /** 平台简写 */
  value?: string;
}

export interface ModelsBlockChainData {
  /** 最近交易流水 */
  latest_transaction?: ModelsTransaction[];
  /** 账本列表 */
  ledgers?: ModelsLedger[];
  /** 网络状态 */
  network_status?: string;
  /** 总区块高度 */
  total_block_height?: number;
  /** 总交易数 */
  total_transaction?: number;
  /** 24小时总交易数 */
  transaction_in_24_hours?: number;
  /** 最近7小时的交易数据，按小时统计 */
  transaction_in_7_hours?: ModelsTransactionStats[];
}

export interface ModelsBlockInfo {
  /** 区块hash */
  hash?: string;
  /** 区块高度 */
  height?: number;
  /** 提案组织 */
  org?: string;
  /** 区块创建时间 */
  time?: string;
  /** 当前块交易 */
  txInfoDetails?: ModelsTxInfoDetail[];
  /** 承载交易数 */
  txNum?: number;
}

export interface ModelsChainInfo {
  /** 合约数 */
  ccNum?: number;
  /** 证书数 */
  certNum?: number;
  /** 账本数 */
  channelNum?: number;
  /** 运行周期 */
  cycle?: number;
  /** 共识节点 */
  ordererNum?: number;
  /** 组织数 */
  orgNum?: number;
  /** 同步节点 */
  peerNum?: number;
  /** 平台名称 */
  platformName?: string;
  /** 租户 */
  tenantNum?: number;
}

export interface ModelsChannel {
  /** 引擎 */
  engine?: string;
  /** 账本名称 */
  name?: string;
}

export interface ModelsChannelInfo {
  /** 区块高度 */
  blockHeight?: number;
  /** 最新区块 */
  blockInfos?: ModelsBlockInfo[];
  /** 最新7天交易数 */
  daysTxCount?: ModelsTxCountDay[];
  /** 近一小时交易数 */
  hourTxCount?: number;
  /** 总节点数 */
  nodeNum?: number;
  /** 组织数 */
  orgNum?: number;
  /** 累计交易数 */
  txCount?: number;
  /** 最新交易 */
  txInfoDetails?: ModelsTxInfoDetail[];
}

export interface ModelsCity {
  /** 城市经纬度 */
  location?: ModelsCityLocation;
  /** 城市名称 */
  name?: string;
}

export interface ModelsCityLocation {
  /** 纬度 */
  latitude?: number;
  /** 经度 */
  longitude?: number;
}

export interface ModelsErrorResponse {
  /** 错误码 */
  code?: number;
  /** 错误信息 */
  message?: string;
}

export interface ModelsHourHeight {
  /** 块高 */
  height?: number;
  time?: string;
}

export interface ModelsHourTxCount {
  time?: string;
  /** 小时交易数 */
  txCount?: number;
}

export interface ModelsIndustry {
  /** 行业网络数 */
  blockchain_network?: number;
  /** 行业名称 */
  name?: string;
}

export interface ModelsLedger {
  /** 账本代码 */
  code?: string;
  /** 账本名称 */
  name?: string;
  /** 网络状态 */
  network_status?: string;
  /** 总块高 */
  total_block_height?: number;
  /** 总交易数 */
  total_transaction?: number;
}

export interface ModelsPlatform {
  /** 块高 */
  blockHigh?: number;
  /** 7小时块高 */
  height7Hours?: ModelsHourHeight[];
  /** 平台名称 */
  name?: string;
  /** 网络状态 */
  status?: string;
  /** 平台交易数 */
  txCount?: number;
  /** 7小时交易数 */
  txCount7Hours?: number;
  /** 7小时交易数 */
  txCounts7Hours?: ModelsHourTxCount[];
  /** 交易流水 */
  txFlows?: ModelsTxFlow[];
}

export interface ModelsPlatformChannel {
  /** 指定平台账本 */
  channels?: ModelsChannel[];
}

export interface ModelsRqBlockList {
  /** 账本 */
  channelName?: string;
  /** 引擎 */
  engine?: string;
  /** 页号 */
  page?: number;
  /** 大小 */
  size?: number;
}

export interface ModelsRqTxIdOrHeight {
  /** 账本 */
  channelName?: string;
  /** 引擎 */
  engine?: string;
  /** 请求内容 */
  id?: string;
}

export interface ModelsRsBlockList {
  /** 数据 */
  data?: ModelsBlockInfo[];
  /** 页号 */
  page?: number;
  /** 大小 */
  size?: number;
  /** 总数 */
  total?: number;
  /** 总页数 */
  totalPage?: number;
}

export interface ModelsRsTxIdOrHeight {
  /** 块信息 */
  blockInfo?: ModelsBlockInfo;
  /** 交易信息 */
  txInfoDetail?: ModelsTxInfoDetail;
  /** tx or block */
  type?: string;
}

export interface ModelsSummaryData {
  /** 区块链网络数 */
  blockchain_network?: number;
  /** 部署节点城市 */
  cities?: ModelsCity[];
  /** 共识节点数 */
  consensus?: number;
  /** 行业网络信息 */
  industries?: ModelsIndustry[];
  /** 安全运行天数 */
  security_day?: number;
  /** 同步节点数 */
  sync_node?: number;
  /** 平台租户数 */
  tenant?: number;
}

export interface ModelsTotalTxInfo {
  /** 总交易数 */
  totalTxCount?: number;
  /** 24小时交易数 */
  txCount24Hours?: number;
}

export interface ModelsTransaction {
  /** 区块哈希 */
  block_hash?: string;
  /** 区块高度 */
  block_height?: number;
  /** 交易号 */
  tx_id?: string;
}

export interface ModelsTransactionStats {
  /** 时间 */
  timestamp?: string;
  /** 交易量 */
  value?: number;
}

export interface ModelsTxCountDay {
  /** 交易数 */
  count?: number;
  /** 时间 */
  time?: string;
}

export interface ModelsTxFlow {
  /** 区块hash */
  blockHash?: string;
  /** 提案组织 */
  proposalOrg?: string;
  /** 交易时间 */
  time?: string;
  /** 交易hash */
  txHash?: string;
}

export interface ModelsTxInfoDetail {
  /** 合约方法名 */
  action?: string;
  /** 块hash */
  blockHash?: string;
  /** 执行合约 */
  ccName?: string;
  /** 块高 */
  height?: number;
  /** 发起者 */
  org?: string;
  /** 交易创建时间 */
  time?: string;
  /** 交易id */
  txId?: string;
}

import request from '../apiConfig';

export enum ContentType {
  Json = 'application/json',
  FormData = 'multipart/form-data',
  UrlEncoded = 'application/x-www-form-urlencoded',
  Text = 'text/plain',
}
export type QueryParamsType = Record<string | number, any>;

export interface FullRequestParams
  extends Omit<any, 'data' | 'params' | 'url' | 'responseType'> {
  /** set parameter to `true` for call `securityWorker` for this request */
  secure?: boolean;
  /** request path */
  path: string;
  /** content type of request body */
  type?: ContentType;
  /** query params */
  query?: QueryParamsType;
  /** format of response (i.e. response.json() -> format: "json") */
  format?: ResponseType;
  /** request body */
  body?: unknown;
}

export type RequestParams = Omit<
  FullRequestParams,
  'body' | 'method' | 'query' | 'path'
>;

/**
 * @title 大屏数据服务
 * @version v1.0
 * @baseUrl /big/screen
 * @contact
 *
 * 为大屏汇总及提供各种数据
 */
export class Api {
  allChain = {
    /**
     * @description 统计平台数据
     *
     * @tags 大屏V1
     * @name AllChainDetail
     * @summary 统计平台数据
     * @request GET:/allChain/{name}
     */
    allChainDetail: (name: string, params: RequestParams = {}) =>
      request<ModelsChainInfo, ModelsErrorResponse>({
        path: `/allChain/${name}`,
        method: 'GET',
        type: ContentType.Json,
        ...params,
      }),
  };
  allTxInfo = {
    /**
     * @description 统计平台交易数据
     *
     * @tags 大屏V1
     * @name AllTxInfoDetail
     * @summary 统计平台交易数据
     * @request GET:/allTxInfo/{name}
     */
    allTxInfoDetail: (name: string, params: RequestParams = {}) =>
      request<ModelsPlatform, ModelsErrorResponse>({
        path: `/allTxInfo/${name}`,
        method: 'GET',
        type: ContentType.Json,
        ...params,
      }),
  };
  blockList = {
    /**
     * @description 获取块列表
     *
     * @tags 大屏V1
     * @name BlockListCreate
     * @summary 获取块列表
     * @request POST:/blockList
     */
    blockListCreate: (object: ModelsRqBlockList, params: RequestParams = {}) =>
      request<ModelsRsBlockList, ModelsErrorResponse>({
        path: `/blockList`,
        method: 'POST',
        body: object,
        type: ContentType.Json,
        ...params,
      }),
  };
  blockchain = {
    /**
     * @description 包含总交易数，24小时交易数，总块疝，近7小时交易数及细节，最近交易数据（区块高度，块链哈希，交易号）
     *
     * @tags 大屏V1
     * @name BlockchainList
     * @summary 获取平台区块链信息
     * @request GET:/blockchain
     */
    blockchainList: (params: RequestParams = {}) =>
      request<ModelsBlockChainData, ModelsErrorResponse>({
        path: `/blockchain`,
        method: 'GET',
        type: ContentType.Json,
        format: 'json',
        ...params,
      }),
  };
  channel = {
    /**
     * @description 获取账本数据
     *
     * @tags 大屏V1
     * @name ChannelDetail
     * @summary 获取账本数据
     * @request GET:/channel/{name}
     */
    channelDetail: (name: string, params: RequestParams = {}) =>
      request<ModelsChannelInfo, ModelsErrorResponse>({
        path: `/channel/${name}`,
        method: 'GET',
        type: ContentType.Json,
        ...params,
      }),
  };
  platform = {
    /**
     * @description 获取平台
     *
     * @tags 大屏V1
     * @name PlatformList
     * @summary 获取平台
     * @request GET:/platform
     */
    platformList: (params: RequestParams = {}) =>
      request<ModelsAppName[], ModelsErrorResponse>({
        path: `/platform`,
        method: 'GET',
        type: ContentType.Json,
        ...params,
      }),

    /**
     * @description 获取指定平台的账本数据
     *
     * @tags 大屏V1
     * @name PlatformDetail
     * @summary 获取指定平台的账本数据
     * @request GET:/platform/{name}
     */
    platformDetail: (name: string, params: RequestParams = {}) =>
      request<ModelsPlatformChannel, ModelsErrorResponse>({
        path: `/platform/${name}`,
        method: 'GET',
        type: ContentType.Json,
        ...params,
      }),
  };
  summary = {
    /**
     * @description 包含区块链网络数、共识节点数、运行安全天数、租房数、同步节点数，行业信息，节点地理信息
     *
     * @tags 大屏V1
     * @name SummaryList
     * @summary 获取一般统计数据
     * @request GET:/summary
     */
    summaryList: (params: RequestParams = {}) =>
      request<ModelsSummaryData, ModelsErrorResponse>({
        path: `/summary`,
        method: 'GET',
        type: ContentType.Json,
        format: 'json',
        ...params,
      }),
  };
  totalTxInfo = {
    /**
     * @description 统计总交易、24小时数据
     *
     * @tags 大屏V1
     * @name TotalTxInfoList
     * @summary 统计总交易、24小时数据
     * @request GET:/totalTxInfo
     */
    totalTxInfoList: (params: RequestParams = {}) =>
      request<ModelsTotalTxInfo, ModelsErrorResponse>({
        path: `/totalTxInfo`,
        method: 'GET',
        type: ContentType.Json,
        ...params,
      }),
  };
  txIdOrHeight = {
    /**
     * @description 通过块高或交易id搜索
     *
     * @tags 大屏V1
     * @name TxIdOrHeightCreate
     * @summary 通过块高或交易id搜索
     * @request POST:/txIdOrHeight
     */
    txIdOrHeightCreate: (
      object: ModelsRqTxIdOrHeight,
      params: RequestParams = {},
    ) =>
      request<ModelsRsTxIdOrHeight[], ModelsErrorResponse>({
        path: `/txIdOrHeight`,
        method: 'POST',
        body: object,
        type: ContentType.Json,
        ...params,
      }),
  };
}
