/* eslint-disable */
/* tslint:disable */
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

/**
 * RoleGroupVO
 * 带有父角色信息的角色对象
 */
export interface RoleGroupVO {
  /**
   * id
   * @format int64
   */
  id?: number;
  /** 父角色名称 */
  parentName?: string;
  /** 角色名称 */
  roleName?: string;
}

/**
 * JobPositionVO
 * 岗位信息表
 */
export interface JobPositionVO {
  /**
   * id
   * @format int64
   */
  id?: number;
  /** 岗位代码 */
  positionCode?: string;
  /** 岗位名称 */
  positionName?: string;
  /**
   * 岗位类型
   * @format int32
   */
  positionType?: number;
  /** 岗位描述 */
  remark?: string;
}

/**
 * RoleVO
 * 角色列表展示数据对象
 */
export interface RoleVO {
  /** 角色的描述信息 */
  description?: string;
  /**
   * id
   * @format int64
   */
  id?: number;
  /** 角色的名称，必须唯一 */
  name?: string;
  /**
   * 父角色ID
   * @format int64
   */
  parentId?: number;
  /** 父角色名称 */
  parentName?: string;
  /**
   * 父角色状态【1:启用, 0:停用, 2:锁定, 3:过期, 4:未启用】
   * @format int32
   */
  parentStatus?: 0 | 1 | 2 | 3 | 4;
  /** 从根节点到当前节点的路径;不包含当前节点，例如：/1/2/3 */
  path?: string;
  /**
   * 角色的状态，例如启用，停用等【1:启用, 0:停用, 2:锁定, 3:过期, 4:未启用】
   * @format int32
   */
  status?: 0 | 1 | 2 | 3 | 4;
}

/**
 * OrganizationVO
 * 组织机构，包括根、公司、部门等等
 */
export interface OrganizationVO {
  /**
   * 机构的地址
   * @minLength 0
   * @maxLength 32
   */
  address?: string;
  /**
   * 机构所从事的业务的类别
   * @format int64
   */
  businessType?: number;
  /** 机构子节点 */
  child?: OrganizationVO[];
  /**
   * 机构的编码，例如PSC
   * @minLength 0
   * @maxLength 8
   */
  code?: string;
  /**
   * id
   * @format int64
   */
  id?: number;
  /**
   * 机构所属的行业
   * @format int64
   */
  industryType?: number;
  /**
   * 机构的简介
   * @minLength 0
   * @maxLength 65
   */
  introduction?: string;
  /**
   * 组织机构的全称
   * @minLength 0
   * @maxLength 21
   */
  name?: string;
  /**
   * 机构的英文名称
   * @minLength 0
   * @maxLength 64
   */
  nameEn?: string;
  /** name全路径 */
  nameFullPath?: string;
  /**
   * 父机构ID
   * @format int64
   */
  parentId?: number;
  /**
   * 人数
   * @format int32
   */
  peopleNum?: number;
  /**
   * 机构的负责人
   * @minLength 0
   * @maxLength 20
   */
  personInCharge?: string;
  /**
   * 机构的电话
   * @minLength 0
   * @maxLength 20
   */
  phone?: string;
  /**
   * 机构的代码，由代码生成
   * @minLength 0
   * @maxLength 32
   */
  serialNo?: string;
  /**
   * 机构名称的简称
   * @minLength 0
   * @maxLength 16
   */
  shortName?: string;
  /**
   * 排序号
   * @format int32
   */
  sortOrder?: number;
  /**
   * 钉钉同步状态(0-未同步、1-同步成功、2-同步失败、3-已从钉钉删除)
   * @format int32
   */
  syncStatus?: number;
  /**
   * 机构的类型
   * @format int32
   */
  type?: number;
}

/** CaptchaVO */
export interface CaptchaVO {
  /** @format int32 */
  captchaFontSize?: number;
  captchaFontType?: string;
  captchaId?: string;
  captchaOriginalPath?: string;
  captchaType?: string;
  captchaVerification?: string;
  jigsawImageBase64?: string;
  originalImageBase64?: string;
  point?: PointVO;
  pointJson?: string;
  pointList?: Point[];
  projectCode?: string;
  result?: boolean;
  secretKey?: string;
  token?: string;
  wordList?: string[];
}

/** Point */
export interface Point {
  /** @format int32 */
  x?: number;
  /** @format int32 */
  y?: number;
}

/** PointVO */
export interface PointVO {
  secretKey?: string;
  /** @format int32 */
  x?: number;
  /** @format int32 */
  y?: number;
}

/**
 * LabelValueCandidateVO
 * 系统中标签的可选值
 */
export interface LabelValueCandidateVO {
  /**
   * id
   * @format int64
   */
  id?: number;
  /** 标签值的显示名称 */
  name?: string;
  /** 用户是否有该标签值 */
  selected?: boolean;
  /** 可选值 */
  value?: string;
}

/**
 * LabelVO
 * 系统中可用的标签项
 */
export interface LabelVO {
  /**
   * id
   * @format int64
   */
  id?: number;
  /** 标签所有选项 */
  labelValues?: LabelValueCandidateVO[];
  /** 标签的名称 */
  name?: string;
  /** 用户是否有该标签 */
  selected?: boolean;
  /**
   * 标签项的类型，是单选标签还是多选标签
   * @format int32
   */
  type?: number;
}

/** BaseResultOfobject */
export interface BaseResultOfobject {
  /** @format int32 */
  code?: number;
  data?: object;
  message?: string;
}

/** UserDetailVO */
export interface UserDetailVO {
  /** 用户的头像图片url */
  avatar?: string;
  /** 人员的电子邮件地址 */
  emailAddress?: string;
  /** 人员编码 */
  employeeCode?: string;
  /**
   * 人员的唯一标识，没有业务含义
   * @format int64
   */
  employeeId?: number;
  /** 人员姓名 */
  employeeName?: string;
  /**
   * 人员的性别，1表示男性，2表示女性
   * @format int32
   */
  gender?: number;
  /**
   * id
   * @format int64
   */
  id?: number;
  /** 身份证号 */
  idCard?: string;
  /** 人员工号 */
  jobNumber?: string;
  /** 岗位代码(多个逗号分隔) */
  jobPosition?: string;
  /** 所有可用标签，用户有的用selected=true表示 */
  labels?: LabelVO[];
  manageOrgs?: OrganizationVO[];
  /** 用户管理的角色 */
  manageRoleIds?: number[];
  /** 用户管理角色对象集 */
  manageRoles?: RoleVO[];
  /** 用户名，可用于登录系统，必须唯一。 */
  name?: string;
  /** 归属机构全路径 */
  nameFullPath?: string;
  /**
   * 归属机构ID
   * @format int64
   */
  organizationId?: number;
  /** 归属机构 */
  organizationName?: string;
  /**
   * 归属机构ID【0:集团, 1:公司, 2:部门】
   * @format int32
   */
  organizationType?: 0 | 1 | 2;
  /**
   * 用户的密码状态，1标识是初始状态，需要修改密码【1:初始化, 2:已修改】
   * @format int32
   */
  passwordStatus?: 1 | 2;
  /** 从根节点到当前节点的路径(不包含当前节点)， 例如(/r-1/c-13/d-25).  由代码自动生成，不展示给用户。用于后端查询或者生成树的时候使用 */
  path?: string;
  /** 人员的电话 */
  phone?: string;
  /** 岗位集合 */
  positionList?: JobPositionVO[];
  /** 备注 */
  remark?: string;
  /** 用户被授予的资源(CA专用) */
  resources?: number[];
  role?: RoleVO[];
  /** 用户的角色 */
  roles?: RoleGroupVO[];
  /**
   * 用户的状态，例如0：停用，1：启用，2：锁定，3：过期等【1:启用, 0:停用, 2:锁定, 3:过期, 4:未启用】
   * @format int32
   */
  status?: 0 | 1 | 2 | 3 | 4;
  /**
   * 用户的类型，-1-系统管理员，1-企业管理员，2-普通管理员，3-普通用户，4-机构管理员【-1:系统管理员, 1:企业管理员, 2:普通管理员, 3:普通用户, 4:机构管理员】
   * @format int32
   */
  type?: -1 | 1 | 2 | 3 | 4;
  /** 用户管理的机构 */
  userManageOrganization?: number[];
  /** 用户为哪些机构工作 */
  userWorkForOrganization?: number[];
  workOrgs?: OrganizationVO[];
  /**
   * null【1:上班, 2:值班, 3:休假】
   * @format int32
   */
  workStatus?: 1 | 2 | 3;
  /** 工作航站(多个逗号分隔) */
  workTerminal?: string;
  /** 微信OpenId */
  wxOpenid?: string;
}

/**
 * EmployeeVO
 * 系统所管理的人员
 */
export interface EmployeeVO {
  /**
   * 所属机构的id
   * @format int64
   */
  belongToOrg: number;
  /**
   * 人员的出生日期
   * @format date
   */
  birthday?: string;
  /** 钉钉账号 */
  dingtalk?: string;
  /** 人员的电子邮件地址 */
  emailAddress?: string;
  /** 员工的编号 */
  employeeCode?: string;
  /**
   * 人员的性别，1表示男性，2表示女性
   * @format int32
   */
  gender: number;
  /** 人员的毕业院校 */
  graduateFrom?: string;
  /**
   * id
   * @format int64
   */
  id?: number;
  /** 身份证号 */
  idCard?: string;
  /** 人员工号 */
  jobNumber?: string;
  /** 人员的岗位 */
  jobPosition?: string;
  /** 员工的职务 */
  jobTitle?: string;
  /** 人员的姓名 */
  name: string;
  /** 所属机构名称 */
  organizationName?: string;
  /** 人员的电话 */
  phone?: string;
  /** 人员头像的地址 */
  photoRul?: string;
  /** 人员的政治面貌 */
  politicalStatus?: string;
  /** 岗位名称 */
  positionName?: string;
  /** 岗位名称集合 */
  positionNameList?: string[];
  /**
   * 人员当前的状态，例如在职、离职、停职等
   * @format int32
   */
  status?: number;
  /**
   * 钉钉同步状态(0-未同步、1-同步成功、2-同步失败、3-已从钉钉删除)
   * @format int32
   */
  syncStatus?: number;
  /** 工作航站(多个逗号分隔) */
  workTerminal?: string;
  /** 工作场站名称 */
  workTerminalList?: string[];
  /** 微信OpenId */
  wxOpenid?: string;
}

/**
 * UserCenterTenantVO
 * 用户中心租户admin账号信息
 */
export interface UserCenterTenantVO {
  /** 公司名称 */
  companyName?: string;
  /** 租户联系人的姓名 */
  contactor?: string;
  /** 租户联系人的邮箱 */
  contactorEmail?: string;
  /** 租户联系人的电话 */
  contactorPhone?: string;
  /** 人员信息 */
  emp?: EmployeeVO;
  /**
   * 产品许可剩余天数
   * @format int64
   */
  remainingNumberOfDays?: number;
  /** 租户编码 */
  tenantCode?: string;
  /**
   * 租户ID
   * @format int64
   */
  tenantId?: number;
  /** 租户名称 */
  tenantName?: string;
  /**
   * 租户状态
   * @format int32
   */
  tenantStatus?: number;
  /**
   * 用户的类型，-1-系统管理员，1-企业管理员，2-普通管理员，3-普通用户，4-机构管理员【-1:系统管理员, 1:企业管理员, 2:普通管理员, 3:普通用户, 4:机构管理员】
   * @format int32
   */
  type?: -1 | 1 | 2 | 3 | 4;
  /** 系统用户 */
  user?: UserDetailVO;
  /**
   * 租户租约有效期的起始日期
   * @format date
   */
  validFrom?: string;
  /**
   * 租户租约有效期的结束日期
   * @format date
   */
  validTo?: string;
}

/**
 * FeatureResourceTreeVO
 * 系统资源树返回展示对象
 */
export interface FeatureResourceTreeVO {
  /** 子节点 */
  children?: FeatureResourceTreeVO[];
  /** 资源是否被选中的状态，区别于资源是否启用的状态和是否被删除的状态 */
  flag?: boolean;
  /** 资源图标的url */
  icon?: string;
  /**
   * 资源id
   * @format int64
   */
  id?: number;
  /** 是否是公共资源(是否可以被选中) */
  isPublic?: boolean;
  /** 资源的名称 */
  name?: string;
  /**
   * 父资源id
   * @format int64
   */
  parentId?: number;
  /** 控件名称 */
  perms?: string;
  /** 资源的前端路由地址 */
  routeUrl?: string;
  /**
   * 资源的排序，代表兄弟资源之间显示的先后顺序
   * @format int32
   */
  sortOrder?: number;
  /** 子系统编码 */
  subsystemCode?: string;
  /**
   * 资源的类型，包括1:子系统、2：菜单、3：操作【1:子系统, 2:菜单, 3:操作】
   * @format int32
   */
  type?: 1 | 2 | 3;
}

/**
 * AuthMenuVO
 * 获取登录用户菜单返回对象
 */
export interface AuthMenuVO {
  /** 有权限的菜单树 */
  menu?: FeatureResourceTreeVO[];
  /** 有权限的控件 */
  perms?: Record<string, string[]>;
}

/** DictDataCacheVO */
export type DictDataCacheVO = object;

/** BaseResultOfUserCenterTenantVO */
export interface BaseResultOfUserCenterTenantVO {
  /** @format int32 */
  code?: number;
  /** 用户中心租户admin账号信息 */
  data?: UserCenterTenantVO;
  message?: string;
}

/** BaseResultOfAuthMenuVO */
export interface BaseResultOfAuthMenuVO {
  /** @format int32 */
  code?: number;
  /** 获取登录用户菜单返回对象 */
  data?: AuthMenuVO;
  message?: string;
}

/** BaseResultOfMapOfstringAndobject */
export interface BaseResultOfMapOfstringAndobject {
  /** @format int32 */
  code?: number;
  data?: object;
  message?: string;
}

/** BaseResultOfMapOfstringAndListOfDictDataCacheVO */
export interface BaseResultOfMapOfstringAndListOfDictDataCacheVO {
  /** @format int32 */
  code?: number;
  data?: Record<string, DictDataCacheVO[]>;
  message?: string;
}

/** BaseResultOfstring */
export interface BaseResultOfstring {
  /** @format int32 */
  code?: number;
  data?: string;
  message?: string;
}

import request from '../apiConfig';

export enum ContentType {
  Json = 'application/json',
  FormData = 'multipart/form-data',
  UrlEncoded = 'application/x-www-form-urlencoded',
  Text = 'text/plain',
}
export type QueryParamsType = Record<string | number, any>;

export interface FullRequestParams
  extends Omit<any, 'data' | 'params' | 'url' | 'responseType'> {
  /** set parameter to `true` for call `securityWorker` for this request */
  secure?: boolean;
  /** request path */
  path: string;
  /** content type of request body */
  type?: ContentType;
  /** query params */
  query?: QueryParamsType;
  /** format of response (i.e. response.json() -> format: "json") */
  format?: ResponseType;
  /** request body */
  body?: unknown;
}

export type RequestParams = Omit<
  FullRequestParams,
  'body' | 'method' | 'query' | 'path'
>;

/**
 * @title 机场平台小程序轻量接口
 * @version 1.0.0
 */
export class Api {
  uc = {
    /**
     * @description 必填参数captchaType、token、captchaVerification
     *
     * @tags 验证码接口
     * @name CheckUsingPost
     * @summary 验证码校验接口
     * @request POST:/uc/api/captcha/check
     */
    checkUsingPost: (data: CaptchaVO, params: RequestParams = {}) =>
      request<BaseResultOfobject, void>({
        path: `/uc/api/captcha/check`,
        method: 'POST',
        body: data,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * @description 必填参数captchaType，值可选范围【clickWord-点选验证码, blockPuzzle-滑块验证码, bladePatchca-图片字符验证码】
     *
     * @tags 验证码接口
     * @name GetUsingPost
     * @summary 验证码图片获取接口
     * @request POST:/uc/api/captcha/get
     */
    getUsingPost: (data: CaptchaVO, params: RequestParams = {}) =>
      request<BaseResultOfobject, void>({
        path: `/uc/api/captcha/get`,
        method: 'POST',
        body: data,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 参数配置服务接口
     * @name GetLoginCfgUsingGet
     * @summary getLoginCfg
     * @request GET:/uc/api/login/get_login_cfg
     */
    getLoginCfgUsingGet: (params: RequestParams = {}) =>
      request<BaseResultOfMapOfstringAndobject, void>({
        path: `/uc/api/login/get_login_cfg`,
        method: 'GET',
        ...params,
      }),

    /**
     * No description
     *
     * @tags 参数配置服务接口
     * @name GetSyncModeUsingGet
     * @summary getSyncMode
     * @request GET:/uc/api/login/get_sync_mode
     */
    getSyncModeUsingGet: (params: RequestParams = {}) =>
      request<BaseResultOfstring, void>({
        path: `/uc/api/login/get_sync_mode`,
        method: 'GET',
        ...params,
      }),

    /**
     * No description
     *
     * @tags 字典公共访问接口
     * @name GetAllUsingGet1
     * @summary 加载全部数据字典
     * @request GET:/uc/api/common/dict/get_all
     */
    getAllUsingGet1: (params: RequestParams = {}) =>
      request<BaseResultOfMapOfstringAndListOfDictDataCacheVO, void>({
        path: `/uc/api/common/dict/get_all`,
        method: 'GET',
        ...params,
      }),

    /**
     * No description
     *
     * @tags 系统用户、租户用户登录登出接口
     * @name LogoutUsingGet
     * @summary 退出登录接口
     * @request GET:/uc/api/auth/logout
     */
    logoutUsingGet: (params: RequestParams = {}) =>
      request<BaseResultOfstring, void>({
        path: `/uc/api/auth/logout`,
        method: 'GET',
        ...params,
      }),

    /**
     * @description subSystemCode为子系统编号，多个用英文逗号分隔
     *
     * @tags 系统用户、租户用户登录登出接口
     * @name MenuUsingGet
     * @summary 获取当前用户菜单
     * @request GET:/uc/api/auth/menu
     */
    menuUsingGet: (
      query?: {
        /** subSystemCode */
        subSystemCode?: string;
      },
      params: RequestParams = {},
    ) =>
      request<BaseResultOfAuthMenuVO, void>({
        path: `/uc/api/auth/menu`,
        method: 'GET',
        query: query,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 用户中心接口
     * @name GetUserInfoUsingGet
     * @summary 获取当前用户个人信息
     * @request GET:/uc/api/userCenter/getUserInfo
     */
    getUserInfoUsingGet: (params: RequestParams = {}) =>
      request<BaseResultOfUserCenterTenantVO, void>({
        path: `/uc/api/userCenter/getUserInfo`,
        method: 'GET',
        ...params,
      }),

    /**
     * @description 用于本地调试用或自动化测试时登录
     *
     * @name ApiOauthTokenCreate
     * @summary 登录接口
     * @request POST:/uc/api/oauth/token
     */
    apiOauthTokenCreate: (
      data: {
        /**
         * 用户名
         * @example "lxa_admin"
         */
        username?: string;
        /**
         * 密码
         * @example "m93Nlc/i6CXH1TuMcxRQNQ=="
         */
        password?: string;
        /**
         * 固定password
         * @example "password"
         */
        grant_type?: string;
      },
      params: RequestParams = {},
    ) =>
      request<
        {
          /** 登录token */
          access_token: string;
          /**
           * token类型
           * bearer
           */
          token_type: string;
          /** 刷新token */
          refresh_token: string;
          /**
           * 有效期
           * 单位秒
           */
          expires_in: number;
          token_user: {
            /** 用户id */
            id: string;
            /** 登录token */
            token: string;
            /** 登录时间 */
            loginTime: string;
            /**
             * 登录过期时间戳
             * 毫秒时间戳
             */
            expireTime: string;
            /** 登录ip */
            ipaddr: string;
            mac: null;
            loginLocation: string;
            /** 登录浏览器 */
            browser: string;
            /** 浏览器id */
            browserId: number;
            /** 操作系统 */
            os: string;
            dt: string;
          };
        },
        any
      >({
        path: `/uc/api/oauth/token`,
        method: 'POST',
        body: data,
        type: ContentType.UrlEncoded,
        format: 'json',
        ...params,
      }),
  };
}
