// request.ts
import { TOKENBASENAME } from '@/utils';
import { loginOut } from '@/utils/tools';
import Taro from '@tarojs/taro';

interface RequestOptions {
  path: string;
  method?: keyof Taro.request.Method;
  type?: any;
  body?: any;
  query?: any;
  format?: any;
}

const BASE_URL = process.env.TARO_APP_API; // 替换成你的API基础地址

// 假设你的token存储在本地存储中
const getToken = () => {
  return { Authorization: `Bearer ${Taro.getStorageSync(TOKENBASENAME)}` }; // 携带token}
};

// eslint-disable-next-line @typescript-eslint/no-unused-vars
const request = async <T = any, _E = any>(
  options: RequestOptions & {
    isloading?: boolean;
  },
): Promise<T> => {
  // 设置请求默认值
  const {
    path,
    method = 'GET',
    body,
    query,
    type,
    isloading = false,
    ...params
  } = options;

  try {
    // 请求拦截器
    isloading && Taro.showLoading({ title: '加载中...' });

    // 发起请求
    const response = await Taro.request({
      url: BASE_URL + path, // 请求地址
      method: method, // 请求方法
      data: body || query, // 发送的数据
      header: {
        'content-type': type || 'application/json', // 默认头部
        ...getToken(),
        ...((params as any)?.headers ?? {}), // 自定义头部
      },
      ...params,
    });

    isloading && Taro.hideLoading();

    // 处理返回
    if (response.statusCode >= 200 && response.statusCode < 300) {
      if (response?.data?.code !== 200) {
        Taro.showToast({
          title: response?.data?.message || '',
          icon: 'none',
          duration: 2000,
        });
      }
      // 请求成功
      return response.data;
    } else {
      // 显示错误提示
      // 处理错误
      handleError(response);
      return response?.data ?? {};
    }
  } catch (error) {
    // 错误处理
    handleError(error);
    return error;
  }
};

const handleError = (error: any) => {
  // 统一处理错误日志
  console.log('请求错误:', error);

  let errorMessage = '请求失败，请稍后重试';
  let shouldReturn = false;

  // 处理HTTP状态码错误
  if (error && error.statusCode) {
    // 根据不同状态码给出不同提示
    switch (error.statusCode) {
      case 401:
        errorMessage = '登录已过期，请重新登录';
        shouldReturn = true;
        break;
      case 403:
        errorMessage = '没有操作权限';
        break;
      case 404:
        errorMessage = '请求的资源不存在';
        break;
      case 500:
        errorMessage = '服务器错误，请联系管理员';
        break;
      default:
        // 如果服务端返回了错误信息，优先使用服务端的错误信息
        if (error.data && (error.data.message || error.data.msg)) {
          errorMessage = error.data.message || error.data.msg;
        } else {
          errorMessage = `请求错误(${error.statusCode})`;
        }
    }
  } else if (error instanceof Error) {
    // 处理JS原生Error对象
    errorMessage = error.message || errorMessage;
  } else if (typeof error === 'string') {
    // 处理字符串类型的错误
    errorMessage = error;
  } else if (error && error.errMsg) {
    // 处理小程序API可能返回的错误格式
    errorMessage = error.errMsg;
  }

  // 显示错误提示
  Taro.showToast({
    title: errorMessage,
    icon: 'none',
    duration: 2000,
  });

  // 对于401错误，执行登出操作
  if (shouldReturn) {
    loginOut();
    return;
  }

  throw new Error(errorMessage);
};

export default request;
