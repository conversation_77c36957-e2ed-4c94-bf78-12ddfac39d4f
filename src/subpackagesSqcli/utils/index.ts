import Taro from '@tarojs/taro';
import CryptoES from 'crypto-es';
import { fetchUploadFileImage } from '@/subpackagesSqcli/api';
import { LI_TYPE } from '@/utils';
import { TOKENBASENAME } from './contanst';

export * from './contanst';
export * from './eventEmit';

export const setStorageSync = (key: string, value: any) => {
  Taro.setStorageSync(`${LI_TYPE}-${key}`, value);
};

export const getStorageSync = (key: string) => {
  return Taro.getStorageSync(`${LI_TYPE}-${key}`);
};

function isEmpty(value) {
  switch (Object.prototype.toString.call(value)) {
    case '[object Undefined]':
      return value === void 0;
    case '[object Null]':
      return value === null;
    case '[object Number]':
      return Number.isNaN(value);
    case '[object String]':
      return value === '';
    case '[object Boolean]':
      return false;
    case '[object Object]':
      return Object.keys(value).length === 0;
    case '[object Array]':
      return value.length === 0;
    default:
      return false;
  }
}

/**
 * 设置路由参数，适用于对象
 * 对象转换为加密的json字符串
 * @param params
 */
export const setUrlParams = (params: any) => {
  if (params && !isEmpty(params)) {
    return CryptoES.enc.Utf8.parse(JSON.stringify(params)).toString(
      CryptoES.enc.Base64,
    );
  }
  return '';
};

/**
 * 获取路由参数，适用于对象
 * @param params
 */
export const getUrlParams = (params: string | undefined) => {
  if (params) {
    return JSON.parse(
      CryptoES.enc.Base64.parse(params).toString(CryptoES.enc.Utf8),
    );
  }
};
export const transferListToMap = (
  dataList: any[],
  key = 'dictValue',
  value = 'dictLabel',
) => {
  const list = (dataList || []).map(ele => [ele[key], ele[value]]);
  return Object.fromEntries(list);
};

export const writeBinaryToTempFile = (uploadRes): Promise<string> => {
  return new Promise((resolve, reject) => {
    const fileTempPath = `${Taro.env.USER_DATA_PATH}/temp_${Date.now()}.png`;
    const fs = Taro.getFileSystemManager();
    fs.writeFile({
      filePath: fileTempPath,
      data: uploadRes,
      encoding: 'binary',
      success: () => {
        resolve(fileTempPath);
      },
      fail: err => {
        console.error('文件写入失败:', err);
        Taro.showToast({ title: '预览失败', icon: 'none' });
        reject(err);
      },
    });
  });
};

export const handleUploadImage = async (
  fileId: string,
): Promise<{ url: string; fileId: string }> => {
  const uploadRes = await fetchUploadFileImage(fileId);
  const fileTempPath = await writeBinaryToTempFile(uploadRes);
  return {
    url: fileTempPath,
    fileId,
  };
};

export const upLoadAvatar = async (filePath: string) => {
  return new Promise((resolve, reject) => {
    Taro.showLoading({
      title: '上传中...', // 加载提示的文本
    });
    Taro.uploadFile({
      url: `${process.env.TARO_APP_SQCLI_API}/sys/file/uploadTwo`,
      filePath: filePath,
      name: 'file', // 后端接收文件的字段名
      header: {
        Authorization: `Bearer ${getStorageSync(TOKENBASENAME)}`,
        terminalType: 'Manager',
        terminalId: '577',
        terminalSystem: 'miniProgram',
      },
      formData: {
        // 其他需要传递的表单数据
      },
      success: res => {
        const data = JSON.parse(res.data);
        if (data.code === 0) {
          // 上传成功提示
          Taro.showToast({
            title: '上传成功',
            icon: 'success',
            duration: 2000,
          });
          handleUploadImage(data.data)
            .then(({ url, fileId }) => {
              resolve({
                ...data,
                upload: { url, fileId },
              });
            })
            .catch(reject);
        } else {
          Taro.showToast({
            title: data.msg || '上传失败',
            icon: 'none',
            duration: 2000,
          });
          reject(data);
        }
      },
      fail: error => {
        Taro.showToast({
          title: '上传失败',
          icon: 'none',
          duration: 2000,
        });
        reject(error); // 处理上传失败
      },
      complete: () => {
        Taro.hideLoading();
      },
    });
  });
};

// 添加一个处理图片预览的函数
export const handleImagePreview = (url: string, images?: any[]) => {
  Taro.previewImage({
    current: url, // 当前显示图片的url
    urls: images || [url], // 需要预览的图片链接列表
  });
};

export const onOversize = () => {
  Taro.showToast({
    title: '文件大小不能超过5M',
    icon: 'none',
    duration: 2000,
  });
};

export const beforeUpload = async (files: File[]) => {
  console.log('beforeUpload', files);
};
