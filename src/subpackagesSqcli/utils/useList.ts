import { useEffect, useState } from 'react';
import Taro, {
  useDidHide,
  useDidShow,
  usePullDownRefresh,
  useReachBottom,
} from '@tarojs/taro';
import { getStorageSync } from '.';

const useList = (
  listReq,
  pageSize = 10,
  searchData = {},
  loadMore = true, //是否加载更多
  isOnload = true, // 是否在页面加载时就请求数据
) => {
  const [list, setList] = useState<any>(null);
  const [pageNumber, setPageNumber] = useState(1);
  const [totalPage, setTotalPage] = useState(0);
  const [totalRecords, setTotalRecords] = useState(0);
  const [refreshHasMore, setRefreshHasMore] = useState(true);

  // 获取列表数据
  const getList = (curPage, search = {}) => {
    return listReq({
      current: curPage,
      size: pageSize,
      ...search,
    })
      .then(res => {
        // 当前页不为1则是加载更多，需要拼接数据
        if (curPage === 1) {
          setList(res.data || []);
        } else {
          if (res.data && res.data.length > 0) {
            setList([...list, ...res.data]);
          }
        }
        setTotalPage(res.totalPages);
        setTotalRecords(res.totalRecords);
        setRefreshHasMore(curPage < res.totalPages);
        return res;
      })
      .finally(info => {
        Taro.stopPullDownRefresh();
        return info;
      });
  };

  // 初始化列表，回到第一页数据
  const initList = (search = {}, page = 1) => {
    const curPage = page;
    setPageNumber(curPage);
    return getList(curPage, search);
  };

  // 搜索
  const onSearch = (search, page = 1) => {
    initList(search, page);
  };

  useEffect(() => {
    if (isOnload) {
      initList(searchData);
    }
  }, []);

  useDidShow(() => {
    // 是否刷新页面数据
    const isOnloadInPageShow = getStorageSync('isfresh');
    if (isOnloadInPageShow) {
      initList(searchData);
    }
  });

  useDidHide(() => {
    Taro.removeStorageSync('isfresh');
  });

  // 上拉加载
  useReachBottom(() => {
    if (loadMore) {
      if (pageNumber >= totalPage) {
        Taro.showToast({
          title: '我是有底线的',
          icon: 'none',
        });
        return;
      }
      const newPageNumber = pageNumber + 1;
      setPageNumber(newPageNumber);
      getList(newPageNumber, searchData);
    }
  });

  // 下拉刷新
  usePullDownRefresh(() => {
    if (loadMore) {
      initList(searchData);
    }
  });

  // 自定义上拉加载，下拉刷新

  const refresh = () => {
    return initList(searchData);
  };
  const refreshLoadMore = () => {
    if (pageNumber >= totalPage) {
      setRefreshHasMore(false);
      return Promise.resolve({});
    }
    const newPageNumber = pageNumber + 1;
    setPageNumber(newPageNumber);
    return getList(newPageNumber, searchData);
  };

  return {
    list,
    getList,
    onSearch,
    setList,
    pageNumber,
    refresh,
    refreshLoadMore,
    refreshHasMore,
    totalRecords,
  };
};

export default useList;
