// request.ts
import {
  getStorageSync,
  setStorageSync,
  TOKENBASENAME,
} from '@/subpackagesSqcli/utils';
import Taro from '@tarojs/taro';
import { apiIsSuccess } from '@/subpackagesSqcli/api/servies/api';
import { timeoutInterceptor } from './interceptor';

Taro.addInterceptor(timeoutInterceptor);

interface RequestOptions<T> {
  url: string;
  method?: keyof Taro.request.Method;
  data?: T;
  header?: any;
  loading?: boolean;
  // 是否需要自己处理错误提示 默认是自己处理 如果想要拦截器拦截 传false
  isOwnHandleError?: boolean;
  isStayCookies?: boolean; // 是否保持会话
  responseType?: any;
  isFile?: boolean;
}

const BASE_URL = process.env.TARO_APP_SQCLI_API; // 替换成你的API基础地址
// const BASE_URL = 'https://tips-test.iprd.sw';
// const BASE_URL = 'https://tips-develop.iprd.sw';

// 假设你的token存储在本地存储中
const getToken = () => {
  const defaultConfig = {
    terminalType: 'Manager',
    terminalId: '577',
    terminalSystem: 'miniProgram',
  };
  if (!getStorageSync(TOKENBASENAME)) {
    return defaultConfig;
  }
  return {
    Authorization: `Bearer ${getStorageSync(TOKENBASENAME)}`,
    ...defaultConfig,
  }; // 携带token
};

const request = async <T = any, _E = any>(
  options: RequestOptions<_E>,
): Promise<T> => {
  // 设置请求默认值
  const {
    url,
    method = 'GET',
    data,
    header,
    loading = true,
    isStayCookies = false,
    isOwnHandleError = false,
    isFile = false,
    ...requestOpt
  } = options;
  try {
    if (loading) {
      // 请求拦截器
      Taro.showLoading({ title: '加载中...' });
    }
    // 发起请求
    const response = await Taro.request({
      url: BASE_URL + url, // 请求地址
      method: method, // 请求方法
      data: data, // 发送的数据
      header: {
        'content-type': 'application/json', // 默认头部
        ...getToken(),
        ...header, // 自定义头部
      },
      ...requestOpt,
    });
    if (loading) {
      Taro.hideLoading();
    }
    // 处理返回
    if (response.statusCode >= 200 && response.statusCode < 300) {
      // 是否保持会话
      if (isStayCookies) {
        setStorageSync('COOKIE', response.cookies?.[0]);
      }
      if (isOwnHandleError) {
        //是否自己处理错误  默认自己处理错误
        return response.data;
      } else {
        //拦截器处理错误
        if (isFile) {
          if (response.data) {
            return response.data;
          } else {
            handleError(response);
            return response?.data ?? {};
          }
        }
        if (apiIsSuccess(response.data.code)) {
          return response.data;
        } else {
          handleError(response);
          return response?.data ?? {};
        }
      }
      // 请求成功
    } else {
      // 处理错误
      handleError(response);
      return response?.data ?? {};
    }
  } catch (error) {
    // 错误处理
    // handleError(error);
    return error;
  }
};

const handleError = (error: any) => {
  let errorMessage = '请求失败，请稍后重试';
  if (error && error.data) {
    // 根据实际API返回的错误信息进行调整
    errorMessage = error.data.msg;
  }
  if (error.statusCode !== 403 || error.data.code !== 0) {
    Taro.showToast({
      title: errorMessage,
      icon: 'none',
      duration: 2000,
    });
  }
  if (
    error.statusCode === 401 ||
    error.data.code === 401 ||
    error.data.code === 403
  ) {
    Taro.redirectTo({ url: '/subpackagesSqcli/pages/login/login' });
    Taro.clearStorage();
  }
  throw new Error(errorMessage);
};

export default request;
