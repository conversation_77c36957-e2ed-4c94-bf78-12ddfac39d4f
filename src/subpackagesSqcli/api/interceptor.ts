import Taro, { Chain } from '@tarojs/taro';

const TIMEOUT = 60000;

export function timeoutInterceptor(chain: Chain) {
  const requestParams = chain.requestParams;
  let p;
  const res: any = new Promise((resolve, reject) => {
    let timeout: null | NodeJS.Timeout = setTimeout(
      () => {
        timeout = null;
        Taro.hideLoading();
        Taro.showToast({
          title: '网络请求超时,请稍后再试！',
          icon: 'none',
          mask: true,
        });
        reject(new Error('网络链接超时,请稍后再试！'));
      },
      (requestParams && requestParams.timeout) || TIMEOUT,
    );

    p = chain.proceed(requestParams);
    // eslint-disable-next-line @typescript-eslint/no-shadow
    p.then((res: unknown) => {
      if (!timeout) return;
      clearTimeout(timeout);
      resolve(res);
    }).catch((err: any) => {
      if (!err.code && !err.status) {
        // 避免failed的接口一直加载loading
        Taro.hideLoading();
      }
      timeout && clearTimeout(timeout);
      reject(err);
    });
  });

  if (p !== undefined && typeof p.abort === 'function') res.abort = p.abort;
  return res;
}
