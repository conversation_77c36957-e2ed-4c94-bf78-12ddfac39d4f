import CryptoES from 'crypto-es';
import Taro from '@tarojs/taro';
import request from '../apiConfig';

export const apiIsSuccess = code => [100000, 200, 0].includes(code);

// 登录
export const fetchLogin = (loginForm: any) => {
  const hash = CryptoES.SHA256(loginForm.password);
  const params = {
    username: loginForm.username,
    password: hash.toString(CryptoES.enc.Base64),
    verCode: loginForm.captchaVerification,
    randomKey: loginForm.randomKey,
  };
  const formData = Object.keys(params)
    .map(key => {
      return `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`;
    })
    .join('&');
  return request({
    url: '/auth/login',
    method: 'POST',
    data: formData,
    header: {
      terminalid: 577,
      terminalsystem: 'miniProgram',
      terminaltype: 'Manager',
      'content-type': 'application/x-www-form-urlencoded',
    },
    isOwnHandleError: true,
  });
};
// 获取验证码
export const fetchVerCode = (randomKey: number) => {
  return request({
    url: `/auth/verCode/getVerCode?h=30&w=105&randomKey=${randomKey}`,
    method: 'GET',
    loading: false,
  });
};
export const getUserInfo = () => {
  return request({ url: '/sys/user/getUserInfo', method: 'GET' });
};
// 退出登录
export const fetchLogout = () => {
  return request({
    url: `/auth/logout`,
    method: 'GET',
    data: {},
  });
};
// 修改密码
export const fetchUpdatePassword = (params: any) => {
  return request({
    url: '/sys/user/modifyPassword',
    method: 'POST',
    data: params,
  });
};
// 修改手机号
export const fetchUpdatePhone = (params: any) => {
  return request({
    url: '/sys/user/modifyPhoneNumber',
    method: 'POST',
    data: params,
  });
};

export const fetchAccountManageMenu = () => {
  return request({ url: '/sys/dept/getAirlineList', method: 'GET', data: {} });
};
export const fetchUploadAvatar = params => {
  return request({
    url: '/sys/file/upload',
    method: 'POST',
    data: params,
    header: { 'Content-Type': 'multipart/form-data' },
  });
};
// 获取图片地址
export const fetchUploadFileImage = (id: string) => {
  return request({
    url: `/sys/file/download/${id}`,
    method: 'GET',
    responseType: 'arraybuffer',
    isFile: true,
  });
};

export const getBatteryManageList = (params: any) => {
  return request({
    url: '/sys/batteryApp/page',
    method: 'POST',
    data: params,
  });
};

export const getDictDataByIdentity = (identity: string) => {
  return request({
    url: `/sys/sysDict/getAirlineDictValues?identity=${identity}`,
    method: 'GET',
    data: {},
  });
};

export const addBattery = (params: any) => {
  return request({
    url: '/sys/batteryApp/addBatteryApp',
    method: 'POST',
    data: params,
  });
};
export const editBattery = (params: any) => {
  return request({
    url: '/sys/batteryApp/updateBatteryApp',
    method: 'POST',
    data: params,
  });
};
export const getBatteryInfoById = (id: string) => {
  return request({
    url: `/sys/batteryApp/get?id=${id}`,
    method: 'GET',
    data: {},
  });
};
// 获取是否是提交人或者航司待办人
export const getSubmitor = () => {
  return request({
    url: '/sys/batteryApp/isSubmitter',
    method: 'GET',
    data: {},
  });
};
// 删除申报单
export const deleteBattery = (id: number) => {
  return request({ url: `/batteryApp/delete?id=${id}`, method: 'DELETE' });
};
// 获取申报单审核记录
export const getAuditRecords = (id: number) => {
  return request({
    url: `/sys/batteryApp/getAuditList?id=${id}`,
    method: 'GET',
  });
};
// 审核申报单
export const auditBattery = (params: any) => {
  return request({
    url: '/sys/batteryApp/audit',
    method: 'POST',
    data: params,
  });
};

export const fetchImageToBase64 = (url: string) => {
  return request({
    url,
    method: 'GET',
    responseType: 'arraybuffer',
  });
};

export const SqcliLogin = () => {
  return request({
    url: '/auth/login',
    method: 'POST',
    data: {
      loginFrom: 'UC',
      ucToken: Taro.getStorageSync('sqcli-minApp-authorization'),
    },
    header: {
      'content-type': 'application/x-www-form-urlencoded',
    },
    isOwnHandleError: true,
    loading: false,
  });
};
