.create {
  .nut-cell {
    padding: 13px 0;
  }
  .nut-cell-group-wrap {
    background: transparent;
  }
  
  .form-item {
    padding: 0 12px;
    border-radius: 6px;
    margin-bottom: 12px;
    background: #FFFFFF;
    .nut-form-item-label {
      padding-left: 0;
    }
    &.has-rules {
      .nut-form-item-label {
        padding-left: 12px;
      }
      .nut-form-item-body-tips {
        text-align: right !important;
      }
    }
  }
  .nut-form-item {
    height: 46px;
  }
  textarea {
    height: 68px;
  }
  .has-label {
    .nut-form-item-body-slots {
      display: flex;
      justify-content: flex-end;
      text-align: right;
    }
    .nut-input .nut-input-native {
      text-align: right !important;
    }
  }
  .upload-btn {
    width: 20px;
    height: 20px;
    border-radius: 100%;
    text-align: center;
    line-height: 20px;
    background: #3FBA5C;
  }
  .nut-uploader-preview {
    margin-right: 0;
  }
}