import NavHeader from '@/subpackagesSqcli/components/NavHeader';
import {
  Cell,
  Divider,
  Form,
  TextArea,
  Uploader,
  Picker,
  Input,
  FileItem,
  DatePicker,
  PickerOption,
  Radio,
  Button,
} from '@nutui/nutui-react-taro';
import { Text, View, Image } from '@tarojs/components';
import FootOpt from '@/subpackagesSqcli/components/FootOpt';
import Taro from '@tarojs/taro';
import { useEffect, useState } from 'react';
import {
  addBattery,
  editBattery,
  fetchAccountManageMenu,
  getBatteryInfoById,
} from '@/subpackagesSqcli/api';
import {
  handleImagePreview,
  handleUploadImage,
  onOversize,
  upLoadAvatar,
  maxFileSize,
  SIGN_WAY,
  setStorageSync,
} from '@/subpackagesSqcli/utils';
import dayjs from 'dayjs';
import Sign from '@/subpackagesSqcli/components/Sign';
import SignaturePreview from '@/subpackagesSqcli/components/SignaturePreview';
import './create.less';

definePageConfig({
  navigationStyle: 'custom',
  enableShareAppMessage: true,
  enablePullDownRefresh: true,
});

const cellTheme: any = {
  '--nutui-cell-divider-border-bottom': '0',
};
const RadioGroupTheme: any = {
  '--nutui-radio-icon-font-size': '14px',
};

const Create = () => {
  const routerParams: any = Taro?.getCurrentInstance()?.router?.params;
  const { id } = routerParams || {};
  const [airlineOptions, setAirlineOptions] = useState<
    { value: number; text: string }[]
  >([]);
  const [fileList, setFileList] = useState<{ fileId: string; url: string }[]>(
    [],
  );
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [form] = Form.useForm();
  const [requiredFormData, setRequiredFormData] = useState({
    title: '',
    airlineId: '',
    flightNumber: '',
    flightDate: dayjs().format('YYYY-MM-DD'),
    passenger: '',
    batterySpec: '',
    signUrl: '',
  });
  const [signWay, setSignWay] = useState(SIGN_WAY.SIGN);
  const [uploadSignUrl, setUploadSignUrl] = useState('');
  const [signatureUrl, setSignatureUrl] = useState('');
  const [previewVisible, setPreviewVisible] = useState(false);
  const [visible, setVisible] = useState(false);

  const isFormDataValid = () => {
    return Object.values(requiredFormData).every(value => value !== '');
  };

  const handleFormChange = (key: string, values: any) => {
    setRequiredFormData({ ...requiredFormData, [key]: values });
  };

  const getAirlines = () => {
    fetchAccountManageMenu().then(res => {
      const data = res?.deptDtoList
        ?.find(v => v?.dept?.id === 100)
        ?.deptList?.map(_ => ({ value: _.id, text: _.name }));
      setAirlineOptions(data);
    });
  };

  useEffect(() => {
    // 获取航司下拉框
    getAirlines();
  }, []);

  useEffect(() => {
    if (id) {
      getBatteryInfoById(id).then(res => {
        form.setFieldsValue({
          ...res.data,
          airlineId: [res.data.airlineId],
        });
        const formData = {
          title: res.data.title,
          airlineId: res.data.airlineId,
          flightNumber: res.data.flightNumber,
          flightDate: res.data.flightDate,
          passenger: res.data.passenger,
          batterySpec: res.data.batterySpec,
          signUrl: '',
        };
        if (res.data.signFileId) {
          handleUploadImage(res.data.signFileId).then(signRes => {
            if (res.data.signType === SIGN_WAY.SIGN) {
              setSignatureUrl(signRes.url);
            } else {
              setUploadSignUrl(signRes.url);
            }
            formData.signUrl = signRes.url;
          });
        }
        setRequiredFormData(formData);
        setSignWay(res.data.signType);
        if (res?.data?.fileList?.length) {
          const promises = res.data.fileList.map(fileId =>
            handleUploadImage(fileId),
          );
          Promise.all(promises).then(fileTempPaths => {
            setFileList(fileTempPaths);
          });
        }
      });
    }
  }, [id]);

  // 文件上传
  const handleFileChange = async (uploadFiles: FileItem[]) => {
    const result: any = await upLoadAvatar(
      uploadFiles[uploadFiles.length - 1].path as string,
    );

    if (result.code === 0) {
      fileList.push(result.upload);
      setFileList([...fileList]);
    }
  };

  const handleSubmit = async (type?: 'SAVE' | 'SUBMIT') => {
    await form.submit();
    const values = form.getFieldsValue(true);
    const flightNumberPattern = /^[A-Za-z0-9]{1,8}$/;
    if (!isFormDataValid()) {
      return Taro.showToast({
        title:
          '标题、航司、航班号、航班日期、旅客姓名、电池规格，旅客签名为必填项',
        icon: 'none',
      });
    }
    if (!flightNumberPattern.test(values.flightNumber)) {
      return Taro.showToast({
        title: '航班号必须为8位字符，仅限英文和数字',
        icon: 'none',
      });
    }
    const params: any = {
      ...values,
      fileList: fileList.map(v => v.fileId),
      airlineId: values.airlineId[0],
      type,
    };
    if (values.signUrl) {
      const uploadRes: any = await upLoadAvatar(values.signUrl);
      params.signFileId = uploadRes.upload.fileId;
    }
    delete params.signUrl;
    let res;
    if (id) {
      res = await editBattery({ ...params, id });
    } else {
      res = await addBattery(params);
    }
    if (res.code === 0) {
      Taro.showToast({ title: '提交成功', icon: 'none' });
      setTimeout(() => {
        setStorageSync('isfresh', true);
        setStorageSync('isDetailFresh', true);
        Taro.navigateBack();
      }, 1500);
    } else {
      Taro.showToast({ title: res.msg, icon: 'none' });
    }
  };

  const confirm = async (dataurl: string, isSigned?: boolean) => {
    if (isSigned) {
      const base64 = Taro.getFileSystemManager().readFileSync(
        dataurl,
        'base64',
      );
      const res = 'data:image/jpeg;base64,' + base64;
      form.setFieldsValue({
        signUrl: dataurl,
      });
      setUploadSignUrl(res);
      setSignatureUrl('');
      handleFormChange('signUrl', dataurl);
    } else {
      Taro.showToast({
        title: '请先签字',
        icon: 'none',
      });
    }
  };

  return (
    <NavHeader
      title={id ? '编辑申报单' : '创建申报单'}
      background="#F5F5F5"
      headerStyle={{
        position: 'fixed',
        background: '#F2F3F5',
      }}
      icon={<Text className="ri-close-line" />}
    >
      <View className="create mx-16 my-4" style={{ paddingBottom: '100px' }}>
        <Form
          labelPosition="left"
          form={form}
          initialValues={{
            flightDate: dayjs().format('YYYY-MM-DD'),
            signType: SIGN_WAY.SIGN,
          }}
        >
          <View className="form-item">
            <Form.Item name="title">
              <Input
                placeholder="标题"
                onChange={value => handleFormChange('title', value)}
                maxLength={20}
              />
            </Form.Item>
          </View>

          <View className="form-item">
            <Form.Item
              name="airlineId"
              trigger="onConfirm"
              getValueFromEvent={(...args) => args[1]}
              onClick={(_, ref: any) => {
                ref.open();
              }}
            >
              <Picker
                options={[airlineOptions]}
                onConfirm={(_, value) =>
                  handleFormChange('airlineId', value[0])
                }
              >
                {(value: any) => {
                  return (
                    <Cell
                      style={{
                        padding: 0,
                        ...cellTheme,
                      }}
                      className="nutui-cell--clickable"
                      title={
                        value.length ? (
                          airlineOptions.filter(po => po.value === value[0])[0]
                            ?.text
                        ) : (
                          <Text className="text-hint-color">航司</Text>
                        )
                      }
                      align="center"
                    />
                  );
                }}
              </Picker>
            </Form.Item>
            <Divider className="m-0" />
            <Form.Item name="flightNumber">
              <Input
                placeholder="航班号"
                onChange={value => handleFormChange('flightNumber', value)}
                maxLength={8}
              />
            </Form.Item>
            <Divider className="m-0" />
            <Form.Item name="flightDate" trigger="onConfirm">
              <Input
                placeholder="航班日期"
                readOnly
                onClick={() => setShowDatePicker(true)}
              />
            </Form.Item>
            <Divider className="m-0" />
            <Form.Item name="passenger">
              <Input
                placeholder="旅客姓名"
                onChange={value => handleFormChange('passenger', value)}
                maxLength={20}
              />
            </Form.Item>
          </View>
          <View className="form-item has-label">
            <Form.Item name="batterySpec" label="电池规格">
              <Input
                placeholder="电池规格"
                onChange={value => handleFormChange('batterySpec', value)}
                maxLength={50}
              />
            </Form.Item>
            <Divider className="m-0" />
            <Form.Item name="fileList" label="上传照片（选填）">
              <Uploader
                maxCount="10"
                multiple
                autoUpload={false}
                onChange={handleFileChange}
                camera="back"
                onOversize={onOversize}
                maxFileSize={maxFileSize}
              >
                <View className="flex justify-center items-center gap-1">
                  <Text className="text-hint-color">选择附件</Text>
                  <View className="upload-btn text-white">
                    <Text className="ri-add-fill" />
                  </View>
                </View>
              </Uploader>
            </Form.Item>
            <Divider className="m-0" />
            <View className="flex flex-wrap gap-1 mb-2">
              {fileList.map(image => (
                <span key={image.fileId} style={{ marginRight: '10px' }}>
                  <Image
                    style={{ width: '80px', height: '80px' }}
                    src={image.url}
                    mode="aspectFill"
                    onClick={() =>
                      handleImagePreview(
                        image.url,
                        fileList.map(v => v.url),
                      )
                    }
                  />
                </span>
              ))}
            </View>
            <>
              <Form.Item name="signType" label="旅客签名">
                <Radio.Group
                  direction="horizontal"
                  onChange={(value: any) => setSignWay(value)}
                  style={RadioGroupTheme}
                >
                  <Radio value={SIGN_WAY.SIGN}>手写签名</Radio>
                  <Radio value={SIGN_WAY.UPLOAD}>上传签名图片</Radio>
                </Radio.Group>
              </Form.Item>
              <Form.Item
                name="signUrl"
                style={{ height: 'auto', paddingTop: 0 }}
              >
                {signWay === SIGN_WAY.SIGN ? (
                  <View className="flex-1">
                    <Button
                      type="primary"
                      className="primary-btn"
                      onClick={() => setVisible(true)}
                    >
                      点击签名
                    </Button>
                    {signatureUrl && (
                      <View className="relative mt-2">
                        <Image
                          src={signatureUrl}
                          style={{
                            width: '100px',
                            height: '100px',
                            transform: 'rotate(-90deg)',
                            boxShadow: '0 2px 10px 0 rgba(0, 0, 0, .1)',
                            borderRadius: '6px',
                          }}
                          mode="aspectFill"
                          onClick={() => setPreviewVisible(true)}
                        />
                      </View>
                    )}
                  </View>
                ) : (
                  <View>
                    <Uploader
                      maxCount="1"
                      autoUpload={false}
                      onChange={(files: FileItem[]) =>
                        confirm(files[0].path as string, true)
                      }
                      maxFileSize={maxFileSize}
                      onOversize={onOversize}
                    >
                      <View className="flex justify-center items-center gap-1">
                        <Text className="text-hint-color">上传签名图片</Text>
                        <View className="upload-btn text-white">
                          <Text className="ri-add-fill" />
                        </View>
                      </View>
                    </Uploader>
                    <View>
                      {uploadSignUrl && (
                        <View className="relative mt-2">
                          <Image
                            src={uploadSignUrl}
                            style={{
                              width: '100px',
                              height: '100px',
                              boxShadow: '0 2px 10px 0 rgba(0, 0, 0, .1)',
                              borderRadius: '6px',
                            }}
                            mode="aspectFill"
                            onClick={() => handleImagePreview(uploadSignUrl)}
                          />
                        </View>
                      )}
                    </View>
                  </View>
                )}
              </Form.Item>
            </>
          </View>
          <Form.Item
            name="remark"
            style={{ height: 'auto', paddingLeft: '12px' }}
          >
            <TextArea placeholder="备注（选填）" rows={3} maxLength={500} />
          </Form.Item>
        </Form>
      </View>
      <FootOpt
        isFormBtn
        btns={[
          {
            text: '保存',
            className: 'default-btn medium',
            handle: () => handleSubmit('SAVE'),
            disabled: !isFormDataValid(),
          },
          {
            text: '提交航司代办处理',
            className: 'primary-btn medium',
            handle: () => handleSubmit('SUBMIT'),
            disabled: !isFormDataValid(),
          },
        ]}
      />
      <DatePicker
        visible={showDatePicker}
        onClose={() => setShowDatePicker(false)}
        onConfirm={(options: PickerOption[]) => {
          form.setFieldsValue({
            flightDate: options.map(option => option.text).join('-'),
          });
          handleFormChange(
            'flightDate',
            options.map(option => option.text).join('-'),
          );
        }}
        defaultValue={new Date()}
      />
      <Sign
        visible={visible}
        setVisible={setVisible}
        onConfirm={(_base64, dataUrl) => {
          handleFormChange('signUrl', dataUrl);
          setSignatureUrl(dataUrl);
          setUploadSignUrl('');
          form.setFieldsValue({
            signUrl: dataUrl,
          });
        }}
      />
      <SignaturePreview
        imageUrl={signatureUrl}
        visible={previewVisible}
        onClose={() => setPreviewVisible(false)}
        width="280px"
        height="180px"
        title="您的签名"
      />
    </NavHeader>
  );
};

export default Create;
