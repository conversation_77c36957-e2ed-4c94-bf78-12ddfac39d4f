@import '@/subpackagesSqcli/assets/style/theme.less';

.login {
  font-family: MiSans;

  .logo-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 16px;
    color: #0162e1;
    font-size: @font-size-xl;
    font-weight: 600;
    line-height: 28px;

    .logo {
      height: 42.2px;
      width: 42.2px;
      margin-right: 12px;
    }
  }

  &-form {
    padding: 0 20px;
    margin-top: 60px;
    z-index: 10;
  }

  &-btn {
    margin-top: 70px;
    background: @primary_main;
  }

  .captcha-wrapper {
    position: relative;
  }

  .captcha-bar {
    position: absolute;
    width: 92px;
    height: 38px;
    bottom: 5px;
    right: 4px;
    border-radius: @border-radius-lg;
    z-index: 10;
  }

  .nut-cell-group-wrap {
    background-color: transparent;
  }

  .nut-cell {
    background-color: transparent;
  }

  .nut-input {
    padding: 0 4px;
    height: 48px;
  }

  .form-layout-top .nut-form-item-label {
    color: @text-secondary;
  }
}