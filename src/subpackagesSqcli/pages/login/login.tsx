import { useEffect, useState } from 'react';
import Taro, { useDidShow, useShareAppMessage } from '@tarojs/taro';
import { fetchLogin, fetchVerCode, getUserInfo } from '@/subpackagesSqcli/api';
import { View, Image } from '@tarojs/components';
import { Form, Input, Button } from '@nutui/nutui-react-taro';
import NavHeader from '@/subpackagesSqcli/components/NavHeader';
import { setStorageSync, TOKENBASENAME } from '@/subpackagesSqcli/utils';
import TitleImg from '@/subpackagesSqcli/assets/img/title.png';
import Logo from '@/subpackagesSqcli/assets/img/logo.png';
import './login.less';

definePageConfig({
  navigationStyle: 'custom',
  enableShareAppMessage: true,
  enablePullDownRefresh: true,
});

export default function Login() {
  const [captcha, setCaptcha] = useState('');
  const [captchaVerification, setCaptchaVerification] = useState('');
  const [randomKey, setRandomKey] = useState<number | null>(null);

  // 避免验证码相同创建10位随机数
  useEffect(() => {
    const key = Math.floor(
      (Math.random() + Math.floor(Math.random() * 9 + 1)) *
        Math.pow(10, 10 - 1),
    );
    setRandomKey(key);
  }, []);

  useEffect(() => {
    if (randomKey !== null) {
      getCaptcha(randomKey); // 仅在 randomKey 不为 null 时调用
    }
  }, [randomKey]);

  const [form] = Form.useForm();

  const getCaptcha = key => {
    fetchVerCode(key)
      .then(res => {
        setCaptcha(res?.verCode);
      })
      .catch(error => {
        console.log(error);
      });
  };

  useDidShow(() => {});

  useShareAppMessage(() => {
    return {
      path: '/subpackagesSqcli/pages/index/index',
    };
  });

  const onFinish = async values => {
    const { username, password } = values;
    try {
      const loginRes = await fetchLogin({
        username,
        password,
        captchaVerification,
        randomKey,
      });
      if (loginRes.code === 0) {
        setStorageSync(TOKENBASENAME, loginRes.response.token);
        const userRes = await getUserInfo();
        setStorageSync('USER_INFO', JSON.stringify(userRes));
        // 获取活动json配置
        Taro.reLaunch({ url: '/subpackagesSqcli/pages/index/index' });
      } else {
        Taro.showToast({
          title: loginRes.msg,
          icon: 'none',
          duration: 2000,
        });
        setTimeout(() => {
          const newKey = Math.floor(
            (Math.random() + Math.floor(Math.random() * 9 + 1)) *
              Math.pow(10, 10 - 1),
          );
          setRandomKey(newKey);
        }, 2000);
      }
    } catch (error) {
      console.log('error===', error);
    }
  };
  return (
    <NavHeader
      title="登录"
      contenStyle={{ paddingTop: '0px' }}
      onClick={() => {
        Taro.reLaunch({ url: '/pages/index/index' });
      }}
      background="#F5F5F5"
    >
      <View className="login">
        <View className="header-bg flex justify-center flex-col items-center">
          <View>
            <Image src={Logo} style={{ width: '140px', height: '95px' }} />
          </View>
          <View>
            <Image src={TitleImg} style={{ width: '216px', height: '29px' }} />
          </View>
        </View>
        <View className="login-form">
          <Form
            labelPosition="top"
            footer={
              <Button
                type="primary"
                className="login-btn rounded-lg"
                block
                formType="submit"
                size="large"
                shape="square"
              >
                登录
              </Button>
            }
            onFinish={values => onFinish(values)}
            form={form}
          >
            <Form.Item name="username">
              <Input placeholder="账号" />
            </Form.Item>
            <Form.Item name="password">
              <Input placeholder="密码" type="password" maxLength={20} />
            </Form.Item>
            <Form.Item name="captchaVerification">
              <View className="captcha-wrapper">
                <Input
                  placeholder="验证码"
                  type="text"
                  value={captchaVerification}
                  onChange={value => setCaptchaVerification(value)}
                />
                <Image
                  src={`data:image/png;base64,${captcha}`}
                  className="captcha-bar"
                  onClick={() => {
                    const newKey = Math.floor(
                      (Math.random() + Math.floor(Math.random() * 9 + 1)) *
                        Math.pow(10, 10 - 1),
                    );
                    setRandomKey(newKey);
                  }}
                />
              </View>
            </Form.Item>
          </Form>
        </View>
      </View>
    </NavHeader>
  );
}
