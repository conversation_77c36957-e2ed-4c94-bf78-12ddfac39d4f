import NavHeader from '@/subpackagesSqcli/components/NavHeader';
import { Image, View, Text } from '@tarojs/components';
import LogoEntry from '@/subpackagesSqcli/assets/img/logo_entry.png';
import EntryImg1 from '@/subpackagesSqcli/assets/img/entry_img1.png';
import EntryImg2 from '@/subpackagesSqcli/assets/img/entry_img2.png';
// import EntryImg3 from '@/subpackagesSqcli/assets/img/entry_img3.png';
import Taro from '@tarojs/taro';
import './entry.less';

const Entry = () => {
  // 跳转旅检电池申报
  const toBatteryApply = () => {
    Taro.reLaunch({ url: '/subpackagesSqcli/pages/index/index' });
  };

  // 跳转补偿通
  // const toCompensatory = () => {
  //   const h5Url = `https://lxaap.swcares.com.cn/lfm-h5/login`;
  //   Taro.navigateTo({
  //     url: `/pages/webview-page/webview-page?url=${h5Url}`,
  //   });
  // };

  // 跳转安检质控
  const toQualityControl = () => {
    Taro.navigateToMiniProgram({
      appId: 'wx5bb8b8eab9c7ba17',
      path: '/subpackagesSqcli/pages/login/login',
      success: res => {
        console.log('跳转成功', res);
      },
      fail: err => {
        console.log('跳转失败', err);
      },
    });
  };
  const entryList = [
    {
      title: '旅检锂电池申报',
      subtitle: '用于机场安检与航司进行锂电池运输审批',
      icon: EntryImg1,
      onClick: () => toBatteryApply(),
    },
    {
      title: '安检质控',
      subtitle: '用于机场安检质量控制监控',
      icon: EntryImg2,
      onClick: () => toQualityControl(),
    },
    // {
    //   title: '补偿通',
    //   subtitle: '用于机场地服代航司履行对客的赔付服务',
    //   icon: EntryImg3,
    //   onClick: () => toCompensatory(),
    // },
  ];
  return (
    <NavHeader
      title=""
      contenStyle={{ paddingTop: '0px' }}
      icon={
        <Image
          src={LogoEntry}
          style={{ width: '256px', height: '36px', margin: '4px 0' }}
        />
      }
      background="#D4E6FE"
      onClick={() => {}}
    >
      <View className="header-bg-1">
        <View className="text-center py-4">
          <View className="text-2xl font-semibold">
            <Text>拉萨机场</Text>
            <Text>·</Text>
            <Text className="gradient-text">运行管理平台</Text>
          </View>
          <View className="text-secondary text-sm my-1">
            统一登录、在线协同，助力机场工作人员
          </View>
        </View>
        <View>
          {entryList.map((item, index) => (
            <View
              key={index}
              className="bg-white rounded-xl flex flex-col items-center mt-4 p-4"
              onClick={() => item.onClick()}
            >
              <Image src={item.icon} className="w-20 h-20" />
              <View
                className="text-base font-medium"
                style={{ color: '#1D1F20' }}
              >
                {item.title}
              </View>
              <View className="text-sm text-secondary mt-1">
                {item.subtitle}
              </View>
            </View>
          ))}
        </View>
      </View>
    </NavHeader>
  );
};

export default Entry;
