.auth-redirect {
  min-height: 100vh;
  background: linear-gradient(180deg, #F5F7FA 0%, #FFFFFF 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 32px;

  .auth-content {
    width: 100%;
    text-align: center;
    animation: fadeIn 0.5s ease;
    
    .logo-wrapper {
      animation: pulse 1.2s ease-in-out infinite;

      .logo {
        width: 140px;
        height: 95px;
      }
    }

    .loading-dots {
      display: flex;
      justify-content: center;
      gap: 8px;

      .dot {
        width: 8px;
        height: 8px;
        background-color: #396ff7;
        border-radius: 50%;
        opacity: 0.3;

        &:nth-child(1) {
          animation: dot 1.2s infinite 0s;
        }
        &:nth-child(2) {
          animation: dot 1.2s infinite 0.4s;
        }
        &:nth-child(3) {
          animation: dot 1.2s infinite 0.8s;
        }
      }
    }
  }
}

// Logo 轻微跳动动画
@keyframes pulse {
  0% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-6px);
  }
  100% {
    transform: translateY(0);
  }
}

// 整体淡入动画
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 小点动画
@keyframes dot {
  0%, 100% {
    opacity: 0.3;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.3);
  }
}