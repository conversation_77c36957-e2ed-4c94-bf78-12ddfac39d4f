import { SqcliLogin } from '@/subpackagesSqcli/api';
import { setStorageSync, TOKENBASENAME } from '@/subpackagesSqcli/utils';
import { View, Image } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { useEffect } from 'react';
import Logo from '@/subpackagesSqcli/assets/img/logo.png';
import './authRedirect.less';

const AuthRedirect: React.FC = () => {
  useEffect(() => {
    handleLogin();
  }, []);
  const handleLogin = async () => {
    try {
      const res = await SqcliLogin();
      if (res.code === 0) {
        const { Menu, token, ...rest } = res.response;
        setStorageSync(TOKENBASENAME, res.response.token);
        setStorageSync('USER_INFO', JSON.stringify({ rest }));
        Taro.reLaunch({ url: '/subpackagesSqcli/pages/index/index' });
      } else {
        Taro.showToast({
          title: res.msg,
          icon: 'none',
        });
        setTimeout(() => {
          Taro.reLaunch({ url: '/pages/index/index' });
        }, 1000);
      }
    } catch (error) {
      Taro.showToast({
        title: '登录失败',
        icon: 'none',
      });
      setTimeout(() => {
        Taro.reLaunch({ url: '/pages/index/index' });
      }, 1000);
    }
  };
  return (
    <View className="auth-redirect">
      <View className="auth-content">
        <View className="logo-wrapper">
          <Image src={Logo} className="logo" />
        </View>
        <View className="loading-dots">
          <View className="dot"></View>
          <View className="dot"></View>
          <View className="dot"></View>
        </View>
      </View>
    </View>
  );
};

export default AuthRedirect;
