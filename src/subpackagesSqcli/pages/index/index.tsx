import { View, Text, Image } from '@tarojs/components';
import NavHeader from '@/subpackagesSqcli/components/NavHeader';
import Taro, { useDidHide, useDidShow } from '@tarojs/taro';
import { CSSProperties, useEffect, useState } from 'react';
import {
  getBatteryInfoById,
  getBatteryManageList,
  getDictDataByIdentity,
} from '@/subpackagesSqcli/api';
import {
  Button,
  Calendar,
  Divider,
  Empty,
  InfiniteLoading,
  Input,
  Tabs,
} from '@nutui/nutui-react-taro';
import useList from '@/subpackagesSqcli/utils/useList';
import dayjs from 'dayjs';
import CustomTabBar from '@/subpackagesSqcli/components/CustomTabBar';
import {
  getStorageSync,
  HNANDEL_STATUS,
  transferListToMap,
} from '@/subpackagesSqcli/utils';
import EmptyImg from '@/subpackagesSqcli/assets/img/empty.png';
import Cell from './components/Cell';
import './index.less';

interface TabModel {
  label: string;
  value: string;
}

const inputTheme: any = {
  '--nutui-input-padding': '10px',
  '--nutui-input-border-radius': '6px',
};

const tabTheme: any = {
  '--nutui-tabs-titles-background-color': 'trasparent',
  '--nutui-tabs-titles-font-size': '14px',
  '--nutui-tabs-tab-line-color': '#1872F0',
  '--nutui-tabs-tab-line-width': '100%',
  '--nutui-tabs-tab-line-height': '2px',
  '--nutui-tabs-line-border-radius': '0 0 4px 0',
  '--nutui-tabs-line-bottom': '0',
};
const calendarTheme: any = {
  '--nutui-calendar-active-background-color': '#1872F0',
  '--nutui-calendar-choose-background-color': 'rgba(24, 114, 240, .2)',
  '--nutui-calendar-day67-color': '#000',
  '--nutui-calendar-day-active-border-radius': '6px',
};

const InfiniteUlStyle: CSSProperties = {
  height: '520px',
  width: '100%',
  padding: '0',
  overflowY: 'auto',
  overflowX: 'hidden',
};

const EmptyTheme: any = {
  '--nutui-empty-title-margin-top': '8px',
  '--nutui-empty-padding': '190px 40px',
};
const commonJson = {
  pullingText: '下拉刷新',
  loadingText: '...加载中',
  pullRefresh: true,
};

const tabList: TabModel[] = [
  { label: '全部', value: 'ALL' },
  { label: '审批中', value: 'IN_PROGRESS' },
  { label: '通过', value: 'APPROVED' },
  { label: '不通过', value: 'REJECTED' },
];

export default function Index() {
  const currentDate = dayjs().format('YYYY-MM-DD');
  const [tabvalue, setTabvalue] = useState<string | number>('ALL');
  const [isVisible, setIsVisible] = useState(false);
  const [searchValues, setSearchValues] = useState<{ [key: string]: any }>({
    ALL: {
      createdTimeStart: currentDate,
      createdTimeEnd: currentDate,
      title: '',
      items: [
        {
          asc: false,
          column: 'createdTime',
        },
      ],
    },
    IN_PROGRESS: {
      createdTimeStart: currentDate,
      createdTimeEnd: currentDate,
      title: '',
      items: [
        {
          asc: false,
          column: 'createdTime',
        },
      ],
      statusList: [
        HNANDEL_STATUS.AGENT_HANDLE,
        HNANDEL_STATUS.SUBMITTER_HANDLE,
      ],
    },
    APPROVED: {
      createdTimeStart: currentDate,
      createdTimeEnd: currentDate,
      title: '',
      items: [
        {
          asc: false,
          column: 'createdTime',
        },
      ],
      statusList: [HNANDEL_STATUS.APPROVED],
    },
    REJECTED: {
      createdTimeStart: currentDate,
      createdTimeEnd: currentDate,
      title: '',
      items: [
        {
          asc: false,
          column: 'createdTime',
        },
      ],
      statusList: [HNANDEL_STATUS.REJECT],
    },
  });
  const [statusMap, setStatusMap] = useState<any>({});
  const [calendarTypes, setCalendarTypes] = useState<{
    [key: string]: 'single' | 'range';
  }>({
    ALL: 'single',
    IN_PROGRESS: 'single',
    APPROVED: 'single',
    REJECTED: 'single',
  });

  const {
    list: allList,
    setList: setAllList,
    refreshHasMore: allHasMore,
    refreshLoadMore: allOnLoadMore,
    refresh: allOnRefresh,
    onSearch: allOnSearch,
  } = useList(getBatteryManageList, 10, searchValues.ALL);

  const {
    list: inProgressList,
    setList: setInProgressList,
    refreshHasMore: inProgressHasMore,
    refreshLoadMore: inProgressOnLoadMore,
    refresh: inProgressOnRefresh,
    onSearch: inProgressOnSearch,
  } = useList(getBatteryManageList, 10, searchValues.IN_PROGRESS);

  const {
    list: approvedList,
    setList: setApprovedList,
    refreshHasMore: approvedHasMore,
    refreshLoadMore: approvedOnLoadMore,
    refresh: approvedOnRefresh,
    onSearch: approvedOnSearch,
  } = useList(getBatteryManageList, 10, {
    ...searchValues.APPROVED,
  });
  const {
    list: rejectedList,
    setList: setRejectedList,
    refreshHasMore: rejecteHasMore,
    refreshLoadMore: rejecteOnLoadMore,
    refresh: rejecteOnRefresh,
    onSearch: rejecteOnSearch,
  } = useList(getBatteryManageList, 10, {
    ...searchValues.REJECTED,
  });

  useDidShow(() => {
    const batteryId = getStorageSync('batteryId');
    if (batteryId) {
      // 需要更新某条数据的状态
      // 获取申报单最新的状态
      getBatteryInfoById(batteryId).then(res => {
        const { status } = res.data;

        // 确定 batteryId 所在的数组并更新状态
        const updateStatusInList = list => {
          const index = list.findIndex(item => item.id === Number(batteryId));
          if (index !== -1) {
            list[index].status = status; // 更新状态
            return [...list];
          }
          return list;
        };

        if (allList.some(item => item.id === Number(batteryId))) {
          const updatedAllList = updateStatusInList(allList);
          setAllList(updatedAllList);
        } else if (inProgressList.some(item => item.id === Number(batteryId))) {
          const updatedInProgressList = updateStatusInList(inProgressList);
          setInProgressList(updatedInProgressList);
        } else if (approvedList.some(item => item.id === Number(batteryId))) {
          const updatedApprovedList = updateStatusInList(approvedList);
          setApprovedList(updatedApprovedList);
        } else if (rejectedList.some(item => item.id === Number(batteryId))) {
          const updatedRejectedList = updateStatusInList(rejectedList);
          setRejectedList(updatedRejectedList);
        }
      });
    }
  });

  useDidHide(() => {
    Taro.removeStorageSync('batteryId');
  });

  // 请求状态下拉
  const getStatus = () => {
    getDictDataByIdentity('BATTERY_APP_STATUS').then(res => {
      const data = transferListToMap(res?.dictValues, 'name', 'value');
      setStatusMap(data);
    });
  };

  useEffect(() => {
    getStatus();
  }, []);

  const handleOnSearch = (search: any) => {
    setSearchValues(prev => ({
      ...prev,
      [tabvalue]: search, // 更新当前标签的搜索条件
    }));
    switch (tabvalue) {
      case 'ALL':
        allOnSearch(search);
        break;
      case 'IN_PROGRESS':
        inProgressOnSearch(search);
        break;
      case 'APPROVED':
        approvedOnSearch(search);
        break;
      case 'REJECTED':
        rejecteOnSearch(search);
        break;
    }
  };

  const renderHeaderButtons = () => {
    return (
      <View className="calendar-btns bg-background-color h-8 flex items-center justify-center gap-4">
        <View
          className={`text-sqcli-hint-color ${
            calendarTypes[tabvalue] === 'single' ? 'active' : ''
          }`}
          onClick={() =>
            setCalendarTypes(prev => ({
              ...prev,
              [tabvalue]: 'single' as any,
            }))
          }
        >
          选择日期
        </View>
        <View
          className={`text-sqcli-hint-color ${
            calendarTypes[tabvalue] === 'range' ? 'active' : ''
          }`}
          onClick={() =>
            setCalendarTypes(prev => ({
              ...prev,
              [tabvalue]: 'range' as 'single' | 'range',
            }))
          }
        >
          选择时间范围
        </View>
      </View>
    );
  };

  const renderCalendarText = (createdTimeStart, createdTimeEnd) => {
    return (
      <View className="bg-gray-100 h-12 flex items-center justify-center">
        <View onClick={() => setIsVisible(true)}>
          {calendarTypes[tabvalue] === 'single'
            ? createdTimeStart
            : `${createdTimeStart} - ${createdTimeEnd}`}
          <Text className="ri-arrow-down-s-fill" />
        </View>
      </View>
    );
  };

  const getTitleColor = (status: string) => {
    switch (status) {
      case HNANDEL_STATUS.DRAFT:
        return { color: '#737578' };
      case HNANDEL_STATUS.APPROVED:
        return { color: '#3FBA5C' };
      case HNANDEL_STATUS.REJECT:
        return { color: '#F3564B' };
      case HNANDEL_STATUS.AGENT_HANDLE:
      case HNANDEL_STATUS.SUBMITTER_HANDLE:
        return { color: '#1872F0' };
      default:
        return { color: '#737578' };
    }
  };
  const renderBatteryItem = item => {
    return (
      <View
        key={item.id}
        onClick={() => {
          Taro.navigateTo({
            url: `/subpackagesSqcli/pages/battery-details/battery-details?id=${item.id}`,
          });
        }}
      >
        <Cell
          title={item.title}
          desc={dayjs(item.createdTime).format('YYYY-MM-DD HH:mm:ss')}
          extra={
            <Text style={{ color: getTitleColor(item.status).color }}>
              {statusMap[item.status]}
            </Text>
          }
        />
        <Divider className="m-0" />
      </View>
    );
  };

  const handleCalendarChange = (value: any) => {
    const newSearchValue = { ...searchValues[tabvalue] };
    if (calendarTypes[tabvalue] === 'single') {
      const date = dayjs(value[3]).format('YYYY-MM-DD');
      newSearchValue.createdTimeStart = date;
      newSearchValue.createdTimeEnd = date;
    } else if (calendarTypes[tabvalue] === 'range') {
      const startDate = dayjs(value[0][3]).format('YYYY-MM-DD');
      const endDate = dayjs(value[1][3]).format('YYYY-MM-DD');
      newSearchValue.createdTimeStart = startDate;
      newSearchValue.createdTimeEnd = endDate;
    }
    setSearchValues(prev => ({
      ...prev,
      [tabvalue]: newSearchValue,
    }));
    handleOnSearch(newSearchValue); // 更新搜索
  };
  return (
    <NavHeader
      title="申报管理"
      contenStyle={{ paddingTop: '0px' }}
      // showBack={false}
      onClick={() => {
        Taro.reLaunch({ url: '/pages/index/index' });
      }}
      background="#F5F5F5"
    >
      <View className="header-bg">
        <View className="flex items-center gap-4">
          <div
            style={{
              display: 'flex',
              alignItems: 'center',
              background: '#fff',
              padding: '0 10px',
              borderRadius: '6px',
              border: '1px solid #E7E8E9',
              height: '40px',
              overflow: 'hidden',
              flex: 1,
            }}
          >
            <Text className="ri-search-line text-sqcli-hint-color" />
            <Input
              placeholder="搜索申报单标题"
              style={inputTheme}
              value={searchValues[tabvalue].title}
              onChange={value =>
                setSearchValues(prev => ({
                  ...prev,
                  [tabvalue]: { ...prev[tabvalue], title: value },
                }))
              }
              clearable
            />
          </div>
          <View
            className="text-sqcli-primary-color"
            onClick={() => handleOnSearch(searchValues[tabvalue])}
          >
            搜索
          </View>
        </View>
      </View>
      <View className="mx-16">
        <Tabs
          value={tabvalue}
          title={() => {
            return tabList.map(tab => (
              <View
                key={tab.value}
                className={`nut-tabs-titles-item nut-tabs-titles-item-left ${
                  tabvalue === tab.value ? 'nut-tabs-titles-item-active' : ''
                }`}
                onClick={() => {
                  setTabvalue(tab.value);
                }}
              >
                <Text className="nut-tabs-titles-item-line"></Text>
                <Text className="nut-tabs-titles-item-title">{tab.label}</Text>
              </View>
            ));
          }}
          autoHeight
          align="left"
          style={tabTheme}
        >
          <Tabs.TabPane key={tabList[0].value} value={tabList[0].value}>
            {renderCalendarText(
              searchValues.ALL.createdTimeStart,
              searchValues.ALL.createdTimeEnd,
            )}
            <View id={tabList[0].value} style={InfiniteUlStyle}>
              {allList && allList.length > 0 ? (
                <InfiniteLoading
                  {...commonJson}
                  target={tabList[0].value}
                  hasMore={allHasMore}
                  onLoadMore={allOnLoadMore}
                  onRefresh={allOnRefresh}
                >
                  {allList?.map(item => renderBatteryItem(item))}
                </InfiniteLoading>
              ) : (
                <Empty
                  title="暂无申报单"
                  image={<Image src={EmptyImg} />}
                  imageSize="32px"
                  style={EmptyTheme}
                />
              )}
            </View>
          </Tabs.TabPane>
          <Tabs.TabPane key={tabList[1].value} value={tabList[1].value}>
            {renderCalendarText(
              searchValues.IN_PROGRESS.createdTimeStart,
              searchValues.IN_PROGRESS.createdTimeEnd,
            )}
            <View id={tabList[1].value} style={InfiniteUlStyle}>
              {inProgressList && inProgressList.length > 0 ? (
                <InfiniteLoading
                  {...commonJson}
                  target={tabList[1].value}
                  hasMore={inProgressHasMore}
                  onLoadMore={inProgressOnLoadMore}
                  onRefresh={inProgressOnRefresh}
                >
                  {inProgressList?.map(item => renderBatteryItem(item))}
                </InfiniteLoading>
              ) : (
                <Empty
                  title="暂无申报单"
                  image={<Image src={EmptyImg} />}
                  imageSize="32px"
                  style={EmptyTheme}
                />
              )}
            </View>
          </Tabs.TabPane>
          <Tabs.TabPane key={tabList[2].value} value={tabList[2].value}>
            {renderCalendarText(
              searchValues.APPROVED.createdTimeStart,
              searchValues.APPROVED.createdTimeEnd,
            )}
            <View id={tabList[2].value} style={InfiniteUlStyle}>
              {approvedList && approvedList.length > 0 ? (
                <InfiniteLoading
                  {...commonJson}
                  target={tabList[2].value}
                  hasMore={approvedHasMore}
                  onLoadMore={approvedOnLoadMore}
                  onRefresh={approvedOnRefresh}
                >
                  {approvedList?.map(item => renderBatteryItem(item))}
                </InfiniteLoading>
              ) : (
                <Empty
                  title="暂无申报单"
                  image={<Image src={EmptyImg} />}
                  imageSize="32px"
                  style={EmptyTheme}
                />
              )}
            </View>
          </Tabs.TabPane>
          <Tabs.TabPane key={tabList[3].value} value={tabList[3].value}>
            {renderCalendarText(
              searchValues.REJECTED.createdTimeStart,
              searchValues.REJECTED.createdTimeEnd,
            )}
            <View id={tabList[3].value} style={InfiniteUlStyle}>
              {rejectedList && rejectedList.length > 0 ? (
                <InfiniteLoading
                  {...commonJson}
                  target={tabList[2].value}
                  hasMore={rejecteHasMore}
                  onLoadMore={rejecteOnLoadMore}
                  onRefresh={rejecteOnRefresh}
                >
                  {rejectedList?.map(item => renderBatteryItem(item))}
                </InfiniteLoading>
              ) : (
                <Empty
                  title="暂无申报单"
                  image={<Image src={EmptyImg} />}
                  imageSize="32px"
                  style={EmptyTheme}
                />
              )}
            </View>
          </Tabs.TabPane>
        </Tabs>
      </View>
      <View className="add-btn">
        <Button
          type="primary"
          size="xlarge"
          icon={<Text className="ri-add-fill text-xl" />}
          color="#1872F0"
          className="text-base"
          onClick={() =>
            Taro.navigateTo({ url: '/subpackagesSqcli/pages/create/create' })
          }
        >
          创建申报单
        </Button>
      </View>
      <Calendar
        style={calendarTheme}
        visible={isVisible}
        showTitle={false}
        defaultValue={
          calendarTypes[tabvalue] === 'single'
            ? searchValues[tabvalue].createdTimeStart
            : [
                searchValues[tabvalue].createdTimeStart,
                searchValues[tabvalue].createdTimeEnd,
              ]
        }
        onClose={() => setIsVisible(false)}
        onConfirm={handleCalendarChange}
        type={calendarTypes[tabvalue]}
        renderHeaderButtons={renderHeaderButtons}
        startDate="1999-12-31"
      />
      <CustomTabBar />
    </NavHeader>
  );
}
