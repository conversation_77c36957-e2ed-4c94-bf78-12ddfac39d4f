import { View, Text } from '@tarojs/components';
import React from 'react';

interface CellProps {
  title: string | React.ReactNode;
  desc?: string | React.ReactNode;
  extra?: string | React.ReactNode;
}

const Cell: React.FC<CellProps> = ({ title, desc, extra }) => {
  return (
    <View className="bg-white p-12">
      <View className="flex justify-between items-center">
        <View>
          <View className="text-sm">{title}</View>
          <View className="text-xs text-sqcli-hint-color mt-1">{desc}</View>
        </View>
        <Text className="text-xs text-sqcli-hint-color">{extra}</Text>
      </View>
    </View>
  );
};

export default Cell;
