.nut-tabs-titles-item {
  padding: 0;
  margin-left: 0;
  width: 25%;
  box-sizing: border-box;
  min-width: auto;

  &-active {
    .nut-tabs-titles-item-title {
      color: #1872F0;
    }
  }
}

.nut-tabs-titles-item-title {
  font-weight: 500;
  color: #999;
}

.nut-tabpane {
  padding: 0;
  background: #FFFFFF;
  border-radius: 8px;
}

.add-btn {
  position: fixed;
  bottom: 66px;
  left: 50%;
  transform: translateX(-50%);

  button {
    box-shadow:
      0px 8px 8px -4px rgba(17, 24, 39, 0.04),
      /* This creates a lighter shadow */
      0px 20px 24px -4px rgba(17, 24, 39, 0.1);
    /* This creates a darker shadow */
  }
}

.calendar-btns {
  width: 192px;
  font-size: 14px;
  margin: 0 auto;
  border-radius: 6px;

  .active {
    background: #FFFFFF;
    padding: 2px 10px;
    color: #1D1F20;
    border-radius: 6px;
  }
}

.nut-calendar-footer .calendar-confirm-btn {
  background: #1872F0;
}

.nut-calendar-day:nth-child(7n),
.nut-calendar-day:nth-child(7n+1),
.nut-calendar-week-item:first-of-type,
.nut-calendar-week-item:last-of-type,
.nut-calendar-week-item:first-of-type,
.nut-calendar-week-item:last-of-type,
.nut-calendar-day:nth-child(7n),
.nut-calendar-day:nth-child(7n+1) {
  color: #000;
}

.nut-calendar-day-choose {
  color: #1872F0;
}

.nut-calendar-header-buttons {
  height: auto;

  .calendar-btns {
    margin: 12px auto;
  }
}