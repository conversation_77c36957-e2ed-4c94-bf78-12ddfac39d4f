import FootOpt from '@/subpackagesSqcli/components/FootOpt';
import NavHeader from '@/subpackagesSqcli/components/NavHeader';
import { Form, Input } from '@nutui/nutui-react-taro';
import { View } from '@tarojs/components';
import '@/subpackagesSqcli/pages/create/create.less';
import { fetchUpdatePhone } from '@/subpackagesSqcli/api/servies/api';
import Taro from '@tarojs/taro';

definePageConfig({
  navigationStyle: 'custom',
  enableShareAppMessage: true,
  enablePullDownRefresh: true,
});

const updateUserInfo = () => {
  const onFinish = async values => {
    console.log('values===', values);
    const res = await fetchUpdatePhone({ newPhoneNumbe: values.newPhoneNumbe });
    if (res.code === 0) {
      Taro.showToast({
        title: '修改成功',
        icon: 'success',
        duration: 2000,
      });
      setTimeout(() => {
        Taro.navigateBack();
      }, 2000);
    } else {
      Taro.showToast({
        title: res.msg,
        icon: 'error',
        duration: 2000,
      });
    }
  };
  return (
    <NavHeader
      title="修改手机号"
      background="#F5F5F5"
      headerStyle={{
        position: 'fixed',
        background: '#F2F3F5',
      }}
    >
      <View className="create mx-16 my-4">
        <Form
          footer={
            <FootOpt
              isFormBtn
              btns={[
                {
                  text: '确认修改',
                  className: 'medium',
                },
              ]}
            />
          }
          onFinish={onFinish}
        >
          <View className="form-item">
            <Form.Item name="newPhoneNumbe">
              <Input placeholder="请输入手机号" />
            </Form.Item>
          </View>
        </Form>
      </View>
    </NavHeader>
  );
};

export default updateUserInfo;
