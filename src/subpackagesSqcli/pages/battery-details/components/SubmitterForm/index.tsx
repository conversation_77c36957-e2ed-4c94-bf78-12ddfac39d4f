import Switch from '@/subpackagesSqcli/components/Switch';
import {
  Divider,
  FileItem,
  Form,
  FormInstance,
  Input,
  TextArea,
  Uploader,
} from '@nutui/nutui-react-taro';
import { View, Text, Image } from '@tarojs/components';
import React, { useEffect, useState } from 'react';
import '@/subpackagesSqcli/pages/create/create.less';
import {
  handleImagePreview,
  HNANDEL_TYPE,
  upLoadAvatar,
  onOversize,
  maxFileSize,
  getStorageSync,
} from '@/subpackagesSqcli/utils';

interface IProps {
  form: FormInstance;
  onChange: (type: string, value: string) => void;
  handleType: HNANDEL_TYPE;
  batterySpec: string;
}

export enum SIGN_WAY {
  SIGN = 1,
  UPLOAD = 2,
}

const SubmitterForm: React.FC<IProps> = ({
  form,
  onChange,
  handleType,
  batterySpec,
}) => {
  const [fileList, setFileList] = useState<any[]>([]);
  const [isResubmit, setIsResubmit] = useState(true);

  const userInfo = JSON.parse(getStorageSync('USER_INFO'));

  const handleSwitchChange = value => {
    const type = value ? 'RESUBMIT' : 'CONFIRM';
    onChange('handleType', type);
    setIsResubmit(value);
  };

  useEffect(() => {
    form.setFieldsValue({
      handleType:
        handleType === HNANDEL_TYPE.APPROVE
          ? 'CONFIRM'
          : isResubmit
            ? 'RESUBMIT'
            : 'CONFIRM',
      handleByName: userInfo?.name,
    });
    // 当审核未通过时，回显电池规格
    if (handleType === HNANDEL_TYPE.REJECT) {
      form.setFieldsValue({
        batterySpec,
      });
    }
    setIsResubmit(handleType === HNANDEL_TYPE.REJECT);
  }, []);
  // 文件上传
  const handleFileChange = async (uploadFiles: FileItem[]) => {
    const result: any = await upLoadAvatar(
      uploadFiles[uploadFiles.length - 1].path as string,
    );
    if (result.code === 0) {
      fileList.push(result.upload);
      setFileList([...fileList]);
      form.setFieldsValue({
        fileList: [...fileList],
      });
    }
  };

  return (
    <View className="create">
      <Form labelPosition="left" form={form}>
        <View className="form-item has-label">
          <Form.Item name="handleByName" label="审批人">
            <Input placeholder="审批人" disabled />
          </Form.Item>
          <Divider className="m-0" />

          {handleType === HNANDEL_TYPE.REJECT && (
            <>
              <Form.Item name="handleType" label="是否重提" trigger="onConfirm">
                <Switch
                  initialValue={isResubmit}
                  onChange={handleSwitchChange}
                  activeText="是"
                  inactiveText="否"
                />
              </Form.Item>
              <Divider className="m-0" />
            </>
          )}

          {isResubmit && (
            <>
              <Form.Item name="batterySpec" label="电池规格">
                <Input placeholder="电池规格" maxLength={50} />
              </Form.Item>
              <Form.Item name="fileList" label="上传照片（选填）">
                <Uploader
                  maxCount="10"
                  multiple
                  autoUpload={false}
                  onChange={handleFileChange}
                  camera="back"
                  maxFileSize={maxFileSize}
                  onOversize={onOversize}
                >
                  <View className="flex justify-center items-center gap-1">
                    <Text className="text-hint-color">选择附件</Text>
                    <View className="upload-btn text-white">
                      <Text className="ri-add-fill" />
                    </View>
                  </View>
                </Uploader>
              </Form.Item>
              <Divider className="m-0" />
              <View className="flex flex-wrap gap-1">
                {fileList.map(image => (
                  <span key={image.url} style={{ marginRight: '10px' }}>
                    <Image
                      style={{ width: '80px', height: '80px' }}
                      src={image.url}
                      mode="aspectFill"
                      onClick={() =>
                        handleImagePreview(
                          image.url,
                          fileList.map(v => v.url),
                        )
                      }
                    />
                  </span>
                ))}
              </View>
            </>
          )}
          <Form.Item name="remark" style={{ height: 'auto' }}>
            <TextArea placeholder="审批备注（选填）" maxLength={500} />
          </Form.Item>
        </View>
      </Form>
    </View>
  );
};

export default SubmitterForm;
