import Switch from '@/subpackagesSqcli/components/Switch';
import {
  Cell,
  Divider,
  Form,
  FormInstance,
  Input,
  Picker,
  TextArea,
} from '@nutui/nutui-react-taro';
import { View, Text } from '@tarojs/components';
import '@/subpackagesSqcli/pages/create/create.less';
import React, { useEffect, useState } from 'react';
import { getDictDataByIdentity } from '@/subpackagesSqcli/api';
import { getStorageSync } from '@/subpackagesSqcli/utils';

const cellTheme: any = {
  '--nutui-cell-divider-border-bottom': '0',
};

interface IProps {
  form: FormInstance;
  onChange: (type: string, value: string) => void;
}

const AirLineForm: React.FC<IProps> = ({ form, onChange }) => {
  const [reasonOptions, setReasonOptions] = useState<any[]>([]);
  const [handleType, setHandleType] = useState(true);
  const userInfo = JSON.parse(getStorageSync('USER_INFO'));
  const handleSwitchChange = value => {
    const type = value ? 'APPROVED' : 'REJECT';
    onChange('handleType', type);
    setHandleType(value);
  };
  useEffect(() => {
    form.setFieldsValue({
      handleType: handleType ? 'APPROVED' : 'REJECT',
      handleByName: userInfo?.name,
      auditOpinion: '审批通过',
    });
    getDictDataByIdentity('BATTERY_APP_REJECT_REASON').then(res => {
      const data = res?.dictValues?.map(v => ({ value: v.name, text: v.name }));
      setReasonOptions(data);
    });
  }, []);

  const handleReasonChange = value => {
    form.setFieldsValue({ auditOpinion: `审核未通过，原因：${value || ''}` });
  };
  return (
    <View className="create">
      <Form labelPosition="left" form={form}>
        <View className="form-item has-label">
          <Form.Item name="handleByName" label="审批人">
            <Input disabled />
          </Form.Item>
          <Divider className="m-0" />
          <Form.Item name="handleType" label="审批是否通过" trigger="onConfirm">
            <Switch
              initialValue={handleType}
              onChange={handleSwitchChange}
              activeText="通过"
              inactiveText="拒绝"
            />
          </Form.Item>
          <Divider className="m-0" />
          {!handleType && (
            <>
              <Form.Item
                label="原因"
                name="auditReason"
                trigger="onConfirm"
                getValueFromEvent={(...args) => args[1]}
                onClick={(_, ref: any) => {
                  ref.open();
                }}
              >
                <Picker
                  options={[reasonOptions]}
                  onConfirm={(_, value) => handleReasonChange(value[0])}
                >
                  {(value: any) => {
                    return (
                      <Cell
                        style={{
                          padding: 0,
                          ...cellTheme,
                        }}
                        className="nutui-cell--clickable"
                        title={
                          value.length ? (
                            reasonOptions.filter(po => po.value === value[0])[0]
                              ?.text
                          ) : (
                            <Text className="text-sqcli-hint-color">
                              请选择
                            </Text>
                          )
                        }
                        align="center"
                      />
                    );
                  }}
                </Picker>
              </Form.Item>
              <Divider className="m-0" />
            </>
          )}
          <Form.Item name="auditOpinion" style={{ height: 'auto' }}>
            <TextArea placeholder="审批意见" />
          </Form.Item>
          <Divider className="m-0" />
          <Form.Item name="remark" style={{ height: 'auto' }}>
            <TextArea placeholder="审批备注" maxLength={500} />
          </Form.Item>
        </View>
      </Form>
    </View>
  );
};

export default AirLineForm;
