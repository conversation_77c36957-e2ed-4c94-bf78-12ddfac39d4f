import { Cell, Tag } from '@nutui/nutui-react-taro';
import { View, Text, Image } from '@tarojs/components';
import dayjs from 'dayjs';
import { useEffect, useState } from 'react';
import {
  handleImagePreview,
  handleUploadImage,
  HNANDEL_TYPE,
} from '@/subpackagesSqcli/utils';
import { getStatusText, tagTheme } from '../AirlineAudit';

const SubmitterAudit = ({ audit }) => {
  const [fileList, setFileList] = useState<{ fileId: string; url: string }[]>(
    [],
  );

  useEffect(() => {
    if (audit?.fileList?.length) {
      const promises = audit.fileList.map(fileId => handleUploadImage(fileId));
      Promise.all(promises).then(fileTempPaths => {
        setFileList(fileTempPaths);
      });
    }
  }, [audit]);

  return (
    <Cell>
      <View className="w-full">
        <View className="flex items-center justify-between">
          <Tag
            type={
              audit.handleType === HNANDEL_TYPE.RESUBMIT ? 'warning' : 'success'
            }
            style={tagTheme}
            round
          >
            {getStatusText(audit.handleType)}
          </Tag>
          <Text className="text-hint-color text-xs">
            {dayjs(audit.handleTime).format('YYYY-MM-DD HH:mm:ss')}
          </Text>
        </View>
        <View className="mt-2 text-sm flex">
          <Text className="font-medium text-primary-text-color min-w-20 inline-block">
            提交人
          </Text>
          <Text className="flex-1 text-hint-color">{audit.handleByName}</Text>
        </View>
        {audit.handleType === HNANDEL_TYPE.RESUBMIT && (
          <>
            <View className="mt-2 text-sm flex">
              <Text className="font-medium text-primary-text-color min-w-20 inline-block">
                电池规格
              </Text>
              <Text className="flex-1 text-hint-color">
                {audit.batterySpec}
              </Text>
            </View>
            <View className="mt-2 text-sm">
              <Text className="font-medium text-primary-text-color min-w-20 inline-block">
                上传图片
              </Text>
              <View className="flex flex-wrap gap-1">
                {fileList?.length > 0 &&
                  fileList?.map(image => (
                    <span key={image.fileId} style={{ marginRight: '10px' }}>
                      <Image
                        style={{ width: '80px', height: '80px' }}
                        src={image.url}
                        mode="aspectFill"
                        onClick={() =>
                          handleImagePreview(
                            image.url,
                            fileList?.map(v => v.url),
                          )
                        }
                      />
                    </span>
                  ))}
              </View>
            </View>
          </>
        )}
        <View className="mt-2 text-sm flex">
          <Text className="font-medium text-primary-text-color min-w-20 inline-block">
            审批备注
          </Text>
          <Text className="flex-1 text-hint-color">{audit.remark}</Text>
        </View>
      </View>
    </Cell>
  );
};

export default SubmitterAudit;
