import { HNANDEL_TYPE } from '@/subpackagesSqcli/utils';
import { Cell, Tag } from '@nutui/nutui-react-taro';
import { View, Text } from '@tarojs/components';
import dayjs from 'dayjs';

export const tagTheme: any = {
  '--nutui-tag-font-size': '14px',
  '--nutui-tag-padding': '1px 4px',
  '--nutui-tag-border-radius': '4px',
  '--nutui-tag-warning-background-color': '#FB7A28',
};

export const getStatusText = status => {
  switch (status) {
    case 'SUBMIT':
      return '提交';
    case 'RESUBMIT':
      return '重提';
    case 'APPROVED':
      return '审批通过';
    case 'REJECT':
      return '审批未通过';
    case 'CONFIRM':
      return '审批确认';
    default:
      return '未知';
  }
};

const AirlineAudit = ({ audit }) => {
  const type = audit.handleType === HNANDEL_TYPE.APPROVE ? 'success' : 'danger';
  return (
    <Cell>
      <View className="w-full">
        <View className="flex items-center justify-between">
          <Tag type={type} style={tagTheme} round>
            <Text
              className={
                audit.handleType === HNANDEL_TYPE.APPROVE
                  ? 'ri-check-line'
                  : 'ri-close-line'
              }
            />
            {getStatusText(audit.handleType)}
          </Tag>
          <Text className="text-sqcli-hint-color text-xs">
            {dayjs(audit.handleTime).format('YYYY-MM-DD HH:mm:ss')}
          </Text>
        </View>
        <View className="mt-2 text-sm flex">
          <Text className="font-medium text-sqcli-primary-text-color min-w-20 inline-block">
            航司待办人
          </Text>
          <Text className="flex-1 text-sqcli-hint-color">
            {audit.handleByName}
          </Text>
        </View>
        {audit.handleType === HNANDEL_TYPE.REJECT && (
          <View className="mt-2 text-sm flex">
            <Text className="font-medium text-sqcli-primary-text-color min-w-20 inline-block">
              原因
            </Text>
            <Text className="flex-1 text-sqcli-hint-color">
              {audit.auditReason}
            </Text>
          </View>
        )}
        <View className="mt-2 text-sm flex">
          <Text className="font-medium text-sqcli-primary-text-color min-w-20 inline-block">
            审批意见
          </Text>
          <Text className="flex-1 text-sqcli-hint-color">
            {audit.auditOpinion}
          </Text>
        </View>
        <View className="mt-2 text-sm flex">
          <Text className="font-medium text-sqcli-primary-text-color min-w-20 inline-block">
            审批备注
          </Text>
          <Text className="flex-1 text-sqcli-hint-color">{audit.remark}</Text>
        </View>
      </View>
    </Cell>
  );
};

export default AirlineAudit;
