import NavHeader from '@/subpackagesSqcli/components/NavHeader';
import { Cell, Tag, Form } from '@nutui/nutui-react-taro';
import { View, Text, Image } from '@tarojs/components';
import { useEffect, useState } from 'react';
import Taro, { useDidShow } from '@tarojs/taro';
import {
  auditBattery,
  getAuditRecords,
  getBatteryInfoById,
  getDictDataByIdentity,
  getSubmitor,
} from '@/subpackagesSqcli/api';
import {
  getStorageSync,
  handleImagePreview,
  handleUploadImage,
  HNANDEL_TYPE,
  setStorageSync,
  SIGN_WAY,
  transferListToMap,
} from '@/subpackagesSqcli/utils';
import FootOpt from '@/subpackagesSqcli/components/FootOpt';
import SignaturePreview from '@/subpackagesSqcli/components/SignaturePreview';
import AirLineForm from './components/AirlineForm';
import SubmitterForm from './components/SubmitterForm';
import AirlineAudit, { tagTheme } from './components/AirlineAudit';
import SubmitterAudit from './components/SubmitterAudit';

definePageConfig({
  navigationStyle: 'custom',
  enableShareAppMessage: true,
  enablePullDownRefresh: true,
});

const cellTheme: any = {
  '--nutui-cell-title-font-size': '16px',
  '--nutui-cell-title-color': '#1D1F20',
  '--nutui-cell-description-font-size': '14px',
  '--nutui-cell-description-color': '#1D1F20',
};

const BatteryDetails = () => {
  const [batteryInfo, setBatteryInfo] = useState<any>({});
  const [statusMap, setStatusMap] = useState<any>({});
  const [auditInfo, setAuditInfo] = useState<any[]>([]);
  const [isSubmitter, setIsSubmitter] = useState(false);
  const [fileList, setFileList] = useState<string[]>([]);
  const [signUrl, setSignUrl] = useState<string>('');
  const [previewVisible, setPreviewVisible] = useState(false);
  const [form] = Form.useForm();

  const routerParams: any = Taro?.getCurrentInstance()?.router?.params;
  const { id } = routerParams || {};
  // 请求状态下拉
  const getStatus = () => {
    getDictDataByIdentity('BATTERY_APP_STATUS').then(res => {
      const data = transferListToMap(res?.dictValues, 'name', 'value');
      setStatusMap(data);
    });
  };

  const fetchData = () => {
    getBatteryInfoById(id).then(res => {
      if (res?.data?.fileList?.length) {
        const promises = res.data.fileList.map(fileId =>
          handleUploadImage(fileId),
        );
        Promise.all(promises).then(fileTempPaths => {
          setFileList(fileTempPaths.map(v => v.url));
        });
      }
      if (res.data.signFileId) {
        handleUploadImage(res.data.signFileId).then(signRes =>
          setSignUrl(signRes.url),
        );
      }
      setBatteryInfo(res.data);
    });
    getStatus();
    // 获取审核信息
    getAuditRecords(id).then(res => {
      setAuditInfo(res.data);
    });
    getSubmitor().then(res => {
      setIsSubmitter(res.data);
    });
  };

  useEffect(() => {
    fetchData();
  }, [id]);
  useDidShow(() => {
    // 获取申报单详情
    const isFresh = getStorageSync('isDetailFresh');
    if (isFresh) {
      fetchData();
      Taro.removeStorageSync('isDetailFresh');
    }
  });

  // 是否展示航司待办人表单
  const isShowAirLineForm =
    !isSubmitter && batteryInfo?.status === 'AGENT_HANDLE';
  // 是否展示提交人待办人表单
  const isShowSubmitterForm =
    isSubmitter && batteryInfo.status === 'SUBMITTER_HANDLE';

  const handleSubmit = async () => {
    try {
      await form.submit();
      const values = form.getFieldsValue(true);
      if (isShowAirLineForm) {
        if (values.handleType === HNANDEL_TYPE.REJECT) {
          if (!values.auditReason || values.auditReason.length === 0) {
            return Taro.showToast({
              title: '请选择审批不通过原因',
              icon: 'none',
            });
          }
        }
      } else if (isShowSubmitterForm) {
        if (values.handleType === HNANDEL_TYPE.RESUBMIT) {
          if (!values.batterySpec) {
            return Taro.showToast({ title: '请输入电池规格', icon: 'none' });
          }
        }
      }
      const params: any = {
        ...values,
        auditReason: values?.auditReason ? values?.auditReason[0] : undefined,
        fileList: values?.fileList
          ? values?.fileList.map(v => v.fileId)
          : undefined,
      };
      const res = await auditBattery({ ...params, id });

      if (res.code === 0) {
        Taro.showToast({ title: '提交成功', icon: 'success' });
        // 缓存需要更新状态的数据
        setStorageSync('batteryId', id);
        setTimeout(() => {
          Taro.navigateBack();
        }, 1500);
      } else {
        Taro.showToast({ title: res.msg, icon: 'error' });
      }
    } catch (err) {
      console.log(err);
    }
  };

  return (
    <NavHeader
      title="申报单"
      background="#F5F5F5"
      headerStyle={{
        position: 'fixed',
        background: '#F2F3F5',
      }}
    >
      <View className="mx-4 my-3 pb-28">
        <Cell
          title={batteryInfo?.title}
          description={
            <Text className="pt-1 inline-block">
              {batteryInfo?.airlineName} {batteryInfo?.flightNumber}
            </Text>
          }
          extra={
            <Tag type="warning" style={tagTheme}>
              {statusMap[batteryInfo?.status]}
            </Tag>
          }
          align="center"
          style={cellTheme}
        />
        <Cell title="旅客姓名" extra={batteryInfo?.passenger} />
        <Cell.Group>
          <Cell title="电池规格" extra={batteryInfo?.batterySpec} />
          {fileList.length > 0 && (
            <Cell className="flex-wrap">
              {fileList?.map(image => (
                <span key={image} style={{ marginRight: '10px' }}>
                  <Image
                    style={{ width: '80px', height: '80px' }}
                    src={image}
                    mode="aspectFill"
                    onClick={() => handleImagePreview(image, fileList)}
                  />
                </span>
              ))}
            </Cell>
          )}
        </Cell.Group>
        {batteryInfo?.signFileId && (
          <Cell.Group>
            <Cell title="旅客签名" extra="" />
            <Cell>
              <Image
                src={signUrl}
                style={{
                  width: '100px',
                  height: '100px',
                  ...(batteryInfo?.signType === SIGN_WAY.SIGN && {
                    transform: 'rotate(-90deg)',
                  }),
                }}
                mode="aspectFill"
                onClick={() => {
                  if (batteryInfo?.signType === SIGN_WAY.SIGN) {
                    setPreviewVisible(true);
                  } else {
                    handleImagePreview(signUrl);
                  }
                }}
              />
            </Cell>
          </Cell.Group>
        )}
        <Cell title="备注" description={batteryInfo?.remark} />
        {auditInfo.length > 0 && (
          <>
            <View className="text-base font-medium pt-2">处理意见</View>
            <Cell.Group>
              {auditInfo.map(item =>
                item.handleNode === 'AIRLINE' ? (
                  <AirlineAudit audit={item} />
                ) : (
                  <SubmitterAudit audit={item} />
                ),
              )}
            </Cell.Group>
          </>
        )}
        {isShowAirLineForm && (
          <>
            <View className="text-base font-medium pt-2">处理操作</View>
            <AirLineForm
              form={form}
              onChange={(key, value) => {
                form.setFieldsValue({ [key]: value });
                if (value === HNANDEL_TYPE.REJECT) {
                  const reason = form.getFieldValue('auditReason');
                  form.setFieldsValue({
                    auditOpinion: `审批不通过，原因：${reason || ''}`,
                  });
                } else {
                  form.setFieldsValue({ auditOpinion: '审批通过' });
                }
              }}
            />
          </>
        )}
        {isShowSubmitterForm && (
          <>
            <View className="text-base font-medium pt-2">处理操作</View>
            <SubmitterForm
              form={form}
              onChange={(key, value) => form.setFieldsValue({ [key]: value })}
              handleType={batteryInfo?.handleType}
              batterySpec={batteryInfo?.batterySpec}
            />
          </>
        )}
      </View>
      <FootOpt
        isFormBtn
        btns={[
          {
            text: '返回',
            className: 'default-btn medium',
            handle: () => Taro.navigateBack(),
          },
          ...(isShowAirLineForm || isShowSubmitterForm
            ? [
                {
                  text: '提交处理',
                  className: 'primary-btn medium',
                  handle: () => handleSubmit(),
                },
              ]
            : []),
          ...(batteryInfo?.status === 'DRAFT'
            ? [
                {
                  text: '编辑',
                  className: 'primary-btn medium',
                  handle: () => {
                    Taro.navigateTo({
                      url: `/subpackagesSqcli/pages/create/create?id=${id}`,
                    });
                  },
                },
              ]
            : []),
        ]}
      />
      <SignaturePreview
        imageUrl={signUrl}
        visible={previewVisible}
        onClose={() => setPreviewVisible(false)}
        width="280px"
        height="180px"
        title="您的签名"
      />
    </NavHeader>
  );
};

export default BatteryDetails;
