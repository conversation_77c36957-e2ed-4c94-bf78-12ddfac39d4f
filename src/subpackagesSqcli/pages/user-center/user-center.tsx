import NavHeader from '@/subpackagesSqcli/components/NavHeader';
import { View, Text } from '@tarojs/components';
import { Avatar, Cell } from '@nutui/nutui-react-taro';
import Taro, { useDidShow } from '@tarojs/taro';
import { useState } from 'react';
import { getUserInfo } from '@/subpackagesSqcli/api';
import CustomTabBar from '@/subpackagesSqcli/components/CustomTabBar';
import './user-center.less';

definePageConfig({
  navigationStyle: 'custom',
  enableShareAppMessage: true,
  enablePullDownRefresh: true,
});

const UserCenter = () => {
  // const [visible, setVisible] = useState(false);
  const [userInfo, setUserInfo] = useState<any>({});

  useDidShow(() => {
    getUserInfo().then(res => {
      setUserInfo(res);
    });
  });

  // const logout = () => {
  //   fetchLogout().then(res => {
  //     if (res.code === 0) {
  //       Taro.reLaunch({ url: '/subpackagesSqcli/pages/login/login' });
  //       Taro.clearStorage();
  //     }
  //   });
  // };
  return (
    <NavHeader
      title="个人中心"
      background="#F5F5F5"
      // showBack={false}
      onClick={() => {
        Taro.reLaunch({ url: '/pages/index/index' });
      }}
      contenStyle={{
        paddingTop: '0px',
        backgroundColor: '#F5F5F5',
      }}
    >
      <View className="user-center">
        <View className="header-bg flex items-center flex-col text-sm">
          <Avatar size="80" src={userInfo?.picture} className="mt-4" />
          <View className="user-name text-xl font-bold">{userInfo?.name}</View>
          <View className="user-airline py-1 text-sqcli-hint-color">
            {userInfo?.deptName}
          </View>
          {userInfo?.phoneNumber && (
            <View className="user-phone text-sqcli-hint-color">
              <Text className="ri-phone-fill" />
              {userInfo?.phoneNumber}
            </View>
          )}
        </View>
        <View className="mx-4">
          <Cell
            title="个人信息"
            extra={
              <Text
                className="ri-arrow-right-s-line text-xl text-sqcli-placeholder-color"
                onClick={() =>
                  Taro.navigateTo({
                    url: '/subpackagesSqcli/pages/user-info/user-info',
                  })
                }
              />
            }
            align="center"
            className="h-46"
          />
          <Cell
            title="修改密码"
            extra={
              <Text
                className="ri-arrow-right-s-line text-xl text-sqcli-placeholder-color"
                onClick={() =>
                  Taro.navigateTo({
                    url: '/subpackagesSqcli/pages/update-password/update-password',
                  })
                }
              />
            }
            align="center"
            className="h-46"
          />
          {/* <Cell
            title={<Text className="text-sqcli-error-dark">退出登录</Text>}
            extra={
              <Text className="ri-arrow-right-s-line text-xl text-sqcli-placeholder-color" />
            }
            align="center"
            className="h-46"
            onClick={() => setVisible(true)}
          /> */}
        </View>
        {/* <ActionSheet visible={visible} onCancel={() => setVisible(false)}>
          <View className="action-btn confirm-loginout" onClick={logout}>
            确认退出登录
          </View>
          <View className="action-btn" onClick={() => setVisible(false)}>
            取消
          </View>
        </ActionSheet> */}
      </View>
      <CustomTabBar />
    </NavHeader>
  );
};

export default UserCenter;
