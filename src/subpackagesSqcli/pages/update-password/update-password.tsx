import NavHeader from '@/subpackagesSqcli/components/NavHeader';
import { Button, Dialog, Divider, Form, Input } from '@nutui/nutui-react-taro';
import { View } from '@tarojs/components';
import '@/subpackagesSqcli/pages/create/create.less';
import FootOpt from '@/subpackagesSqcli/components/FootOpt';
import Taro from '@tarojs/taro';
import { fetchUpdatePassword } from '@/subpackagesSqcli/api';
import CryptoES from 'crypto-es';

definePageConfig({
  navigationStyle: 'custom',
  enableShareAppMessage: true,
  enablePullDownRefresh: true,
});

const UpdatePassword = () => {
  const [form] = Form.useForm();

  const onFinish = async values => {
    const res = await fetchUpdatePassword({
      oldPass: CryptoES.SHA256(values.oldPassword).toString(
        CryptoES.enc.Base64,
      ),
      newPass: CryptoES.SHA256(values.newPassword).toString(
        CryptoES.enc.Base64,
      ),
      confirmPw: CryptoES.SHA256(values.confirmPassword).toString(
        CryptoES.enc.Base64,
      ),
    });
    if (res.code === 0) {
      Dialog.open('tips', {
        content: '修改成功, 点击确认返回登录页重新登录',
        hideCancelButton: true,
        footer: (
          <Button
            className="primary-btn small"
            type="primary"
            block
            onClick={() => {
              Dialog.close('tips');
              Taro.reLaunch({ url: '/subpackagesSqcli/pages/login/login' });
              Taro.clearStorage();
            }}
          >
            确认
          </Button>
        ),
      });
    } else {
      Taro.showToast({
        title: res.msg,
        icon: 'none',
      });
    }
  };

  return (
    <NavHeader
      title="修改密码"
      background="#F5F5F5"
      headerStyle={{
        position: 'fixed',
        background: '#F2F3F5',
      }}
    >
      <View className="create mx-16 my-4">
        <Form
          form={form}
          labelPosition="left"
          footer={
            <FootOpt
              isFormBtn
              btns={[
                {
                  text: '确认修改',
                  className: 'medium',
                },
              ]}
            />
          }
          onFinish={onFinish}
        >
          <View className="form-item has-label has-rules">
            <Form.Item
              name="oldPassword"
              label="旧密码"
              rules={[
                {
                  required: true,
                  message: '请输入旧密码',
                },
              ]}
              style={{ height: '50px' }}
            >
              <Input type="password" placeholder="请输入" />
            </Form.Item>
            <Divider className="m-0" />
            <Form.Item
              name="newPassword"
              label="新密码"
              rules={[
                {
                  required: true,
                  message: '请输入新密码',
                },
                {
                  validator: (_ruleCfg, value) => {
                    const confirmPassword =
                      form.getFieldValue('confirmPassword');
                    if (confirmPassword && value !== confirmPassword)
                      return false;
                    return true;
                  },
                  message: '两次密码输入不一致',
                },
              ]}
              style={{ height: '50px' }}
            >
              <Input type="password" placeholder="请输入" />
            </Form.Item>
            <Divider className="m-0" />
            <Form.Item
              name="confirmPassword"
              label="确认新密码"
              rules={[
                {
                  required: true,
                  message: '请输入新密码',
                },
                {
                  validator: (_ruleCfg, value) => {
                    const newPassword = form.getFieldValue('newPassword');
                    if (newPassword && value !== newPassword) return false;
                    return true;
                  },
                  message: '两次密码输入不一致',
                },
              ]}
              style={{ height: '50px' }}
            >
              <Input type="password" placeholder="请输入" />
            </Form.Item>
          </View>
        </Form>
      </View>
      <Dialog id="tips" />
    </NavHeader>
  );
};

export default UpdatePassword;
