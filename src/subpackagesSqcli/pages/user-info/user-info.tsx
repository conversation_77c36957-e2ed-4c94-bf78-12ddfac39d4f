import { getUserInfo } from '@/subpackagesSqcli/api';
import NavHeader from '@/subpackagesSqcli/components/NavHeader';
import { Cell } from '@nutui/nutui-react-taro';
import { Text, View } from '@tarojs/components';
import Taro, { useDidShow } from '@tarojs/taro';
import { useState } from 'react';

definePageConfig({
  navigationStyle: 'custom',
  enableShareAppMessage: true,
  enablePullDownRefresh: true,
});

const UserInfo = () => {
  const [userInfo, setUserInfo] = useState<any>({});

  useDidShow(() => {
    getUserInfo().then(res => {
      setUserInfo(res);
    });
  });
  return (
    <NavHeader
      title="个人信息"
      background="#F5F5F5"
      headerStyle={{
        position: 'fixed',
        background: '#F2F3F5',
      }}
    >
      <Cell.Group className="mt-4 mx-4">
        <Cell title="所属部门" extra={userInfo?.deptName} />
        <Cell title="姓名" extra={userInfo?.name} />
        <Cell
          title="手机号"
          extra={
            <View
              className="flex items-center"
              onClick={() =>
                Taro.navigateTo({
                  url: '/subpackagesSqcli/pages/update-userInfo/update-userInfo',
                })
              }
            >
              {userInfo?.phoneNumber}
              <Text className="ri-arrow-right-s-line text-xl text-sqcli-placeholder-color" />
            </View>
          }
          align="center"
          className="h-46"
        />
      </Cell.Group>
    </NavHeader>
  );
};

export default UserInfo;
