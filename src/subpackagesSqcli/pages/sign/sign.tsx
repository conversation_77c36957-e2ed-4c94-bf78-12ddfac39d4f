import { useRef } from 'react';
import NavHeader from '@/subpackagesSqcli/components/NavHeader';
import { Button } from '@nutui/nutui-react-taro';
import { View, Text } from '@tarojs/components';
// import Taro from '@tarojs/taro';
import SignaturePad from '@/subpackagesSqcli/components/SignaturePad';

definePageConfig({
  navigationStyle: 'custom',
  disableScroll: true,
});

const Sign = () => {
  const signatureRef = useRef<any>(null);
  // const confirm = async (dataurl: string, isSigned?: boolean) => {
  //   if (isSigned) {
  //     const base64 = Taro.getFileSystemManager().readFileSync(
  //       dataurl,
  //       'base64',
  //     );
  //     const res = 'data:image/jpeg;base64,' + base64;
  //     Taro.setStorageSync('signatureBase64', res);
  //     Taro.setStorageSync('signatureUrl', dataurl);
  //     Taro.nextTick(() => {
  //       Taro.navigateBack();
  //     });
  //   } else {
  //     Taro.showToast({
  //       title: '请先签字',
  //       icon: 'none',
  //     });
  //   }
  // };
  // const clear = () => {
  //   console.log('清除事件');
  // };

  const handleSubmit = () => {
    signatureRef.current?.confirm();
  };

  const handleClear = () => {
    if (signatureRef.current) {
      signatureRef.current.clear();
    }
  };
  return (
    <NavHeader
      title="手写签名"
      background="#F5F5F5"
      icon={<Text className="ri-close-line" />}
    >
      <View className="flex mt-6 border-2 border-gray-500 border-solid mx-4 my-4">
        <View className="">
          <Button
            type="default"
            size="small"
            onClick={handleClear}
            className="default-btn"
          >
            重签
          </Button>
          <Button
            type="primary"
            size="small"
            onClick={handleSubmit}
            className="primary-btn"
          >
            确认
          </Button>
        </View>
        <View className="flex-1" style={{ height: '100vh' }}>
          <SignaturePad className="signature-canvas" ref={signatureRef} />
        </View>
      </View>
    </NavHeader>
  );
};

export default Sign;
