
/*
* https://remixicon.com
* https://github.com/Remix-Design/RemixIcon
* Copyright RemixIcon.com
* Released under the Apache License Version 2.0
*/

@font-face {
    font-family: "remixicon";
    src: url('../fonts/remixicon.eot?t=1731550935861'); /* IE9*/
    src: url('../fonts/remixicon.eot?t=1731550935861#iefix') format('embedded-opentype'), /* IE6-IE8 */
    url("../fonts/remixicon.woff2?t=1731550935861") format("woff2"),
    url("../fonts/remixicon.woff?t=1731550935861") format("woff"),
    url('../fonts/remixicon.ttf?t=1731550935861') format('truetype'), /* chrome, firefox, opera, Safari, Android, iOS 4.2+*/
    url('../fonts/remixicon.svg?t=1731550935861#remixicon') format('svg'); /* iOS 4.1- */
    font-display: swap;
}

[class^="ri-"], [class*="ri-"] {
    font-family: 'remixicon' !important;
    font-style: normal;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.ri-lg { font-size: 1.3333em; line-height: 0.75em; vertical-align: -.0667em; }
.ri-xl { font-size: 1.5em; line-height: 0.6666em; vertical-align: -.075em; }
.ri-xxs { font-size: .5em; }
.ri-xs { font-size: .75em; }
.ri-sm { font-size: .875em }
.ri-1x { font-size: 1em; }
.ri-2x { font-size: 2em; }
.ri-3x { font-size: 3em; }
.ri-4x { font-size: 4em; }
.ri-5x { font-size: 5em; }
.ri-6x { font-size: 6em; }
.ri-7x { font-size: 7em; }
.ri-8x { font-size: 8em; }
.ri-9x { font-size: 9em; }
.ri-10x { font-size: 10em; }
.ri-fw { text-align: center; width: 1.25em; }

.ri-arrow-left-s-line:before { content: "\ea64"; }
.ri-arrow-right-s-line:before { content: "\ea6e"; }
.ri-barcode-fill:before { content: "\eaa1"; }
.ri-arrow-down-s-line:before { content: "\ea4e"; }
.ri-arrow-up-s-line:before { content: "\ea78"; }
.ri-arrow-right-line:before { content: "\ea6c"; }
.ri-checkbox-circle-fill:before { content: "\eb80"; }
.ri-error-warning-fill:before { content: "\eca0"; }
.ri-close-circle-fill:before { content: "\eb96"; }
.ri-time-fill:before { content: "\f20e"; }
.ri-check-line:before { content: "\eb7b"; }
.ri-refund-2-line:before { content: "\f066"; }
.ri-arrow-down-s-fill:before { content: "\ea4d"; }
.ri-arrow-up-s-fill:before { content: "\ea77"; }
.ri-file-copy-line:before { content: "\ecd5"; }
.ri-close-line:before { content: "\eb99"; }
.ri-qr-scan-2-line:before { content: "\f03f"; }
.ri-edit-line:before { content: "\ec86"; }
.ri-git-merge-line:before { content: "\edc1"; }
.ri-calendar-2-line:before { content: "\eb21"; }
.ri-store-2-line:before { content: "\f1a5"; }
.ri-group-line:before { content: "\ede3"; }
.ri-user-3-line:before { content: "\f256"; }
.ri-calendar-2-fill:before { content: "\eb20"; }
.ri-filter-3-line:before { content: "\ed25"; }
.ri-pie-chart-2-line:before { content: "\eff6"; }
.ri-book-3-fill:before { content: "\ead4"; }
.ri-search-line:before { content: "\f0d1"; }
.ri-add-fill:before { content: "\ea12"; }
.ri-phone-fill:before { content: "\efe9"; }