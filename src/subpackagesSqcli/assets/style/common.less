@import '@nutui/nutui-react-taro/dist/style.css';
@import '@/subpackagesSqcli/assets/style/remixicon.less';
@import '@/subpackagesSqcli/assets/style/theme.less';

.header-bg {
  background: linear-gradient(180deg, #e8eef7 0%, #f5f5f5 100%);
  padding: 98px 16px 4px;
}

.header-bg-1 {
  background: linear-gradient(180deg, #8eceff 0%, #d4e6fe 12%, #d4e6fe 100%);
  padding: 98px 16px 4px;
}

button:not(.nut-dialog-footer-ok):not(.nut-dialog-footer-cancel) {
  &.disabled {
    opacity: 0.4;
    cursor: not-allowed;
    pointer-events: none;
  }

  &.rounded {
    border-radius: 99px;
  }

  &.primary-btn {
    background: @primary_main;

    &.nut-button-disabled {
      background: rgba(0, 82, 217, 0.5);
      border: none;
    }
  }

  &.outline-btn {
    border: 1px solid @primary_main;
    color: @primary_main;
  }

  &.default-btn {
    background: @neutral-0;
    border: 1px solid @border-color;
    color: @primary_main;

    &::after {
      border: 1px solid @border-color;
    }

    &.nut-button-disabled {
      background: #f6f6f6;
      color: #8c8c8c;
    }
  }
}

.nut-input {
  border-radius: 6px;
}

.nut-cell.nut-form-footer {
  background: transparent;
}

.nut-radio-icon-checked {
  color: @primary_main;
}
