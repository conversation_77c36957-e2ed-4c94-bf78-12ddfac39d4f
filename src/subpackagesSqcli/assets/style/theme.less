// -------- Colors -----------
@white: #fff;
@black: #000;

/******************************/
// 颜色变量
/*****************************/
@primary_main: #1872F0;
@primary_light: #ADCCFF;
@primary_lightest: #F1F8FF;
@neutral-0: #FFFFFF;
@brand_main: #5A53FE;


@primary-100: #ffe8e7;
@primary-200: #f7a6a3;
@primary-300: #eb7575;
@primary-400: #de4b50;
@primary-500: #d92129;
@primary-600: #850b1b;
@primary-700: #5e0312;
@primary-800: #38010c;

@secondary-50: #C4C7CA;
@secondary-100: #737578;
@secondary-200: #fff47a;
@secondary-300: #2C2E30;
@secondary-400: #1D1F20;
@secondary-500: #ecb205;
@secondary-600: #d1a400;
@secondary-700: #ab8000;
@secondary-800: #855f00;

@gray-100: #f5f5f5;
@gray-200: #d9d9d9;
@gray-300: #a3a3a3;
@gray-400: #7a7a7a;
@gray-500: #666666;
@gray-600: #333333;
@gray-700: #191919;
@gray-800: #000000;

@success-dark: #24A743;
@sqcli-error-dark: #DC2626;
@error-main: #F3564B;
@warning-dark: #EC6F1E;
@warning-main: #FB7A28;
@success-400: #5bbc72;
@success-200: #d6f3e2;
@warning-400: #fead0c;
@warning-200: #fffae6;
@error-400: #f5142b;
@error-200: #ffe6e6;
@link-500: #2c5de5;
@link-200: #abcaff;
@red-400: #e83f4e;
@red-200: #ffd6d7;
@red-100: #ffeff0;
@orange-400: #ee843e;
@orange-200: #ffe1be;
@orange-100: #fff1e2;
@green-400: #5bbc72;
@green-200: #d6f3e2;
@green-100: #e9fbf1;
@blue-400: #5584f2;
@blue-200: #abcaff;
@blue-100: #edf4ff;
@cyan-400: #19bee6;
@cyan-200: #98ecf5;
@cyan-100: #d3f9fb;
@purple-400: #9c5bfc;
@purple-200: #e5d5fc;
@purple-100: #f6f0ff;
@bg-container: #ffffff;

/******************************/
// 间距大小
/*****************************/
@spacing-8: '8px';
@spacing-16: '16px';
@spacing-24: '24px';
@spacing-32: '32px';
@spacing-40: '40px';
@spacing-48: '48px';
@spacing-56: '56px';
@spacing-64: '64px';
@spacing-72: '72px';
@spacing-80: '80px';

/******************************/
// 字体大小
/*****************************/
@font-size-xs: 10px;
@font-size-sm: 12px;
@font-size-base: 14px;
@font-size-lg: 16px;
@font-size-xl: 20px;
@font-size-2xl: 24px;
@font-size-3xl: 32px;
@font-size-4xl: 40px;
@font-size-5xl: 56px;
@font-size-6xl: 72px;

/******************************/
// 字重
/*****************************/
@font-weight-normal: 400;
@font-weight-semibold: 600;
@font-weight-block: 900;

/******************************/
// 字体样式
/*****************************/
@font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'Helvetica',
  'Arial', 'PingFang SC', 'Hiragino Sans GB', 'Heiti SC', 'Microsoft YaHei',
  'WenQuanYi Micro Hei', 'ui-sans-serif', 'system-ui';

/******************************/
// 阴影
/*****************************/
@shadow-bottom-large: 0px 10px 16px 0px rgba(80, 84, 94, 0.1),
  0px 4px 6px 0px rgba(80, 84, 94, 0.06);
@shadow-bottom-medium: 0px 4px 6px 0px rgba(80, 84, 94, 0.1),
  0px 2px 4px 0px rgba(80, 84, 94, 0.06);
@shadow-bottom-small: 0px 1px 2px 0px rgba(80, 84, 94, 0.04),
  0px 1px 4px 0px rgba(80, 84, 94, 0.1);

@shadow-left-large: -10px 0px 16px 0px rgba(80, 84, 94, 0.1),
  -4px 0px 6px 0px rgba(80, 84, 94, 0.06);
@shadow-left-medium: -4px 0px 6px 0px rgba(80, 84, 94, 0.1),
  -2px 0px 4px 0px rgba(80, 84, 94, 0.06);
@shadow-left-small: -1px 0px 2px 0px rgba(80, 84, 94, 0.04),
  -1px 0px 4px 0px rgba(80, 84, 94, 0.1);

@shadow-right-large: 10px 0px 16px 0px rgba(80, 84, 94, 0.1),
  4px 0px 6px 0px rgba(80, 84, 94, 0.06);
@shadow-right-medium: 4px 0px 6px 0px rgba(80, 84, 94, 0.1),
  2px 0px 4px 0px rgba(80, 84, 94, 0.06);
@shadow-right-small: 1px 0px 2px 0px rgba(80, 84, 94, 0.04),
  1px 0px 4px 0px rgba(80, 84, 94, 0.1);

/******************************/
// 圆角
/*****************************/
@border-radius-base: 6px;
@border-radius-none: 0;
@border-radius-sm: @border-radius-base;
@border-radius-lg: 8px;
@border-radius-full: 999px;

@text-primary: #1D1F20;
@text-secondary: #404245;
@text-hint: #737578;
@border-color: #E7E8E9;