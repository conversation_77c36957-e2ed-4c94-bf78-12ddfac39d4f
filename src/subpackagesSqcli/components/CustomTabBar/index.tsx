// src/components/CustomTabBar/index.tsx
import Taro from '@tarojs/taro';
import { View, Image, Text } from '@tarojs/components';
import Icon1Disabled from '@/subpackagesSqcli/assets/img/Icon1-disabled.png';
import Icon1 from '@/subpackagesSqcli/assets/img/Icon1.png';
import Icon2Disabled from '@/subpackagesSqcli/assets/img/Icon2-disabled.png';
import Icon2 from '@/subpackagesSqcli/assets/img/Icon2.png';
import './index.less';

const tabList = [
  {
    pagePath: '/subpackagesSqcli/pages/index/index',
    text: '锂电池申报',
    iconPath: Icon1Disabled,
    selectedIconPath: Icon1,
  },
  {
    pagePath: '/subpackagesSqcli/pages/user-center/user-center',
    text: '个人中心',
    iconPath: Icon2Disabled,
    selectedIconPath: Icon2,
  },
];

/**
 * 自定义底部标签栏组件。
 *
 * 此组件根据当前页面路径动态渲染底部标签栏，包含多个选项卡。
 * 每个选项卡显示一个图标和文本标签，并根据当前活动页面高亮显示。
 * 选项卡可点击，点击后通过 `Taro.reLaunch` 跳转到对应页面。
 */

export default function CustomTabBar() {
  const currentPage = Taro.getCurrentInstance().router?.path || '';
  return (
    <View className="custom-tabbar">
      {tabList.map(item => {
        const isActive = currentPage.includes(item.pagePath);
        return (
          <View
            className="tab-item"
            key={item.pagePath}
            onClick={() => Taro.reLaunch({ url: item.pagePath })}
          >
            <Image
              src={isActive ? item.selectedIconPath : item.iconPath}
              className="icon"
            />
            <Text className={`label ${isActive ? 'active' : ''}`}>
              {item.text}
            </Text>
          </View>
        );
      })}
    </View>
  );
}
