import { useRef } from 'react';
import { Signature, Button, Dialog } from '@nutui/nutui-react-taro';
import Taro from '@tarojs/taro';
import './index.less';

const Sign = ({ visible, setVisible, onConfirm }) => {
  const signatureRef = useRef<any>(null);
  const confirm = (dataurl: string, isSigned?: boolean) => {
    if (isSigned) {
      const base64 = Taro.getFileSystemManager().readFileSync(
        dataurl,
        'base64',
      );
      const res = 'data:image/jpeg;base64,' + base64;
      onConfirm(res, dataurl);
      setVisible(false);
    } else {
      Taro.showToast({
        title: '请先签字',
        icon: 'none',
      });
    }
  };
  const clear = () => {
    console.log('清除事件');
  };
  const handleClear = () => {
    if (signatureRef.current) {
      signatureRef.current.clear();
    }
  };
  const handleSubmit = () => {
    signatureRef.current?.confirm();
  };

  return (
    <>
      <div className="Signclass">
        <Dialog
          title=""
          visible={visible}
          footer={null}
          onClose={() => setVisible(false)}
        >
          <div className="Signclass-content">
            <div onMouseMove={e => e.preventDefault()}>
              <Signature
                style={{ width: '100%', height: '100vh' }}
                onConfirm={confirm}
                onClear={clear}
                ref={signatureRef}
              />
            </div>

            <div className="footer-btn">
              <Button
                type="default"
                size="small"
                onClick={handleClear}
                className="default-btn"
              >
                重签
              </Button>
              <Button
                type="primary"
                size="small"
                onClick={handleSubmit}
                className="primary-btn"
              >
                确认
              </Button>
            </div>
          </div>
        </Dialog>
      </div>
    </>
  );
};
export default Sign;
