import { Text, View } from '@tarojs/components';
import { Button } from '@nutui/nutui-react-taro';
import './index.less';

export interface BtnI {
  text: string | React.ReactNode;
  handle?: () => void;
  icon?: string;
  className?: string;
  disabled?: boolean;
  size?: 'large' | 'medium' | 'small' | 'mini';
}

const FootOpt = (props: { btns: Array<BtnI>; isFormBtn?: boolean }) => {
  const { btns, isFormBtn } = props;
  return (
    <View className="foot-opt">
      {btns.map((item, index) => {
        const { handle, text, icon } = item;
        return (
          <Button
            disabled={item.disabled || false}
            key={index}
            type={index === btns.length - 1 ? 'primary' : 'default'}
            formType={
              index === btns.length - 1 && isFormBtn ? 'submit' : undefined
            }
            className={`btn ${
              index === btns.length - 1 ? 'primary-btn' : 'default-btn'
            } ${item.className}`}
            onClick={() => {
              handle && handle();
            }}
            size="large"
            shape="square"
          >
            <Text
              className={icon}
              style={{ fontSize: '14px', marginRight: '4px' }}
            />
            {text}
          </Button>
        );
      })}
    </View>
  );
};

export default FootOpt;
