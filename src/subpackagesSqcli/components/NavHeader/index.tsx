import { View, Text } from '@tarojs/components';
import React, { ReactNode, useState } from 'react';
import Taro, { useReady } from '@tarojs/taro';
import './index.less';

interface PropsInterface {
  children: ReactNode;
  title: string;
  background?: string;
  contenStyle?: any;
  headerStyle?: any;
  onClick?: () => void;
  showBack?: boolean;
  icon?: React.ReactNode;
}

const NavHeader = (props: PropsInterface) => {
  const { showBack = true } = props;
  const [navBarHeight, setNavBarHeight] = useState(0); // 导航栏高度
  const [menuTop, setMenuTop] = useState(0); // 胶囊距底部间距
  const [menuHeight, setMenuHeight] = useState(0); // 胶囊高度
  useReady(() => {
    const systemInfo = Taro.getSystemInfoSync();
    const menuButtonInfo = Taro.getMenuButtonBoundingClientRect(); //胶囊相关信息
    setMenuTop(menuButtonInfo.top);
    setNavBarHeight(
      (menuButtonInfo.top - (systemInfo.statusBarHeight || 0)) * 2 +
        menuButtonInfo.height +
        (systemInfo.statusBarHeight || 0),
    );
    setMenuHeight(menuButtonInfo.height);
  });

  return (
    <View
      style={{ minHeight: '100vh', background: props.background }}
      className="app-layout"
    >
      <View
        className="nav-header"
        style={{
          height: navBarHeight + 'px',
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          zIndex: 999,
          ...props.headerStyle,
        }}
      >
        <View
          style={{
            position: 'absolute',
            width: '100%',
            top: menuTop + 'px',
            height: menuHeight + 'px',
            display: 'flex',
            alignItems: 'center',
          }}
        >
          {showBack && (
            <View
              className="left"
              onClick={() => {
                if (props.onClick) {
                  props.onClick();
                } else {
                  Taro.navigateBack();
                }
              }}
            >
              {props.icon ? (
                props.icon
              ) : (
                <Text className="ri-arrow-left-s-line" />
              )}
            </View>
          )}
          <View className="center">{props.title}</View>
        </View>
      </View>

      <View
        style={{
          paddingTop: navBarHeight + 'px',
          ...props.contenStyle,
        }}
      >
        {props.children}
      </View>
    </View>
  );
};

export default NavHeader;
