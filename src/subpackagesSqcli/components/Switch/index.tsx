import { View, Text } from '@tarojs/components';
import { useState } from 'react';
import './index.less';

interface SwitchProps {
  initialValue?: boolean;
  onChange?: (value: boolean) => void;
  activeText: string;
  inactiveText: string;
  activeIcon?: string | React.ReactNode;
  inactiveIcon?: string | React.ReactNode;
}

const Switch: React.FC<SwitchProps> = ({
  initialValue = false,
  onChange,
  activeIcon,
  inactiveIcon,
  activeText,
  inactiveText,
}) => {
  const [isChecked, setIsChecked] = useState(initialValue);
  const handleToggle = () => {
    const newValue = !isChecked;
    setIsChecked(newValue);
    if (onChange) {
      onChange(newValue); // 通知父组件状态变化
    }
  };
  return (
    <View
      className={`switch ${isChecked ? 'active' : ''}`}
      onClick={handleToggle}
    >
      <View className={`switch-item ${isChecked ? 'active' : ''}`}>
        {activeIcon ? (
          activeIcon
        ) : (
          <Text className="ri-check-line text-sm"></Text>
        )}
        <Text>{activeText}</Text>
      </View>
      <View className={`switch-item ${isChecked ? '' : 'active'}`}>
        {inactiveIcon ? (
          inactiveIcon
        ) : (
          <Text className="ri-close-line text-sm"></Text>
        )}
        <Text>{inactiveText}</Text>
      </View>
    </View>
  );
};

export default Switch;
