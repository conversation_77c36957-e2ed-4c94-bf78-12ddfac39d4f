.switch {
  padding: 4px;
  display: flex;
  align-items: center;
  background-color: #F3564B; /* 背景颜色 */
  border-radius: 20px;
  cursor: pointer;
  color: white;
  transition: background-color 0.2s; /* 动画效果 */
  width: fit-content;
  &.active {
    background: #3FBA5C;
    .switch-item {
      &.active {
        color: #3FBA5C;
      }
    }
  }
}
.switch-item {
  display: flex;
  align-items: center;
  padding: 0 4px;
  text-align: center;
  width: 50%;
  color: rgba(255, 255, 255, 0.60);
  font-size: 12px;
  &.active {
    background: #FFF;
    color: #F3564B; 
    border-radius: 12px;
  }
}