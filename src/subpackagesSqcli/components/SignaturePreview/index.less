// .signature-preview-bar {
//   .nut-dialog {
//     height: 90vh;
//     .nut-dialog-content {
//       height: 80vh;
//       max-height: 80vh !important;
//     }
//   }
// }

.signature-preview-container {
  .signature-image-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #ffffff; // 白色背景
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
  
  .signature-image {
    display: block;
    background-color: #ffffff; // 确保图片背景也是白色
    // max-height: 78vh;
  }
}