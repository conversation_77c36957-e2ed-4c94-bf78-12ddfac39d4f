import { FC } from 'react';
import { View, Image } from '@tarojs/components';
import { Dialog } from '@nutui/nutui-react-taro';
import './index.less';

interface SignaturePreviewProps {
  // 签名图片的URL
  imageUrl: string;
  // 是否显示预览
  visible: boolean;
  // 关闭预览的回调
  onClose: () => void;
  // 图片宽度，默认为300px
  width?: string | number;
  // 图片高度，默认为200px
  height?: string | number;
  // 弹窗标题
  title?: string;
}

const SignaturePreview: FC<SignaturePreviewProps> = ({
  imageUrl,
  visible,
  onClose,
  width = '300px',
  height = '200px',
  title = '签名预览',
}) => {
  return (
    <div className="signature-preview-bar">
      <Dialog
        title={title}
        visible={visible}
        onClose={onClose}
        closeOnOverlayClick
        lockScroll
        footer={null}
      >
        <View className="signature-preview-container">
          <View className="signature-image-wrapper">
            <Image
              src={imageUrl}
              className="signature-image"
              style={{ width, height, transform: 'rotate(-90deg)' }}
              // mode="widthFix"
              // mode="aspectFit"
              mode="heightFix"
            />
          </View>
        </View>
      </Dialog>
    </div>
  );
};

export default SignaturePreview;
