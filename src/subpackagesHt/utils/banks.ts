export default {
  J: [{ name: '交通银行', id: '1589', code: 'B<PERSON><PERSON>', spell: 'jiao<PERSON>gy<PERSON><PERSON>' }],
  X: [{ name: '西藏银行', id: '2756', code: 'XICYH', spell: 'xicangyinhang' }],
  Z: [
    {
      name: '中国工商银行',
      id: '3232',
      code: 'ICBC',
      spell: 'zhongguogongshangyinhang',
    },
    {
      name: '中国光大银行',
      id: '3233',
      code: 'CEB',
      spell: 'zhongguoguangdayinhang',
    },
    {
      name: '中国建设银行',
      id: '3234',
      code: 'CCB',
      spell: 'zhongguojiansheyinhang',
    },
    {
      name: '中国民生银行',
      id: '3236',
      code: 'CMBC',
      spell: 'zhongguominshengyinhang',
    },
    {
      name: '中国农业银行',
      id: '3238',
      code: 'ABC',
      spell: 'zhongguonongyeyinhang',
    },
    {
      name: '中国人民银行',
      id: '3239',
      code: '<PERSON><PERSON>',
      spell: 'zhonggu<PERSON><PERSON>minyinhang',
    },
    { name: '中国银行', id: '3242', code: 'B<PERSON>', spell: 'zhongguoyinhang' },
    {
      name: '中国邮政储蓄银行',
      id: '3244',
      code: 'PSBC',
      spell: 'zhongguoyouzhengchuxuyinhang',
    },
  ],
};

// var c = a.sort(({ spell: a }, { spell: b }) => {
//   const comper = i => {
//     if (a[i] && b[i]) {
//       return a[i] === b[i] ? comper(i + 1) : a[i] > b[i] ? 1 : -1;
//     } else if (a[i]) {
//       return 1;
//     } else if (b[i]) {
//       return -1;
//     } else {
//       return 0;
//     }
//   };
//   return comper(0);
// });

// [].reduce((pre, cur) => {
//   const key = cur.spell[0].toUpperCase()
//   return pre[key] ? {...pre, [key]: [...pre[key], cur]} : {...pre, [key]: [cur]}
// }, {})
