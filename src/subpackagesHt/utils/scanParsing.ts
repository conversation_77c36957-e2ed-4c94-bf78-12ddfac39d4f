/* eslint-disable no-irregular-whitespace */

import dayjs from 'dayjs';
/**
 * @description: 根据扫描枪扫描结果，获取航班信息（暂时仅适用于川航的一维码和二维码，电子登机牌通用规则）
 * @param {*} code
 * 纸质一维码示例：`3U888814 17APEK036` 长度18
 *     3U8888：航班号
 *     14：年月（不确定）日的日
 *     17A：座位号
 *     PEK：起始航站
 *     036：值机序号
 *
 * 纸质二维码示例：`M1WANGYALIN           EPCVQXN PEKCTU3U 8888 362G007D0169 100` 长度65
 *    WANGYALIN 旅客姓名
 *    PCVQXN PNR（旅客订座记录）
 *    PEK 起飞航站
 *    CTU 到达航站
 *    3U 8888 航班号
 *    169 值机序号
 *    362 今年的第几天
 *    G 舱等
 *    07D 座位号
 *
 * 电子登机牌二维码示例：`M1ZHANGSAN            EMTKH0BSZXCTU3U8706 089W020D005012B>1030MM0E87621262874370NI510902199807078322#90#91#92#9331#9430:1` 长度120
 *    ZHANGSAN 旅客姓名
 *    MTKH0B PNR（旅客订座记录）
 *    SZX 起飞航站
 *    CTU 到达航站
 *    3U8706 航班号
 *    169 值机序号
 *    089 今年的第几天
 *    W 舱等
 *    020D 座位号
 * @return {*}
 */

// 根据天数获取当年的具体日期
const digitalConversionDate = digital => {
  return dayjs()
    .startOf('year')
    .add(digital - 1, 'days')
    .format('YYYY-MM-DD');
};

export const getScanResults = code => {
  if (!code) return;
  // * 是三种码共有的信息
  const scanResults = {
    paxName: '', // 旅客姓名
    flightNo: '', // 航班号*
    serialNo: '', // 值机序号*
    orgCityAirp: '', // 起始航站三字码*
    dstCityAirp: '', // 到达航站三字码
    flightDate: '', // 航班日期*
    ticketNo: '', //票号
    seatNo: '', //座位号
  };
  if (code.length < 40) {
    //纸质一维码
    const yicodeArr = code.match(/^(.{6})\s?([^\s]{2})\s*(.+)(\w{3})(\d{3})/);
    if (!yicodeArr) {
      // Toast.info(formatMessage({ id: 'Illegitimatebarcode' }), 1);
      return null;
    }
    // 航班号 1
    // 日子 2
    // 座位号 3
    // 起始航站 4
    // 值机序号 5
    scanResults.flightNo = yicodeArr[1];
    scanResults.flightDate = dayjs().format('YYYY-MM') + '-' + yicodeArr[2];
    scanResults.orgCityAirp = yicodeArr[4];
    scanResults.serialNo = yicodeArr[5];
  }

  if (code.length > 40 && code.length < 80) {
    //纸质二维码
    code = code.replace(/\s+/g, ' '); //去除名字后面多余的空格
    const ercodeArr = code.match(
      /^..([^\s]+)\s.(\s|\w{6})\s?(\w{3})(\w{3})([^\s]+|[^\s]+\s\d+)\s([^\s]{3}).([^\s]{4})[^\s](\d{3})\s/,
    );
    // 旅客姓名 1
    // pnr 2
    // 起始航站 3
    // 到达航站 4
    // 航班号 5
    // 今年第几天 6
    // 座位号 7
    // 值机序号 8
    if (!ercodeArr) {
      // Toast.info(formatMessage({ id: 'Illegitimatebarcode' }), 1);
      return null;
    }
    scanResults.paxName = ercodeArr[1];
    scanResults.orgCityAirp = ercodeArr[3];
    scanResults.dstCityAirp = ercodeArr[4];
    scanResults.flightNo = ercodeArr[5].replace(/\s/g, '');
    scanResults.flightDate = digitalConversionDate(Number(ercodeArr[6]));
    scanResults.serialNo = ercodeArr[8];
  }

  if (code.length > 80) {
    //电子登机牌二维码
    code = code.replace(/\s+/g, ' '); //去除名字后面多余的空格
    const splitCode = code.split('>');
    const diancodeArr = splitCode[0].match(
      /^..([^\s]+)\s.(\s|\w{6})\s?(\w{3})(\w{3})([^\s]+|[^\s]+\s\d+)\s([^\s]{3}).([^\s]{4})[^\s](\d{3})(.{3,})/,
    );
    // 旅客姓名 1
    // pnr 2
    // 起始航站 3
    // 到达航站 4
    // 航班号 5
    // 今年第几天 6
    // 座位号 7
    // 值机序号 8

    // 获取票号(中英文不一致) 暂不开放
    // const ticketNoArr = splitCode[1].match(/^(.{8})([^\s]{13})/);
    // 票号 2
    if (!diancodeArr) {
      // Toast.info(formatMessage({ id: 'Illegitimatebarcode' }), 1);
      return null;
    }
    scanResults.paxName = diancodeArr[1];
    scanResults.orgCityAirp = diancodeArr[3];
    scanResults.dstCityAirp = diancodeArr[4];
    scanResults.flightNo = diancodeArr[5].replace(/\s/g, '');
    scanResults.flightDate = digitalConversionDate(Number(diancodeArr[6]));
    scanResults.serialNo = diancodeArr[8];
    scanResults.seatNo = diancodeArr[7];
    // scanResults.ticketNo = ticketNoArr[2];
  }

  // 假设 当前是某月最后一天 且 扫描结果解析出日期为01 且 扫描结果解析出月份不为次月 则判定该次扫码为无效
  if (
    dayjs().isSame(dayjs().endOf('month'), 'day') &&
    dayjs(scanResults.flightDate).format('DD') === '01' &&
    Number(dayjs(scanResults.flightDate).format('MM')) !==
      Number(dayjs().format('MM')) + 1
  ) {
    return;
  } else {
    return scanResults;
  }
};

// 川航二维码示例
// M1QILIJUN            EMC3V4LHAKCTUHU7085 005X054D011612B>1030MM0E88034078842000NI510902199507078322#90#91#92#9324#9405:1
// M1QILIJUN            EMTKH0BSZXCTU3U8706 089W020D005012B>1030MM0E87621262874370NI510902199507078322#90#91#92#9331#9430:1
// M1QILIJUN            EPJ84BXTNACTUSC8076 184E016F001912B>1030MM0E32424703899110NI510902199507078322#90#91#92#9314#9417:
// M1QILIJUN            EPCMQV6CTUTNA3U8557 195R048K001012B>1030MM0E87621497690580NI510902199507078322#90#91#92#9325#9411:
// M1YUJIANXIA          E      CTUSYXHU7096 339G055H003912B>1030MM0E88023360727970NI511324199502223554#90#91#92#93150#9421
// M1LIYUTING            E       CTUSYXHU 7096 339G055K0038 12B>1030MM0E88023360727960NI511102199410017324#90æŽé›¨å©·#91æˆéƒ½#92ä¸‰äºš#93150#9421:00
// M1DANGZHENFENG        EMXELTV PEKCTU3U 8882 250H051K0022 12B>1030MM0E87621391676140NI513701199007200055
// M1DANGZHENFENG        EMLCSKE CTUSYXTV 9805 081T037L0022 12B>1030MM0E08821384075580NI513701199007200055#90å…šæŒ¯å³°#91æˆéƒ½#92ä¸‰äºš#93176#9422: 0
// M1DANGZHENFENG        ENT6KWE CTUPEK3U 8885 245T060K0161 12B>1030MM0E87621391674470NI513701199007200055#90å…šæŒ¯å³°#91æˆéƒ½#92åŒ—äº¬#93112#9402: 1

// 新的国航二维码示例
// M1LIUXIAOJUN          EMLT70Y PEKCTU3U 8882 060L031K0020 147>3180W 1060B3U              298762159442859003U 3U 690390260        20K
// M1HUGUIPING           ENC4Z1D PEKCTU3U 8882 060J002A0015 147>3180W 1060B3U              298762159442902003U 3U 201190625        40K
// M1ZHANGCHUHONG        EMDZW61 PEKCTU3U 8882 060A002D0050 147>3180W 1060B3U              298762159425552003U 3U 958892336        20K
// M1YINZONGYI           EPZSDNK PEKCTU3U 8890 060N035J0046 147>3181K 1060B3U              298762155615486003U 3U 720366931        20K
// M1LIQI                ENTZMHZ PEKCTU3U 8892 060E031K0100 147>3181O 1060B3U              298762155614233003U 3U 355744852        20K
// M1DENGYONG            EPXHW7H PEKCTU3U 8882 060G032A0166 147>3181O 1060B3U              298769087052381003U 3U 201376663        20K
