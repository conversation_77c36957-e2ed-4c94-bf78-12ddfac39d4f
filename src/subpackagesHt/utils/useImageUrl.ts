import { useState, useEffect } from 'react';
import Taro from '@tarojs/taro';
import request from '@ht/api/apiConfig';
import { getImageUrl } from './image';

// 图片缓存
const imageCache = new Map<string, string>();
const loadingPromises = new Map<string, Promise<string>>();

/**
 * 将 ArrayBuffer 转换为临时文件路径
 * @param arrayBuffer 文件的二进制数据
 * @param fileName 文件名
 * @returns Promise<string> 临时文件路径
 */
const arrayBufferToTempFile = (
  arrayBuffer: ArrayBuffer,
  fileName: string,
): Promise<string> => {
  return new Promise((resolve, reject) => {
    const fileTempPath = `${Taro.env.USER_DATA_PATH}/${fileName}`;
    const fs = Taro.getFileSystemManager();

    fs.writeFile({
      filePath: fileTempPath,
      data: arrayBuffer,
      encoding: 'binary',
      success: () => {
        resolve(fileTempPath);
      },
      fail: err => {
        reject(err);
      },
    });
  });
};

/**
 * 根据文件ID获取图片URL
 * @param fileId 文件ID
 * @returns Promise<string> 图片URL
 */
const getImageUrlFromFileId = async (fileId: string): Promise<string> => {
  // 下载文件
  const arrayBuffer = await request({
    path: `/uc/api/file/download?fileId=${fileId}`,
    method: 'GET',
    responseType: 'arraybuffer',
    isFile: true,
  });

  // 转换为临时文件
  const tempFilePath = await arrayBufferToTempFile(
    arrayBuffer,
    `image_${fileId}.png`,
  );

  return tempFilePath;
};

/**
 * 获取缓存的图片URL，如果不存在则下载并缓存
 * @param fileId 文件ID
 * @returns Promise<string> 图片URL
 */
const getCachedImageUrl = async (fileId: string): Promise<string> => {
  // 检查缓存
  if (imageCache.has(fileId)) {
    return imageCache.get(fileId)!;
  }

  // 检查是否正在加载
  if (loadingPromises.has(fileId)) {
    return loadingPromises.get(fileId)!;
  }

  // 开始加载
  const loadingPromise = getImageUrlFromFileId(fileId);
  loadingPromises.set(fileId, loadingPromise);

  try {
    const url = await loadingPromise;
    imageCache.set(fileId, url);
    return url;
  } finally {
    loadingPromises.delete(fileId);
  }
};

/**
 * 自定义 Hook：处理图片 URL 转换
 * @param imageSource 图片源，可能是文件ID或者已经是URL
 * @param fallbackImage 备用图片名称
 * @returns 图片URL和加载状态
 */
export const useImageUrl = (
  imageSource?: string,
  fallbackImage: string = 'card_bg.svg',
) => {
  const [imageUrl, setImageUrl] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadImage = async () => {
      // 如果没有图片源，使用备用图片
      if (!imageSource) {
        setImageUrl(getImageUrl(fallbackImage));
        return;
      }

      // 如果图片源已经是完整的URL（包含http或者是本地路径），直接使用
      if (
        imageSource.startsWith('http') ||
        imageSource.startsWith('/') ||
        imageSource.includes('://')
      ) {
        setImageUrl(imageSource);
        return;
      }

      // 如果是文件ID，需要下载转换
      setLoading(true);
      setError(null);

      try {
        const url = await getCachedImageUrl(imageSource);
        setImageUrl(url);
      } catch (err) {
        // console.error('加载图片失败:', err);
        setError(err instanceof Error ? err.message : '加载图片失败');
        // 加载失败时使用备用图片
        setImageUrl(getImageUrl(fallbackImage));
      } finally {
        setLoading(false);
      }
    };

    loadImage();
  }, [imageSource, fallbackImage]);

  return {
    imageUrl,
    loading,
    error,
  };
};
