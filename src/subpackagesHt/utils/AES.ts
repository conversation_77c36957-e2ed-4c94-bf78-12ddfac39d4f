import CryptoJS from 'crypto-es';

export class AES {
  //密钥
  static key = CryptoJS.enc.Utf8.parse('eupsi#2020swcare');
  //密钥偏移量
  static iv = CryptoJS.enc.Utf8.parse('s$eupsi@2020trav');

  static encrypt = (str: string): string => {
    let data = '';
    if (str) {
      const srcs = CryptoJS.enc.Utf8.parse(str);
      const encrypted = CryptoJS.AES.encrypt(srcs, AES.key, {
        iv: AES.iv,
        mode: CryptoJS.mode.CBC,
        padding: CryptoJS.pad.Pkcs7,
      });
      data = encrypted.toString();
    }
    return data;
  };

  static decrypt = <T>(str: T): string => {
    let data = '';
    if (!str) return data;

    try {
      // 检查是否有v1_前缀，如果有则去除
      let encryptedStr = String(str);
      if (encryptedStr.startsWith('v1_')) {
        encryptedStr = encryptedStr.substring(3); // 去除v1_前缀
      }

      // 确保字符串是有效的Base64格式
      // 有些Base64字符串可能包含URL安全字符，需要替换回标准Base64字符
      encryptedStr = encryptedStr.replace(/-/g, '+').replace(/_/g, '/');

      // 尝试解密
      let decrypt = CryptoJS.AES.decrypt(encryptedStr, AES.key, {
        iv: AES.iv,
        mode: CryptoJS.mode.CBC,
        padding: CryptoJS.pad.Pkcs7,
      });

      // 转换为UTF-8字符串
      data = decrypt.toString(CryptoJS.enc.Utf8);

      // 如果解密后的数据为空，可能是格式问题，尝试使用不同的方式
      if (!data) {
        // 尝试使用另一种方式解析Base64
        const wordArray = CryptoJS.enc.Base64.parse(encryptedStr);
        decrypt = CryptoJS.AES.decrypt({ ciphertext: wordArray }, AES.key, {
          iv: AES.iv,
          mode: CryptoJS.mode.CBC,
          padding: CryptoJS.pad.Pkcs7,
        });
        data = decrypt.toString(CryptoJS.enc.Utf8);
      }

      // 如果解密后仍然为空，返回原始字符串
      if (!data) {
        // console.warn('AES解密结果为空，返回原始字符串:', str);
        return String(str);
      }
    } catch (error) {
      // console.error('AES解密失败:', error, '原始字符串:', str);
      // 解密失败时返回原字符串
      return String(str);
    }

    return data;
  };
}
