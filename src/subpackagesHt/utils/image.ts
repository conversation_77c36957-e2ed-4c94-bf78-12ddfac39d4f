/**
 * 获取图片路径
 * @param imageName 图片文件名（带扩展名）
 * @returns 图片路径字符串
 */
export const getImageUrl = (imageName: string) => {
  if (__HT_ASSETS_URL__) {
    // 线上环境
    return `${__HT_ASSETS_URL__}/${imageName}`;
  }

  // H5 本地图片直接 require
  try {
    return require(`@ht/assets/${imageName}`);
  } catch (error) {
    // console.error(`Failed to load image: @ht/assets/${imageName}`, error);
    return '';
  }
};
