/**
 * 订单相关工具函数
 */
import dayjs from 'dayjs';

/**
 * 订单状态类型
 */
export type OrderStatus =
  | '待支付'
  | '已取消'
  | '待使用'
  | '退款中'
  | '已退款'
  | '使用中'
  | '已使用'
  | '退款失败';

/**
 * 订单类型
 */
export type OrderType = '旅客高舱' | '员工次卡';

/**
 * 获取订单状态文本
 * @param status 订单状态码：1-待支付，2-已取消，3-待使用，4-退款中，5-已退款，6-使用中，7-已使用，8-退款失败
 * @returns 订单状态文本
 */
export const getStatusText = (status?: number): OrderStatus => {
  switch (status) {
    case 1:
      return '待支付';
    case 2:
      return '已取消';
    case 3:
      return '待使用';
    case 4:
      return '退款中';
    case 5:
      return '已退款';
    case 6:
      return '使用中';
    case 7:
      return '已使用';
    case 8:
      return '退款失败';
    default:
      return '待使用';
  }
};

/**
 * 获取订单状态对应的颜色类名
 * @param status 订单状态码：1-待支付，2-已取消，3-待使用，4-退款中，5-已退款，6-使用中，7-已使用，8-退款失败
 * @returns 订单状态颜色类名
 */
export const getStatusColorClass = (status?: number): string => {
  switch (status) {
    case 1: // 待支付
      return 'status-pending-payment';
    case 2: // 已取消
      return 'status-cancelled';
    case 3: // 待使用
      return 'status-pending-use';
    case 4: // 退款中
      return 'status-refunding';
    case 5: // 已退款
      return 'status-refunded';
    case 6: // 使用中
      return 'status-using';
    case 7: // 已使用
      return 'status-used';
    case 8: // 退款失败
      return 'status-refund-failed';
    default:
      return 'status-pending-use';
  }
};

/**
 * 获取订单类型文本
 * @param type 订单类型码：1-员工次卡，2-旅客高舱
 * @returns 订单类型文本
 */
export const getOrderTypeText = (type?: number): OrderType => {
  return type === 1 ? '员工次卡' : '旅客高舱';
};

/**
 * 格式化日期时间
 * @param dateTime 日期时间字符串
 * @returns 格式化后的日期时间字符串，格式：YYYY-MM-DD HH:mm
 */
export const formatDateTime = (dateTime?: string): string => {
  if (!dateTime) return '';

  try {
    return dayjs(dateTime).format('YYYY-MM-DD HH:mm');
  } catch (error) {
    return dateTime;
  }
};
