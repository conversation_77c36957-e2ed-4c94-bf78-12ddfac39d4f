import { useEffect } from 'react';
import { useMenuStore } from '@ht/store/menuStore';

// 用于跟踪全局初始化状态
let isGlobalInitialized = false;

/**
 * 菜单信息自定义Hook
 * 使用zustand管理菜单数据，替代原来的本地存储方式
 */
export const useMenu = () => {
  // 从zustand store中获取状态和方法
  const { menuData, loading, fetchMenuData, hasPermission, findMenuByPath } =
    useMenuStore();

  // 初始化时获取菜单数据，只执行一次
  useEffect(() => {
    // 如果全局已初始化，则不再重复请求
    if (!isGlobalInitialized) {
      isGlobalInitialized = true;

      // 调用fetchMenuData
      const fetchData = async () => {
        try {
          await fetchMenuData();
          // 如果fetchMenuData内部有错误但没有抛出，我们可以通过检查store状态来判断是否成功
          const { initialized } = useMenuStore.getState();
          if (!initialized) {
            // 如果初始化失败，将isGlobalInitialized设置为false
            isGlobalInitialized = false;
            // console.error('获取菜单数据失败: 初始化状态为false');
          }
        } catch (error) {
          // console.error('获取菜单数据失败:', error);
          // 当fetchMenuData失败时，将isGlobalInitialized设置为false
          isGlobalInitialized = false;
        }
      };

      fetchData();
    }
  }, []);

  return {
    menuData,
    loading,
    refreshMenuData: () => fetchMenuData(true), // 提供刷新方法，强制从接口获取
    hasPermission, // 提供检查权限的方法
    findMenuByPath, // 提供查找菜单的方法
  };
};
