import { View, Text, Image } from '@tarojs/components';
import { getImageUrl } from '@/subpackagesHt/utils/image';
import { useImageUrl } from '@/subpackagesHt/utils/useImageUrl';
import { ProductDisplay } from '../../types/product';
import './index.less';

interface CardItemProps {
  product: ProductDisplay;
  onClick?: (product: ProductDisplay) => void;
}

/**
 * 产品卡片组件
 * @param props 组件属性
 * @returns 卡片组件
 */
const CardItem: React.FC<CardItemProps> = ({ product, onClick }) => {
  const { imageUrl, loading } = useImageUrl(product.image, 'card_bg.svg');

  const handleClick = () => {
    onClick && onClick(product);
  };

  return (
    <View className="card-item" onClick={handleClick}>
      <View className="card-left">
        <View className="card-bg">
          <Image className="card-bg-image" src={imageUrl} mode="aspectFill" />
          {loading && (
            <View className="card-loading">
              <Text className="loading-text">加载中...</Text>
            </View>
          )}
          <Image
            className="card-v-image"
            src={getImageUrl('card_v.svg')}
            mode="aspectFill"
          />
        </View>
      </View>
      <View className="card-right">
        <View className="card-info">
          <Text className="card-title">{product.productName}</Text>
          <View className="card-tags">
            {product.tags.map(tag => (
              <View key={tag.id} className="card-tag">
                <Text className="card-tag-text">{tag.name}</Text>
              </View>
            ))}
          </View>
        </View>
        <View className="card-price">
          <Text className="price-symbol">¥</Text>
          <Text className="price-value">{product.price}</Text>
        </View>
      </View>
    </View>
  );
};

export default CardItem;
