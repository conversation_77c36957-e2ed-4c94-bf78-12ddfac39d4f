.card-item {
  display: flex;
  align-items: center;
  background-color: #ffffff;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.card-left {
  margin-right: 20px;
}

.card-bg {
  position: relative;
  width: 80px;
  height: 80px;
  border-radius: 4px;
  overflow: hidden;
}

.card-bg-image {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
}

.card-loading {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1;
}

.loading-text {
  font-size: 12px;
  color: #666666;
}

.card-v-image {
  position: absolute;
  width: 40px;
  height: 40px;
  right: 0;
  bottom: 0;
  opacity: 0.1;
}

.card-right {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.card-title {
  font-size: 14px;
  font-weight: 500;
  color: #333333;
  line-height: 1.5;
}

.card-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.card-tag {
  padding: 0 4px;
  border-radius: 4px;
  border: 1px solid #e7e8e9;
  background-color: #ffffff;
}

.card-tag-text {
  font-size: 12px;
  color: #404245;
  opacity: 0.8;
  line-height: 1.6;
}

.card-price {
  display: flex;
  align-items: baseline;
}

.price-symbol {
  font-size: 14px;
  font-weight: 500;
  color: #f84f2a;
  margin-right: 2px;
}

.price-value {
  font-size: 20px;
  font-weight: 500;
  color: #f84f2a;
  line-height: 1.4;
}
