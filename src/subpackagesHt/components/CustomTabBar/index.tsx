// src/components/CustomTabBar/index.tsx
import Taro from '@tarojs/taro';
import { View, Image, Text } from '@tarojs/components';
import { useMenu } from '@ht/utils/hooks/useMenu';
import { getImageUrl } from '@/subpackagesHt/utils/image';
import './index.less';

// 定义标签栏项目的接口
interface TabItem {
  pagePath: string;
  text: string;
  iconType: string;
  iconPath: string;
  activeIconPath: string;
  permission?: string; // 权限码，可选
}

const tabList: TabItem[] = [
  {
    pagePath: '/subpackagesHt/pages/home/<USER>',
    text: '高舱购',
    iconType: 'seat_fill',
    iconPath: getImageUrl('seat_line.svg'),
    activeIconPath: getImageUrl('seat_fill.svg'),
    permission: 'buy_menu', // 高舱购权限码
  },
  {
    pagePath: '/subpackagesHt/pages/verification/index',
    text: '核销',
    iconType: 'qr-scan',
    iconPath: getImageUrl('qr-scan-2-line.svg'),
    activeIconPath: getImageUrl('qr-scan-2-fill.svg'),
    permission: 'check_menu', // 核销权限码
  },
  {
    pagePath: '/subpackagesHt/pages/me/index',
    text: '我的',
    iconType: 'user',
    iconPath: getImageUrl('user-line.svg'),
    activeIconPath: getImageUrl('user-fill.svg'),
    permission: 'my_menu', // 我的权限码
  },
];

/**
 * 自定义底部标签栏组件。
 *
 * 此组件根据当前页面路径动态渲染底部标签栏，包含多个选项卡。
 * 每个选项卡显示一个图标和文本标签，并根据当前活动页面高亮显示。
 * 选项卡可点击，点击后通过 `Taro.reLaunch` 跳转到对应页面。
 *
 * 组件使用 useMenu 中的 hasPermission 函数来控制菜单项的显示权限：
 * - 高舱购: buy_menu
 * - 核销: check_menu
 * - 我的: my_menu
 */

export default function CustomTabBar() {
  const currentPage = Taro.getCurrentInstance().router?.path || '';
  // 使用 useMenu 获取 hasPermission 函数
  const { hasPermission } = useMenu();

  // 根据图标类型渲染图标
  const renderIcon = (item: TabItem, isActive: boolean) => {
    return (
      <View className="icon-container">
        <Image
          className="tab-icon"
          src={isActive ? item.activeIconPath : item.iconPath}
          mode="aspectFit"
        />
      </View>
    );
  };

  // 过滤有权限的菜单项
  const filteredTabList = tabList.filter(item => {
    // 如果没有定义权限码，默认显示
    if (!item.permission) return true;
    // 根据权限码判断是否有权限
    return hasPermission(item.permission);
  });

  return (
    <View className="custom-tabbar">
      <View className="tabbar-content">
        {filteredTabList.map(item => {
          const isActive = currentPage.includes(item.pagePath);
          return (
            <View
              className="tab-item"
              key={item.pagePath}
              onClick={() => Taro.reLaunch({ url: item.pagePath })}
            >
              {renderIcon(item, isActive)}
              <Text className={`tab-text ${isActive ? 'active' : ''}`}>
                {item.text}
              </Text>
            </View>
          );
        })}
      </View>
    </View>
  );
}
