.custom-tabbar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #ffffff;
  display: flex;
  flex-direction: column;
  border-top: 1px solid #e7e8e9;
  z-index: 1000;
  box-shadow: 0px -1px 4px rgba(0, 0, 0, 0.04);
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}

.tabbar-content {
  height: 56px;
  display: flex;
  justify-content: space-around;
  align-items: center;
  padding: 0 16px;
}

.tab-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex: 1;
  height: 100%;
  padding: 8px 0;
}

.icon-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 24px;
  width: 24px;
  margin-bottom: 4px;
}

.tab-icon {
  width: 24px;
  height: 24px;
}

.tab-text {
  font-size: 10px;
  line-height: 1.8;
  color: #737578; /* 默认颜色 */
  font-family: 'MiSans', sans-serif;
}

.tab-text.active {
  color: #1872f0; /* 激活状态颜色 */
}
