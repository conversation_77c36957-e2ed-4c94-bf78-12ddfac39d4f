/* 导航栏容器 */
.nav-bar-container {
  width: 100%;
  position: relative;
}

/* 导航栏 */
.nav-bar {
  width: 100%;
  background-color: #ffffff;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1000;
  display: flex;
  flex-direction: column;
}

/* 导航内容区 */
.nav-content {
  height: 44px;
  display: flex;
  align-items: center;
  padding: 0 16px;
}

/* 左侧区域 */
.nav-left {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

/* 中间区域 */
.nav-center {
  flex: 2;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 右侧区域 */
.nav-right {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

/* 标题文本 */
.title-text {
  font-size: 16px;
  font-weight: 500;
  color: #1d1f20;
  text-align: center;
  font-family: 'MiSans', sans-serif;
  line-height: 1.5em;
}

/* 返回按钮 */
.back-button {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

/* 返回图标 */
.back-icon {
  width: 20px;
  height: 20px;
}

/* 透明背景导航栏 */
.nav-bar-transparent {
  background-color: transparent;
}

.nav-bar-transparent .title-text {
  color: #1d1f20;
  font-weight: 600;
}

.nav-bar-transparent .back-button {
  width: 24px;
  height: 24px;
}

.nav-bar-transparent .back-icon {
  filter: none;
}
