import { View, Text, Image } from '@tarojs/components';
import { useMemo } from 'react';
import { getSystemInfoSync, navigateBack } from '@tarojs/taro';
import { getImageUrl } from '@/subpackagesHt/utils/image';
import './index.less';

interface NavBarProps {
  title: string;
  showBack?: boolean;
  onBack?: () => void;
  rightContent?: React.ReactNode;
  transparent?: boolean; // 是否使用透明背景
}

/**
 * 自定义导航栏组件
 * @param title 标题
 * @param showBack 是否显示返回按钮
 * @param onBack 返回按钮点击事件
 * @param rightContent 右侧内容
 * @param transparent 是否使用透明背景（默认开启）
 */
const NavBar: React.FC<NavBarProps> = ({
  title,
  showBack = true,
  onBack,
  rightContent,
  transparent = true, // 默认开启透明背景
}) => {
  // 获取系统信息，计算状态栏高度
  const { statusBarHeight, navBarHeight } = useMemo(() => {
    const systemInfo = getSystemInfoSync();
    const sysStatusBarHeight = systemInfo.statusBarHeight || 20;
    // 导航栏高度：状态栏 + 44px（导航内容区高度）
    const sysNavBarHeight = sysStatusBarHeight + 44;
    return {
      statusBarHeight: sysStatusBarHeight,
      navBarHeight: sysNavBarHeight,
    };
  }, []);

  // 处理返回按钮点击
  const handleBack = () => {
    if (onBack) {
      onBack();
    } else {
      navigateBack();
    }
  };

  return (
    <View className="nav-bar-container">
      {/* 导航栏 */}
      <View
        className={`nav-bar ${transparent ? 'nav-bar-transparent' : ''}`}
        style={{ height: `${navBarHeight}px` }}
      >
        {/* 状态栏占位 */}
        <View style={{ height: `${statusBarHeight}px` }} />

        {/* 导航内容区 */}
        <View className="nav-content">
          {/* 左侧区域（返回按钮） */}
          <View className="nav-left">
            {showBack && (
              <View className="back-button" onClick={handleBack}>
                <Image
                  className="back-icon"
                  src={getImageUrl('back_icon_dark.svg')}
                  mode="aspectFit"
                />
              </View>
            )}
          </View>

          {/* 中间区域（标题） */}
          <View className="nav-center">
            <Text className="title-text">{title}</Text>
          </View>

          {/* 右侧区域（自定义内容） */}
          <View className="nav-right">{rightContent}</View>
        </View>
      </View>

      {/* 导航栏占位，防止内容被导航栏遮挡 */}
      <View style={{ height: `${navBarHeight}px` }} />
    </View>
  );
};

export default NavBar;
