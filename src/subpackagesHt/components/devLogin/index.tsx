/*
 * @Author: lengkj <EMAIL>
 * @Date: 2025-05-23 11:17:18
 * @LastEditors: lengkj <EMAIL>
 * @LastEditTime: 2025-05-27 10:58:44
 * @Description: 开发下 一键登录
 */
import Taro from '@tarojs/taro';
import request from '@ht/api/apiConfig';

import { Button, View } from '@tarojs/components';

const DevLogin = () => {
  const _login = (url: string) => {
    request({
      path: url,
      method: 'GET',
      isloading: true,
      header: {
        authorization: 'Basic cGFzc3dvcmRfYXV0aF9tb2RlOjEyMzQ1Ng==',
      },
    }).then(res => {
      Taro.clearStorageSync();

      Taro.setStorageSync('authorization', res.access_token);
      Taro.reLaunch({
        url: '/pages/index/index',
      });
    });
  };

  return (
    <View style={{ marginTop: 60, height: 200, overflowY: 'auto' }}>
      <Button
        style={{
          marginBottom: 8,
        }}
        onClick={async () => {
          _login(
            `/uaa/oauth/token?username=wanglu&password=m93Nlc%2Fi6CXH1TuMcxRQNQ%3D%3D&grant_type=password&type=`,
          );
        }}
      >
        登录 wanglu
      </Button>
      <Button
        style={{
          marginBottom: 8,
        }}
        onClick={async () => {
          _login(
            `/uaa/oauth/token?username=gaozimo&password=m93Nlc%2Fi6CXH1TuMcxRQNQ%3D%3D&grant_type=password&type=`,
          );
        }}
      >
        登录 gaozimo
      </Button>
      <Button
        style={{
          marginBottom: 8,
        }}
        onClick={async () => {
          _login(
            `/uaa/oauth/token?username=ht_admin&password=m93Nlc%2Fi6CXH1TuMcxRQNQ%3D%3D&grant_type=password&type=`,
          );
        }}
      >
        登录 ht_admin
      </Button>
      <Button
        style={{
          marginBottom: 8,
        }}
        onClick={async () => {
          _login(
            `/uaa/oauth/token?username=admin1&password=m93Nlc%2Fi6CXH1TuMcxRQNQ%3D%3D&grant_type=password&type=`,
          );
        }}
      >
        登录 admin1
      </Button>
      <Button
        style={{
          marginBottom: 8,
        }}
        onClick={async () => {
          _login(
            `/uaa/oauth/token?username=admin2&password=m93Nlc%2Fi6CXH1TuMcxRQNQ%3D%3D&grant_type=password&type=`,
          );
        }}
      >
        登录 admin2
      </Button>
      <Button
        style={{
          marginBottom: 8,
        }}
        onClick={async () => {
          _login(
            `/uaa/oauth/token?username=admin3&password=m93Nlc%2Fi6CXH1TuMcxRQNQ%3D%3D&grant_type=password&type=`,
          );
        }}
      >
        登录 admin3
      </Button>
      <Button
        style={{
          marginBottom: 8,
        }}
        onClick={async () => {
          _login(
            `/uaa/oauth/token?username=admin4&password=m93Nlc%2Fi6CXH1TuMcxRQNQ%3D%3D&grant_type=password&type=`,
          );
        }}
      >
        登录 admin4
      </Button>
      <Button
        style={{
          marginBottom: 8,
        }}
        onClick={async () => {
          _login(
            `/uaa/oauth/token?username=admin5&password=m93Nlc%2Fi6CXH1TuMcxRQNQ%3D%3D&grant_type=password&type=`,
          );
        }}
      >
        登录 admin5
      </Button>
      <Button
        style={{
          marginBottom: 8,
        }}
        onClick={async () => {
          _login(
            `/uaa/oauth/token?username=ht_buyer1&password=m93Nlc%2Fi6CXH1TuMcxRQNQ%3D%3D&grant_type=password&type=`,
          );
        }}
      >
        登录 ht_buyer1
      </Button>
      <Button
        style={{
          marginBottom: 8,
        }}
        onClick={async () => {
          _login(
            `/uaa/oauth/token?username=ht_buyer2&password=m93Nlc%2Fi6CXH1TuMcxRQNQ%3D%3D&grant_type=password&type=`,
          );
        }}
      >
        登录 ht_buyer2
      </Button>
      <Button
        style={{
          marginBottom: 8,
        }}
        onClick={async () => {
          _login(
            `/uaa/oauth/token?username=ht_buyer3&password=m93Nlc%2Fi6CXH1TuMcxRQNQ%3D%3D&grant_type=password&type=`,
          );
        }}
      >
        登录 ht_buyer3
      </Button>
      <Button
        style={{
          marginBottom: 8,
        }}
        onClick={async () => {
          _login(
            `/uaa/oauth/token?username=ht_buyer4&password=m93Nlc%2Fi6CXH1TuMcxRQNQ%3D%3D&grant_type=password&type=`,
          );
        }}
      >
        登录 ht_buyer4
      </Button>
      <Button
        style={{
          marginBottom: 8,
        }}
        onClick={async () => {
          _login(
            `/uaa/oauth/token?username=ht_buyer5&password=m93Nlc%2Fi6CXH1TuMcxRQNQ%3D%3D&grant_type=password&type=`,
          );
        }}
      >
        登录 ht_buyer5
      </Button>
      <Button
        style={{
          marginBottom: 8,
        }}
        onClick={async () => {
          _login(
            `/uaa/oauth/token?username=ht_buyer6&password=m93Nlc%2Fi6CXH1TuMcxRQNQ%3D%3D&grant_type=password&type=`,
          );
        }}
      >
        登录 ht_buyer6
      </Button>
      <Button
        style={{
          marginBottom: 8,
        }}
        onClick={async () => {
          _login(
            `/uaa/oauth/token?username=ht_test1&password=m93Nlc%2Fi6CXH1TuMcxRQNQ%3D%3D&grant_type=password&type=`,
          );
        }}
      >
        登录 ht_test1
      </Button>
      <Button
        style={{
          marginBottom: 8,
        }}
        onClick={async () => {
          _login(
            `/uaa/oauth/token?username=ht_test2&password=m93Nlc%2Fi6CXH1TuMcxRQNQ%3D%3D&grant_type=password&type=`,
          );
        }}
      >
        登录 ht_test2
      </Button>
      <Button
        style={{
          marginBottom: 8,
        }}
        onClick={() => {
          _login(
            `/uaa/oauth/token?username=ht_test3&password=m93Nlc%2Fi6CXH1TuMcxRQNQ%3D%3D&grant_type=password&type=`,
          );
        }}
      >
        登录 ht_test3
      </Button>
      <Button
        style={{
          marginBottom: 8,
        }}
        onClick={() => {
          _login(
            `/uaa/oauth/token?username=ht_test4&password=m93Nlc%2Fi6CXH1TuMcxRQNQ%3D%3D&grant_type=password&type=`,
          );
        }}
      >
        登录 ht_test4
      </Button>
      <Button
        style={{
          marginBottom: 8,
        }}
        onClick={() => {
          _login(
            `/uaa/oauth/token?username=ht_test5&password=m93Nlc%2Fi6CXH1TuMcxRQNQ%3D%3D&grant_type=password&type=`,
          );
        }}
      >
        登录 ht_test5
      </Button>
      <Button
        style={{
          marginBottom: 8,
        }}
        onClick={() => {
          _login(
            `/uaa/oauth/token?username=ht_test6&password=m93Nlc%2Fi6CXH1TuMcxRQNQ%3D%3D&grant_type=password&type=`,
          );
        }}
      >
        登录 ht_test6
      </Button>
      <Button
        style={{
          marginBottom: 8,
        }}
        onClick={() => {
          _login(
            `/uaa/oauth/token?username=ht_host1&password=m93Nlc%2Fi6CXH1TuMcxRQNQ%3D%3D&grant_type=password&type=`,
          );
        }}
      >
        登录 ht_host1
      </Button>
      <Button
        style={{
          marginBottom: 8,
        }}
        onClick={() => {
          _login(
            `/uaa/oauth/token?username=ht_host2&password=m93Nlc%2Fi6CXH1TuMcxRQNQ%3D%3D&grant_type=password&type=`,
          );
        }}
      >
        登录 ht_host2
      </Button>
      <Button
        style={{
          marginBottom: 8,
        }}
        onClick={() => {
          _login(
            `/uaa/oauth/token?username=ht_host3&password=m93Nlc%2Fi6CXH1TuMcxRQNQ%3D%3D&grant_type=password&type=`,
          );
        }}
      >
        登录 ht_host3
      </Button>
      <Button
        style={{
          marginBottom: 8,
        }}
        onClick={() => {
          _login(
            `/uaa/oauth/token?username=ht_sales2&password=m93Nlc%2Fi6CXH1TuMcxRQNQ%3D%3D&grant_type=password&type=`,
          );
        }}
      >
        登录 ht_sales2
      </Button>
      <Button
        style={{
          marginBottom: 8,
        }}
        onClick={() => {
          _login(
            `/uaa/oauth/token?username=ht_sales3&password=m93Nlc%2Fi6CXH1TuMcxRQNQ%3D%3D&grant_type=password&type=`,
          );
        }}
      >
        登录 ht_sales3
      </Button>
      <Button
        style={{
          marginBottom: 8,
        }}
        onClick={() => {
          _login(
            `/uaa/oauth/token?username=ht_sales4&password=m93Nlc%2Fi6CXH1TuMcxRQNQ%3D%3D&grant_type=password&type=`,
          );
        }}
      >
        登录 ht_sales4
      </Button>
      <Button
        style={{
          marginBottom: 8,
        }}
        onClick={() => {
          _login(
            `/uaa/oauth/token?username=ht_sales5&password=m93Nlc%2Fi6CXH1TuMcxRQNQ%3D%3D&grant_type=password&type=`,
          );
        }}
      >
        登录 ht_sales5
      </Button>
      <Button
        style={{
          marginBottom: 8,
        }}
        onClick={() => {
          _login(
            `/uaa/oauth/token?username=ht_sales6&password=m93Nlc%2Fi6CXH1TuMcxRQNQ%3D%3D&grant_type=password&type=`,
          );
        }}
      >
        登录 ht_sales6
      </Button>
      <Button
        style={{
          marginBottom: 8,
        }}
        onClick={() => {
          _login(
            `/uaa/oauth/token?username=ht_sales7&password=m93Nlc%2Fi6CXH1TuMcxRQNQ%3D%3D&grant_type=password&type=`,
          );
        }}
      >
        登录 ht_sales7
      </Button>
      <Button
        style={{
          marginBottom: 8,
        }}
        onClick={() => {
          _login(
            `/uaa/oauth/token?username=ht_sales8&password=m93Nlc%2Fi6CXH1TuMcxRQNQ%3D%3D&grant_type=password&type=`,
          );
        }}
      >
        登录 ht_sales8
      </Button>
      <Button
        style={{
          marginBottom: 8,
        }}
        onClick={() => {
          _login(
            `/uaa/oauth/token?username=ht_sales9&password=m93Nlc%2Fi6CXH1TuMcxRQNQ%3D%3D&grant_type=password&type=`,
          );
        }}
      >
        登录 ht_sales9
      </Button>
      <Button
        style={{
          marginBottom: 8,
        }}
        onClick={() => {
          _login(
            `/uaa/oauth/token?username=ht_sales10&password=m93Nlc%2Fi6CXH1TuMcxRQNQ%3D%3D&grant_type=password&type=`,
          );
        }}
      >
        登录 ht_sales10
      </Button>
      <Button
        style={{
          marginBottom: 8,
        }}
        onClick={() => {
          _login(
            `/uaa/oauth/token?username=ht_sales11&password=m93Nlc%2Fi6CXH1TuMcxRQNQ%3D%3D&grant_type=password&type=`,
          );
        }}
      >
        登录 ht_sales11
      </Button>
      <Button
        style={{
          marginBottom: 8,
        }}
        onClick={() => {
          _login(
            `/uaa/oauth/token?username=ht_sales1&password=m93Nlc%2Fi6CXH1TuMcxRQNQ%3D%3D&grant_type=password&type=`,
          );
        }}
      >
        登录 ht_sales1
      </Button>
      <Button
        style={{
          marginBottom: 8,
        }}
        onClick={() => {
          _login(
            `/uaa/oauth/token?username=ht_sales02&password=m93Nlc%2Fi6CXH1TuMcxRQNQ%3D%3D&grant_type=password&type=`,
          );
        }}
      >
        登录 ht_sales02
      </Button>
      <Button
        style={{
          marginBottom: 8,
        }}
        onClick={() => {
          _login(
            `/uaa/oauth/token?username=ht_sales03&password=m93Nlc%2Fi6CXH1TuMcxRQNQ%3D%3D&grant_type=password&type=`,
          );
        }}
      >
        登录 ht_sales03
      </Button>
      <Button
        style={{
          marginBottom: 8,
        }}
        onClick={() => {
          _login(
            `/uaa/oauth/token?username=ht_salesleader1&password=m93Nlc%2Fi6CXH1TuMcxRQNQ%3D%3D&grant_type=password&type=`,
          );
        }}
      >
        登录 ht_salesleader1
      </Button>
      <Button
        style={{
          marginBottom: 8,
        }}
        onClick={() => {
          _login(
            `/uaa/oauth/token?username=ht_salesleader2&password=m93Nlc%2Fi6CXH1TuMcxRQNQ%3D%3D&grant_type=password&type=`,
          );
        }}
      >
        登录 ht_salesleader2
      </Button>
    </View>
  );
};

export default DevLogin;
