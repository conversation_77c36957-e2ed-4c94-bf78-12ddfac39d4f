import { View, Text, Image } from '@tarojs/components';
import { ReactNode } from 'react';
import './index.less';

interface DialogProps {
  visible: boolean;
  title: string;
  description?: string;
  icon?: string;
  iconBgColor?: string;
  confirmText?: string;
  cancelText?: string;
  onConfirm?: () => void;
  onCancel?: () => void;
  children?: ReactNode;
}

export default function Dialog({
  visible,
  title,
  description,
  icon,
  iconBgColor = '#FFF3EB',
  confirmText = '确定',
  cancelText,
  onConfirm,
  onCancel,
  children,
}: DialogProps) {
  if (!visible) return null;

  return (
    <View className="dialog-overlay" onClick={onCancel}>
      <View className="dialog-container" onClick={e => e.stopPropagation()}>
        <View className="dialog-header">
          {icon && (
            <View
              className="dialog-icon-container"
              style={{ backgroundColor: iconBgColor }}
            >
              <Image className="dialog-icon" src={icon} mode="aspectFit" />
            </View>
          )}

          <View className="dialog-text-container">
            <Text className="dialog-title">{title}</Text>
            {description && (
              <Text className="dialog-description">{description}</Text>
            )}
          </View>

          {children}
        </View>

        <View className="dialog-footer">
          {cancelText && (
            <View className="dialog-button" onClick={onCancel}>
              <Text className="button-text">{cancelText}</Text>
            </View>
          )}

          <View
            className={`dialog-button ${!cancelText ? 'primary' : ''}`}
            onClick={onConfirm}
          >
            <Text className="button-text">{confirmText}</Text>
          </View>
        </View>
      </View>
    </View>
  );
}
