/* 弹窗遮罩层样式 */
.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 999;
}

.dialog-container {
  width: 320px;
  background-color: #ffffff;
  border-radius: 12px;
  box-shadow: 0px 24px 48px -12px rgba(17, 24, 39, 0.25);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.dialog-header {
  padding: 24px 24px 16px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;

  .dialog-icon-container {
    width: 40px;
    height: 40px;
    border-radius: 99px;
    background-color: #fff3eb;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 8px;

    .dialog-icon {
      width: 24px;
      height: 24px;
    }
  }

  .dialog-text-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    width: 100%;

    .dialog-title {
      font-family: 'MiSans', sans-serif;
      font-weight: 500;
      font-size: 16px;
      line-height: 1.5;
      color: #1d1f20;
      text-align: center;
    }

    .dialog-description {
      font-family: 'MiSans', sans-serif;
      font-weight: 400;
      font-size: 14px;
      line-height: 1.57;
      color: #737578;
      text-align: center;
    }
  }
}

.dialog-footer {
  padding: 10px 24px 20px;
  display: flex;
  justify-content: center;

  .dialog-button {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 9px 12px;
    background-color: #ffffff;
    border: 1px solid #e7e8e9;
    border-radius: 6px;
    box-shadow: 0px 1px 2px 0px rgba(17, 24, 39, 0.05);
    cursor: pointer;

    .button-text {
      font-family: 'MiSans', sans-serif;
      font-weight: 500;
      font-size: 14px;
      line-height: 1.57;
      color: #1d1f20;
    }

    &.primary {
      background-color: #0052d9;
      border: none;

      .button-text {
        color: #ffffff;
      }
    }
  }
}
