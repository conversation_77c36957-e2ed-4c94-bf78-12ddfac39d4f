<svg width="57" height="57" viewBox="0 0 57 57" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_ii_2141_212846)">
<path d="M43.9999 42.6555H22.9794C22.631 42.6555 22.2968 42.5171 22.0504 42.2707C21.804 42.0244 21.6656 41.6902 21.6656 41.3418V17.6937C21.6656 17.3452 21.804 17.0111 22.0504 16.7647C22.2968 16.5183 22.631 16.3799 22.9794 16.3799H43.9999C44.3484 16.3799 44.6825 16.5183 44.9289 16.7647C45.1753 17.0111 45.3137 17.3452 45.3137 17.6937V41.3418C45.3137 41.6902 45.1753 42.0244 44.9289 42.2707C44.6825 42.5171 44.3484 42.6555 43.9999 42.6555Z" fill="#1466E1"/>
<path d="M43.9999 42.6555H22.9794C22.631 42.6555 22.2968 42.5171 22.0504 42.2707C21.804 42.0244 21.6656 41.6902 21.6656 41.3418V17.6937C21.6656 17.3452 21.804 17.0111 22.0504 16.7647C22.2968 16.5183 22.631 16.3799 22.9794 16.3799H43.9999C44.3484 16.3799 44.6825 16.5183 44.9289 16.7647C45.1753 17.0111 45.3137 17.3452 45.3137 17.6937V41.3418C45.3137 41.6902 45.1753 42.0244 44.9289 42.2707C44.6825 42.5171 44.3484 42.6555 43.9999 42.6555Z" fill="url(#paint0_linear_2141_212846)" fill-opacity="0.4"/>
</g>
<path d="M22.9791 16.4795H43.9996C44.2813 16.4795 44.5531 16.5779 44.7691 16.7549L44.858 16.835C45.0856 17.0625 45.2134 17.3716 45.2135 17.6934V41.3418C45.2135 41.6235 45.1161 41.8953 44.9391 42.1113L44.858 42.2002C44.6304 42.4277 44.3214 42.5557 43.9996 42.5557H22.9791C22.6975 42.5556 22.4264 42.4572 22.2105 42.2803L22.1207 42.2002C21.8932 41.9726 21.7652 41.6636 21.7652 41.3418V17.6934C21.7653 17.4119 21.8638 17.1407 22.0406 16.9248L22.1207 16.835C22.3198 16.6359 22.5813 16.513 22.859 16.4854L22.9791 16.4795Z" stroke="url(#paint1_linear_2141_212846)" stroke-width="0.2"/>
<foreignObject x="7.05432" y="10.6033" width="30.058" height="36.75"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(3.5px);clip-path:url(#bgblur_0_2141_212846_clip_path);height:100%;width:100%"></div></foreignObject><g filter="url(#filter1_ii_2141_212846)" data-figma-bg-blur-radius="7">
<path d="M17.4167 40.3533C15.2075 40.3533 13.3397 38.4717 14.3235 36.4937C14.7372 35.6618 15.2858 34.8943 15.9551 34.225C17.5804 32.5997 19.7848 31.6866 22.0833 31.6866C24.3819 31.6866 26.5863 32.5997 28.2116 34.225C28.8809 34.8943 29.4294 35.6618 29.8432 36.4937C30.8269 38.4717 28.9591 40.3533 26.75 40.3533H17.4167ZM22.0833 30.6033C18.4921 30.6033 15.5833 27.6945 15.5833 24.1033C15.5833 20.512 18.4921 17.6033 22.0833 17.6033C25.6746 17.6033 28.5833 20.512 28.5833 24.1033C28.5833 27.6945 25.6746 30.6033 22.0833 30.6033Z" fill="url(#paint2_linear_2141_212846)"/>
<path d="M22.0836 31.7869C24.3556 31.787 26.5348 32.6892 28.1413 34.2957C28.7202 34.8746 29.2076 35.528 29.5944 36.2332L29.7536 36.5378C30.2254 37.4866 30.0156 38.4095 29.4186 39.1003C28.8188 39.7944 27.8272 40.2537 26.7497 40.2537H17.4167C16.3393 40.2537 15.3485 39.7942 14.7487 39.1003C14.189 38.4526 13.969 37.6012 14.3327 36.7156L14.4128 36.5378C14.7705 35.8186 15.2305 35.1481 15.7829 34.5486L16.026 34.2957C17.6326 32.6892 19.8117 31.7869 22.0836 31.7869ZM22.0836 17.7029C25.6195 17.7031 28.4831 20.5674 28.4831 24.1033C28.4831 27.6392 25.6195 30.5035 22.0836 30.5037C18.5476 30.5037 15.6833 27.6393 15.6833 24.1033C15.6833 20.5672 18.5476 17.7029 22.0836 17.7029Z" stroke="url(#paint3_linear_2141_212846)" stroke-width="0.2"/>
</g>
<g filter="url(#filter2_di_2141_212846)">
<path d="M38.6635 34.9368H36.4968C35.8985 34.9368 35.4135 35.4219 35.4135 36.0202C35.4135 36.6185 35.8985 37.1035 36.4968 37.1035H38.6635C39.2618 37.1035 39.7468 36.6185 39.7468 36.0202C39.7468 35.4219 39.2618 34.9368 38.6635 34.9368Z" fill="url(#paint4_linear_2141_212846)"/>
<path d="M38.6635 29.5202H33.2468C32.6485 29.5202 32.1635 30.0052 32.1635 30.6035C32.1635 31.2018 32.6485 31.6868 33.2468 31.6868H38.6635C39.2618 31.6868 39.7468 31.2018 39.7468 30.6035C39.7468 30.0052 39.2618 29.5202 38.6635 29.5202Z" fill="url(#paint5_linear_2141_212846)"/>
<path d="M38.6635 24.1035H35.4135C34.8152 24.1035 34.3302 24.5885 34.3302 25.1868C34.3302 25.7852 34.8152 26.2702 35.4135 26.2702H38.6635C39.2618 26.2702 39.7468 25.7852 39.7468 25.1868C39.7468 24.5885 39.2618 24.1035 38.6635 24.1035Z" fill="url(#paint6_linear_2141_212846)"/>
</g>
<defs>
<filter id="filter0_ii_2141_212846" x="21.6656" y="15.3799" width="25.6481" height="29.2756" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="2" dy="2"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.5 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_2141_212846"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-1"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.66 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_2141_212846" result="effect2_innerShadow_2141_212846"/>
</filter>
<filter id="filter1_ii_2141_212846" x="7.05432" y="10.6033" width="30.058" height="36.75" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="2" dy="2"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.5 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_2141_212846"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-1"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.66 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_2141_212846" result="effect2_innerShadow_2141_212846"/>
</filter>
<clipPath id="bgblur_0_2141_212846_clip_path" transform="translate(-7.05432 -10.6033)"><path d="M17.4167 40.3533C15.2075 40.3533 13.3397 38.4717 14.3235 36.4937C14.7372 35.6618 15.2858 34.8943 15.9551 34.225C17.5804 32.5997 19.7848 31.6866 22.0833 31.6866C24.3819 31.6866 26.5863 32.5997 28.2116 34.225C28.8809 34.8943 29.4294 35.6618 29.8432 36.4937C30.8269 38.4717 28.9591 40.3533 26.75 40.3533H17.4167ZM22.0833 30.6033C18.4921 30.6033 15.5833 27.6945 15.5833 24.1033C15.5833 20.512 18.4921 17.6033 22.0833 17.6033C25.6746 17.6033 28.5833 20.512 28.5833 24.1033C28.5833 27.6945 25.6746 30.6033 22.0833 30.6033Z"/>
</clipPath><filter id="filter2_di_2141_212846" x="30.1635" y="23.1035" width="11.5833" height="17" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.169932 0 0 0 0 0.404803 0 0 0 0 0.733622 0 0 0 0.63 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2141_212846"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2141_212846" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-0.6"/>
<feGaussianBlur stdDeviation="0.1"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0740097 0 0 0 0 0.259687 0 0 0 0 0.537056 0 0 0 0.19 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_2141_212846"/>
</filter>
<linearGradient id="paint0_linear_2141_212846" x1="33.4897" y1="16.3799" x2="33.4897" y2="42.6555" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="0.515" stop-color="#63A0FC" stop-opacity="0.5"/>
<stop offset="1" stop-color="#1466E1"/>
</linearGradient>
<linearGradient id="paint1_linear_2141_212846" x1="33.4897" y1="16.3799" x2="33.4897" y2="42.6555" gradientUnits="userSpaceOnUse">
<stop stop-color="#3882F3"/>
<stop offset="1" stop-color="white" stop-opacity="0.4"/>
</linearGradient>
<linearGradient id="paint2_linear_2141_212846" x1="22.0833" y1="17.6033" x2="22.0833" y2="40.3533" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="0.515" stop-color="#63A0FC" stop-opacity="0.5"/>
<stop offset="1" stop-color="#1466E1"/>
</linearGradient>
<linearGradient id="paint3_linear_2141_212846" x1="22.0833" y1="17.6033" x2="22.0833" y2="40.3533" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="white" stop-opacity="0.4"/>
</linearGradient>
<linearGradient id="paint4_linear_2141_212846" x1="35.9552" y1="24.1035" x2="35.9552" y2="37.1035" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="0.19" stop-color="#C5DDFF"/>
<stop offset="0.401488" stop-color="white"/>
<stop offset="0.565" stop-color="#DBEAFF"/>
<stop offset="0.79" stop-color="white"/>
<stop offset="0.96" stop-color="#C5DDFF"/>
</linearGradient>
<linearGradient id="paint5_linear_2141_212846" x1="35.9552" y1="24.1035" x2="35.9552" y2="37.1035" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="0.19" stop-color="#C5DDFF"/>
<stop offset="0.401488" stop-color="white"/>
<stop offset="0.565" stop-color="#DBEAFF"/>
<stop offset="0.79" stop-color="white"/>
<stop offset="0.96" stop-color="#C5DDFF"/>
</linearGradient>
<linearGradient id="paint6_linear_2141_212846" x1="35.9552" y1="24.1035" x2="35.9552" y2="37.1035" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="0.19" stop-color="#C5DDFF"/>
<stop offset="0.401488" stop-color="white"/>
<stop offset="0.565" stop-color="#DBEAFF"/>
<stop offset="0.79" stop-color="white"/>
<stop offset="0.96" stop-color="#C5DDFF"/>
</linearGradient>
</defs>
</svg>
