<svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_ii_2230_229017)">
<path d="M5.17389 26C3.97327 26 2.99998 25.0267 2.99998 23.8261V6.17391C2.99998 4.97329 3.97327 4 5.17389 4H19.8261C21.0267 4 22 4.97329 22 6.17391V23.8261C22 25.0267 21.0267 26 19.8261 26H5.17389Z" fill="#1466E1"/>
<path d="M5.17389 26C3.97327 26 2.99998 25.0267 2.99998 23.8261V6.17391C2.99998 4.97329 3.97327 4 5.17389 4H19.8261C21.0267 4 22 4.97329 22 6.17391V23.8261C22 25.0267 21.0267 26 19.8261 26H5.17389Z" fill="url(#paint0_linear_2230_229017)" fill-opacity="0.4"/>
</g>
<path d="M5.17381 4.54395H19.8261C20.7265 4.54399 21.456 5.27343 21.456 6.17383V23.8262C21.456 24.7266 20.7265 25.456 19.8261 25.4561H5.17381C4.27341 25.456 3.54397 24.7266 3.54392 23.8262V6.17383C3.54397 5.27343 4.27341 4.54399 5.17381 4.54395Z" stroke="url(#paint1_linear_2230_229017)" stroke-width="1.08696"/>
<g opacity="0.4" filter="url(#filter1_f_2230_229017)">
<path d="M20.7924 14.5098V22.4391" stroke="white" stroke-width="0.842975"/>
</g>
<g opacity="0.8" filter="url(#filter2_di_2230_229017)">
<path d="M6.99998 10.1075C6.99998 9.49584 7.49582 9 8.10746 9H14.7524C15.364 9 15.8599 9.49584 15.8599 10.1075C15.8599 10.7191 15.364 11.215 14.7524 11.215H8.10746C7.49582 11.215 6.99998 10.7191 6.99998 10.1075ZM6.99998 14.5374C6.99998 13.9258 7.49582 13.4299 8.10746 13.4299H14.7524C15.364 13.4299 15.8599 13.9258 15.8599 14.5374C15.8599 15.1491 15.364 15.6449 14.7524 15.6449H8.10746C7.49582 15.6449 6.99998 15.1491 6.99998 14.5374ZM6.99998 18.9674C6.99998 18.3557 7.49582 17.8599 8.10746 17.8599H11.4299C12.0416 17.8599 12.5374 18.3557 12.5374 18.9674C12.5374 19.579 12.0416 20.0749 11.4299 20.0749H8.10746C7.49582 20.0749 6.99998 19.579 6.99998 18.9674Z" fill="url(#paint2_linear_2230_229017)"/>
</g>
<foreignObject x="10.0992" y="10.0552" width="24.8451" height="24.8456"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(2.95px);clip-path:url(#bgblur_0_2230_229017_clip_path);height:100%;width:100%"></div></foreignObject><g filter="url(#filter3_iii_2230_229017)" data-figma-bg-blur-radius="5.90083">
<circle cx="22.5217" cy="22.4778" r="6.52174" fill="url(#paint3_linear_2230_229017)"/>
<circle cx="22.5217" cy="22.4778" r="5.97826" stroke="url(#paint4_linear_2230_229017)" stroke-width="1.08696"/>
</g>
<g filter="url(#filter4_di_2230_229017)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M21.9783 17.8584C22.4285 17.8584 22.7935 18.2234 22.7935 18.6736V22.7497H25.7826C26.2329 22.7497 26.5978 23.1147 26.5978 23.5649C26.5978 24.0152 26.2329 24.3801 25.7826 24.3801H21.9783C21.528 24.3801 21.1631 24.0152 21.1631 23.5649V18.6736C21.1631 18.2234 21.528 17.8584 21.9783 17.8584Z" fill="url(#paint5_linear_2230_229017)"/>
</g>
<defs>
<filter id="filter0_ii_2230_229017" x="2.99998" y="3.15702" width="20.686" height="24.5289" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="1.68595" dy="1.68595"/>
<feGaussianBlur stdDeviation="1.68595"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.5 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_2230_229017"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-0.842975"/>
<feGaussianBlur stdDeviation="0.842975"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.66 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_2230_229017" result="effect2_innerShadow_2230_229017"/>
</filter>
<filter id="filter1_f_2230_229017" x="17.842" y="11.9808" width="5.90082" height="12.9875" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="1.26446" result="effect1_foregroundBlur_2230_229017"/>
</filter>
<filter id="filter2_di_2230_229017" x="5.31403" y="8.15702" width="12.2318" height="14.4471" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.842975"/>
<feGaussianBlur stdDeviation="0.842975"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.169932 0 0 0 0 0.404803 0 0 0 0 0.733622 0 0 0 0.63 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2230_229017"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2230_229017" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-0.505785"/>
<feGaussianBlur stdDeviation="0.0842975"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0740097 0 0 0 0 0.259687 0 0 0 0 0.537056 0 0 0 0.19 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_2230_229017"/>
</filter>
<filter id="filter3_iii_2230_229017" x="10.0992" y="10.0552" width="24.8451" height="24.8456" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="1.68595" dy="1.68595"/>
<feGaussianBlur stdDeviation="1.68595"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.5 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_2230_229017"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-0.842975"/>
<feGaussianBlur stdDeviation="0.842975"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.66 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_2230_229017" result="effect2_innerShadow_2230_229017"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-0.842975"/>
<feGaussianBlur stdDeviation="0.842975"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.35 0"/>
<feBlend mode="normal" in2="effect2_innerShadow_2230_229017" result="effect3_innerShadow_2230_229017"/>
</filter>
<clipPath id="bgblur_0_2230_229017_clip_path" transform="translate(-10.0992 -10.0552)"><circle cx="22.5217" cy="22.4778" r="6.52174"/>
</clipPath><filter id="filter4_di_2230_229017" x="19.4771" y="17.0154" width="8.80669" height="9.89339" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.842975"/>
<feGaussianBlur stdDeviation="0.842975"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.169932 0 0 0 0 0.404803 0 0 0 0 0.733622 0 0 0 0.63 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2230_229017"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2230_229017" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-0.505785"/>
<feGaussianBlur stdDeviation="0.0842975"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0740097 0 0 0 0 0.259687 0 0 0 0 0.537056 0 0 0 0.19 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_2230_229017"/>
</filter>
<linearGradient id="paint0_linear_2230_229017" x1="12.5" y1="4" x2="12.5" y2="26" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="0.515" stop-color="#63A0FC" stop-opacity="0.5"/>
<stop offset="1" stop-color="#1466E1"/>
</linearGradient>
<linearGradient id="paint1_linear_2230_229017" x1="12.5" y1="4" x2="12.5" y2="26" gradientUnits="userSpaceOnUse">
<stop stop-color="#3882F3"/>
<stop offset="1" stop-color="white" stop-opacity="0.4"/>
</linearGradient>
<linearGradient id="paint2_linear_2230_229017" x1="11.4299" y1="9" x2="11.4299" y2="20.0749" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="0.19" stop-color="#C5DDFF"/>
<stop offset="0.401488" stop-color="white"/>
<stop offset="0.565" stop-color="#DBEAFF"/>
<stop offset="0.79" stop-color="white"/>
<stop offset="0.96" stop-color="#C5DDFF"/>
</linearGradient>
<linearGradient id="paint3_linear_2230_229017" x1="22.5217" y1="15.9561" x2="22.5217" y2="28.9995" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="0.515" stop-color="#63A0FC" stop-opacity="0.5"/>
<stop offset="1" stop-color="#1466E1"/>
</linearGradient>
<linearGradient id="paint4_linear_2230_229017" x1="22.5217" y1="15.9561" x2="22.5217" y2="28.9995" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="white" stop-opacity="0.4"/>
</linearGradient>
<linearGradient id="paint5_linear_2230_229017" x1="23.8805" y1="17.8584" x2="23.8805" y2="24.3801" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="0.79" stop-color="white"/>
<stop offset="0.96" stop-color="#C5DDFF"/>
</linearGradient>
</defs>
</svg>
