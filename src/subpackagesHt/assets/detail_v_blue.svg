<svg width="69" height="65" viewBox="0 0 69 65" fill="none" xmlns="http://www.w3.org/2000/svg">
<g opacity="0.1" filter="url(#filter0_i_2028_170072)">
<path d="M9.3008 13.09C6.68924 5.9827 5.26892 4.63322 0 3.86851L0 0L31.6594 0V3.86851C25.0618 4.76817 24.0996 6.29758 26.4363 13.3149L39.9064 52.6298L53.1016 13.7197C55.5757 6.34256 54.7052 4.81315 47.8785 3.86851V0L69 0V3.86851C63.5936 4.63322 62.3108 5.93772 59.4243 13.8547L40.6394 65H28.1773L9.3008 13.09Z" fill="url(#paint0_linear_2028_170072)"/>
</g>
<defs>
<filter id="filter0_i_2028_170072" x="0" y="0" width="69" height="65" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="1.10132"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0154745 0 0 0 0 0.14184 0 0 0 0 0.233105 0 0 0 0.6 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_2028_170072"/>
</filter>
<linearGradient id="paint0_linear_2028_170072" x1="69" y1="32.5" x2="0" y2="32.5" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0.4"/>
<stop offset="1" stop-color="white"/>
</linearGradient>
</defs>
</svg>
