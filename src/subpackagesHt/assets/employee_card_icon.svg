<svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_ii_2230_229047)">
<path d="M15 26C12.7909 26 11 24.2091 11 22V14C11 11.7909 12.7909 10 15 10H24C26.2092 10 28 11.7909 28 14V22C28 24.2091 26.2092 26 24 26H15Z" fill="#1466E1"/>
<path d="M15 26C12.7909 26 11 24.2091 11 22V14C11 11.7909 12.7909 10 15 10H24C26.2092 10 28 11.7909 28 14V22C28 24.2091 26.2092 26 24 26H15Z" fill="url(#paint0_linear_2230_229047)" fill-opacity="0.4"/>
</g>
<path d="M15 10.5439H24C25.909 10.5439 27.4561 12.091 27.4561 14V22C27.4561 23.909 25.909 25.4561 24 25.4561H15C13.091 25.4561 11.544 23.909 11.544 22V14C11.544 12.091 13.091 10.5439 15 10.5439Z" stroke="url(#paint1_linear_2230_229047)" stroke-width="1.08696"/>
<foreignObject x="-2.9008" y="6.09917" width="32.8017" height="28.8017"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(2.95px);clip-path:url(#bgblur_0_2230_229047_clip_path);height:100%;width:100%"></div></foreignObject><g filter="url(#filter1_iii_2230_229047)" data-figma-bg-blur-radius="5.90083">
<mask id="path-3-inside-1_2230_229047" fill="white">
<path d="M21.4707 12C22.8674 12 24 13.1326 24 14.5293V26.4707C24 27.8674 22.8674 29 21.4707 29H5.52932C4.13263 29 3.00002 27.8674 3.00002 26.4707V14.5293C3.00002 13.1326 4.13263 12 5.52932 12H21.4707Z"/>
</mask>
<path d="M21.4707 12C22.8674 12 24 13.1326 24 14.5293V26.4707C24 27.8674 22.8674 29 21.4707 29H5.52932C4.13263 29 3.00002 27.8674 3.00002 26.4707V14.5293C3.00002 13.1326 4.13263 12 5.52932 12H21.4707Z" fill="url(#paint2_linear_2230_229047)"/>
<path d="M21.4707 12V13.087C22.2671 13.087 22.9131 13.7329 22.9131 14.5293H24H25.087C25.087 12.5323 23.4677 10.913 21.4707 10.913V12ZM24 14.5293H22.9131V26.4707H24H25.087V14.5293H24ZM24 26.4707H22.9131C22.9131 27.2671 22.2671 27.913 21.4707 27.913V29V30.087C23.4677 30.087 25.087 28.4677 25.087 26.4707H24ZM21.4707 29V27.913H5.52932V29V30.087H21.4707V29ZM5.52932 29V27.913C4.73294 27.913 4.08698 27.2671 4.08698 26.4707H3.00002H1.91307C1.91307 28.4677 3.53232 30.087 5.52932 30.087V29ZM3.00002 26.4707H4.08698V14.5293H3.00002H1.91307V26.4707H3.00002ZM3.00002 14.5293H4.08698C4.08698 13.7329 4.73294 13.087 5.52932 13.087V12V10.913C3.53232 10.913 1.91307 12.5323 1.91307 14.5293H3.00002ZM5.52932 12V13.087H21.4707V12V10.913H5.52932V12Z" fill="url(#paint3_linear_2230_229047)" mask="url(#path-3-inside-1_2230_229047)"/>
</g>
<g filter="url(#filter2_dii_2230_229047)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M7.63934 17C7.99244 17 8.27869 17.2862 8.27869 17.6393V23.5C8.27869 23.8531 7.99244 24.1393 7.63934 24.1393C7.28624 24.1393 7 23.8531 7 23.5V17.6393C7 17.2862 7.28624 17 7.63934 17Z" fill="url(#paint4_linear_2230_229047)"/>
</g>
<g filter="url(#filter3_dii_2230_229047)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M9.98348 17.7988C10.3366 17.7988 10.6228 18.0851 10.6228 18.4382V22.7005C10.6228 23.0536 10.3366 23.3398 9.98348 23.3398C9.63038 23.3398 9.34413 23.0536 9.34413 22.7005V18.4382C9.34413 18.0851 9.63038 17.7988 9.98348 17.7988Z" fill="url(#paint5_linear_2230_229047)"/>
</g>
<g filter="url(#filter4_dii_2230_229047)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M12.3279 17C12.681 17 12.9672 17.2862 12.9672 17.6393V23.5C12.9672 23.8531 12.681 24.1393 12.3279 24.1393C11.9748 24.1393 11.6885 23.8531 11.6885 23.5V17.6393C11.6885 17.2862 11.9748 17 12.3279 17Z" fill="url(#paint6_linear_2230_229047)"/>
</g>
<g filter="url(#filter5_dii_2230_229047)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M14.6721 17.7988C15.0252 17.7988 15.3115 18.0851 15.3115 18.4382V22.7005C15.3115 23.0536 15.0252 23.3398 14.6721 23.3398C14.319 23.3398 14.0328 23.0536 14.0328 22.7005V18.4382C14.0328 18.0851 14.319 17.7988 14.6721 17.7988Z" fill="url(#paint7_linear_2230_229047)"/>
</g>
<g filter="url(#filter6_dii_2230_229047)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M17.0164 17C17.3695 17 17.6557 17.2862 17.6557 17.6393V23.5C17.6557 23.8531 17.3695 24.1393 17.0164 24.1393C16.6633 24.1393 16.3771 23.8531 16.3771 23.5V17.6393C16.3771 17.2862 16.6633 17 17.0164 17Z" fill="url(#paint8_linear_2230_229047)"/>
</g>
<g filter="url(#filter7_dii_2230_229047)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M19.3607 18.332C19.7138 18.332 20 18.6183 20 18.9714L20 22.1681C20 22.5212 19.7138 22.8074 19.3607 22.8074C19.0076 22.8074 18.7213 22.5212 18.7213 22.1681L18.7213 18.9714C18.7213 18.6183 19.0076 18.332 19.3607 18.332Z" fill="url(#paint9_linear_2230_229047)"/>
</g>
<defs>
<filter id="filter0_ii_2230_229047" x="11" y="9.15702" width="18.686" height="18.5289" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="1.68595" dy="1.68595"/>
<feGaussianBlur stdDeviation="1.68595"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.5 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_2230_229047"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-0.842975"/>
<feGaussianBlur stdDeviation="0.842975"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.66 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_2230_229047" result="effect2_innerShadow_2230_229047"/>
</filter>
<filter id="filter1_iii_2230_229047" x="-2.9008" y="6.09917" width="32.8017" height="28.8017" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="1.68595" dy="1.68595"/>
<feGaussianBlur stdDeviation="1.68595"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.5 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_2230_229047"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-0.842975"/>
<feGaussianBlur stdDeviation="0.842975"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.66 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_2230_229047" result="effect2_innerShadow_2230_229047"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-0.842975"/>
<feGaussianBlur stdDeviation="0.842975"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.35 0"/>
<feBlend mode="normal" in2="effect2_innerShadow_2230_229047" result="effect3_innerShadow_2230_229047"/>
</filter>
<clipPath id="bgblur_0_2230_229047_clip_path" transform="translate(2.9008 -6.09917)"><path d="M21.4707 12C22.8674 12 24 13.1326 24 14.5293V26.4707C24 27.8674 22.8674 29 21.4707 29H5.52932C4.13263 29 3.00002 27.8674 3.00002 26.4707V14.5293C3.00002 13.1326 4.13263 12 5.52932 12H21.4707Z"/>
</clipPath><filter id="filter2_dii_2230_229047" x="5.32893" y="16.1645" width="4.62082" height="10.4818" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.835535"/>
<feGaussianBlur stdDeviation="0.835535"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.169932 0 0 0 0 0.404803 0 0 0 0 0.733622 0 0 0 0.63 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2230_229047"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2230_229047" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-0.501321"/>
<feGaussianBlur stdDeviation="0.0835535"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0740097 0 0 0 0 0.259687 0 0 0 0 0.537056 0 0 0 0.19 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_2230_229047"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2.13115"/>
<feGaussianBlur stdDeviation="3.19672"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="effect2_innerShadow_2230_229047" result="effect3_innerShadow_2230_229047"/>
</filter>
<filter id="filter3_dii_2230_229047" x="7.67306" y="16.9633" width="4.62082" height="8.88315" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.835535"/>
<feGaussianBlur stdDeviation="0.835535"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.169932 0 0 0 0 0.404803 0 0 0 0 0.733622 0 0 0 0.63 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2230_229047"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2230_229047" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-0.501321"/>
<feGaussianBlur stdDeviation="0.0835535"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0740097 0 0 0 0 0.259687 0 0 0 0 0.537056 0 0 0 0.19 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_2230_229047"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2.13115"/>
<feGaussianBlur stdDeviation="3.19672"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="effect2_innerShadow_2230_229047" result="effect3_innerShadow_2230_229047"/>
</filter>
<filter id="filter4_dii_2230_229047" x="10.0175" y="16.1645" width="4.62082" height="10.4818" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.835535"/>
<feGaussianBlur stdDeviation="0.835535"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.169932 0 0 0 0 0.404803 0 0 0 0 0.733622 0 0 0 0.63 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2230_229047"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2230_229047" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-0.501321"/>
<feGaussianBlur stdDeviation="0.0835535"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0740097 0 0 0 0 0.259687 0 0 0 0 0.537056 0 0 0 0.19 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_2230_229047"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2.13115"/>
<feGaussianBlur stdDeviation="3.19672"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="effect2_innerShadow_2230_229047" result="effect3_innerShadow_2230_229047"/>
</filter>
<filter id="filter5_dii_2230_229047" x="12.3617" y="16.9633" width="4.62082" height="8.88315" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.835535"/>
<feGaussianBlur stdDeviation="0.835535"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.169932 0 0 0 0 0.404803 0 0 0 0 0.733622 0 0 0 0.63 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2230_229047"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2230_229047" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-0.501321"/>
<feGaussianBlur stdDeviation="0.0835535"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0740097 0 0 0 0 0.259687 0 0 0 0 0.537056 0 0 0 0.19 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_2230_229047"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2.13115"/>
<feGaussianBlur stdDeviation="3.19672"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="effect2_innerShadow_2230_229047" result="effect3_innerShadow_2230_229047"/>
</filter>
<filter id="filter6_dii_2230_229047" x="14.706" y="16.1645" width="4.62082" height="10.4818" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.835535"/>
<feGaussianBlur stdDeviation="0.835535"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.169932 0 0 0 0 0.404803 0 0 0 0 0.733622 0 0 0 0.63 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2230_229047"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2230_229047" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-0.501321"/>
<feGaussianBlur stdDeviation="0.0835535"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0740097 0 0 0 0 0.259687 0 0 0 0 0.537056 0 0 0 0.19 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_2230_229047"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2.13115"/>
<feGaussianBlur stdDeviation="3.19672"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="effect2_innerShadow_2230_229047" result="effect3_innerShadow_2230_229047"/>
</filter>
<filter id="filter7_dii_2230_229047" x="17.0502" y="17.4965" width="4.62082" height="7.81772" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.835535"/>
<feGaussianBlur stdDeviation="0.835535"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.169932 0 0 0 0 0.404803 0 0 0 0 0.733622 0 0 0 0.63 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2230_229047"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2230_229047" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-0.501321"/>
<feGaussianBlur stdDeviation="0.0835535"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0740097 0 0 0 0 0.259687 0 0 0 0 0.537056 0 0 0 0.19 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_2230_229047"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2.13115"/>
<feGaussianBlur stdDeviation="3.19672"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="effect2_innerShadow_2230_229047" result="effect3_innerShadow_2230_229047"/>
</filter>
<linearGradient id="paint0_linear_2230_229047" x1="19.5" y1="10" x2="19.5" y2="26" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="0.515" stop-color="#63A0FC" stop-opacity="0.5"/>
<stop offset="1" stop-color="#1466E1"/>
</linearGradient>
<linearGradient id="paint1_linear_2230_229047" x1="19.5" y1="10" x2="19.5" y2="26" gradientUnits="userSpaceOnUse">
<stop stop-color="#3882F3"/>
<stop offset="1" stop-color="white" stop-opacity="0.4"/>
</linearGradient>
<linearGradient id="paint2_linear_2230_229047" x1="14.0001" y1="8.36426" x2="14.0001" y2="26.9097" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="0.515" stop-color="#63A0FC" stop-opacity="0.5"/>
<stop offset="1" stop-color="#1466E1"/>
</linearGradient>
<linearGradient id="paint3_linear_2230_229047" x1="14.0001" y1="8.36426" x2="14.0001" y2="26.9097" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="white" stop-opacity="0.4"/>
</linearGradient>
<linearGradient id="paint4_linear_2230_229047" x1="7.89646" y1="17" x2="7.89646" y2="24.1393" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="0.227063" stop-color="white"/>
<stop offset="0.649401" stop-color="white"/>
<stop offset="0.96" stop-color="#C5DDFF"/>
</linearGradient>
<linearGradient id="paint5_linear_2230_229047" x1="10.2406" y1="17.7988" x2="10.2406" y2="23.3398" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="0.227063" stop-color="white"/>
<stop offset="0.649401" stop-color="white"/>
<stop offset="0.96" stop-color="#C5DDFF"/>
</linearGradient>
<linearGradient id="paint6_linear_2230_229047" x1="12.585" y1="17" x2="12.585" y2="24.1393" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="0.227063" stop-color="white"/>
<stop offset="0.649401" stop-color="white"/>
<stop offset="0.96" stop-color="#C5DDFF"/>
</linearGradient>
<linearGradient id="paint7_linear_2230_229047" x1="14.9293" y1="17.7988" x2="14.9293" y2="23.3398" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="0.227063" stop-color="white"/>
<stop offset="0.649401" stop-color="white"/>
<stop offset="0.96" stop-color="#C5DDFF"/>
</linearGradient>
<linearGradient id="paint8_linear_2230_229047" x1="17.2735" y1="17" x2="17.2735" y2="24.1393" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="0.227063" stop-color="white"/>
<stop offset="0.649401" stop-color="white"/>
<stop offset="0.96" stop-color="#C5DDFF"/>
</linearGradient>
<linearGradient id="paint9_linear_2230_229047" x1="19.6178" y1="18.332" x2="19.6178" y2="22.8074" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="0.227063" stop-color="white"/>
<stop offset="0.649401" stop-color="white"/>
<stop offset="0.96" stop-color="#C5DDFF"/>
</linearGradient>
</defs>
</svg>
