<svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_ii_2201_226839)">
<path d="M15.963 7.55664C12.5433 7.55664 9.77135 10.3545 9.77135 13.8056C9.77135 17.2565 12.5433 20.0544 15.963 20.0544C19.3825 20.0544 22.1545 17.2565 22.1545 13.8056C22.1532 10.3545 19.3825 7.55664 15.963 7.55664ZM23.0725 20.0532C22.5317 19.7004 21.8339 19.8195 21.315 20.2039C19.8157 21.3147 17.9657 21.9711 15.963 21.9711C13.966 21.9711 12.1204 21.3191 10.6234 20.2141C10.1021 19.8294 9.40125 19.7116 8.86112 20.0694C6.88481 21.3785 5.51276 23.3227 5.1173 25.813C4.98515 26.6452 5.68321 27.3332 6.52583 27.3332H25.4743C26.3169 27.3332 27.0151 26.6451 26.8813 25.8132C26.4788 23.3124 25.0792 21.3626 23.0725 20.0532Z" fill="#1466E1"/>
<path d="M15.963 7.55664C12.5433 7.55664 9.77135 10.3545 9.77135 13.8056C9.77135 17.2565 12.5433 20.0544 15.963 20.0544C19.3825 20.0544 22.1545 17.2565 22.1545 13.8056C22.1532 10.3545 19.3825 7.55664 15.963 7.55664ZM23.0725 20.0532C22.5317 19.7004 21.8339 19.8195 21.315 20.2039C19.8157 21.3147 17.9657 21.9711 15.963 21.9711C13.966 21.9711 12.1204 21.3191 10.6234 20.2141C10.1021 19.8294 9.40125 19.7116 8.86112 20.0694C6.88481 21.3785 5.51276 23.3227 5.1173 25.813C4.98515 26.6452 5.68321 27.3332 6.52583 27.3332H25.4743C26.3169 27.3332 27.0151 26.6451 26.8813 25.8132C26.4788 23.3124 25.0792 21.3626 23.0725 20.0532Z" fill="url(#paint0_linear_2201_226839)" fill-opacity="0.4"/>
</g>
<path d="M21.6124 20.6055C21.9676 20.3424 22.3694 20.2801 22.6749 20.4062L22.799 20.4717C24.58 21.6338 25.8434 23.3169 26.3058 25.458L26.3878 25.8926C26.4638 26.3651 26.0681 26.8328 25.4747 26.833H6.52551C5.96971 26.8328 5.58664 26.4222 5.60266 25.9805L5.61145 25.8916C5.95998 23.6968 7.11133 21.9549 8.79407 20.7256L9.13684 20.4863C9.41111 20.3047 9.80386 20.3097 10.171 20.5156L10.3263 20.6162C11.9061 21.7824 13.8552 22.4707 15.963 22.4707C17.9449 22.4707 19.7856 21.8614 21.3116 20.8193L21.6124 20.6055ZM15.963 8.05664C19.1019 8.05666 21.6531 10.6261 21.6544 13.8057L21.6476 14.1016C21.495 17.1427 19.004 19.5547 15.963 19.5547C12.8239 19.5547 10.2717 16.9848 10.2716 13.8057C10.2716 10.6264 12.8238 8.05664 15.963 8.05664Z" stroke="url(#paint1_linear_2201_226839)"/>
<foreignObject x="12.0992" y="9.09917" width="24.8451" height="24.8456"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(2.95px);clip-path:url(#bgblur_0_2201_226839_clip_path);height:100%;width:100%"></div></foreignObject><g filter="url(#filter1_iii_2201_226839)" data-figma-bg-blur-radius="5.90083">
<circle cx="24.5217" cy="21.5217" r="6.52174" fill="url(#paint2_linear_2201_226839)"/>
<circle cx="24.5217" cy="21.5217" r="5.97826" stroke="url(#paint3_linear_2201_226839)" stroke-width="1.08696"/>
</g>
<g filter="url(#filter2_di_2201_226839)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M21 21.75C21 21.3358 21.3358 21 21.75 21H27.75C28.1642 21 28.5 21.3358 28.5 21.75C28.5 22.1642 28.1642 22.5 27.75 22.5H21.75C21.3358 22.5 21 22.1642 21 21.75Z" fill="url(#paint4_linear_2201_226839)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M24.75 18C25.1642 18 25.5 18.3358 25.5 18.75L25.5 24.75C25.5 25.1642 25.1642 25.5 24.75 25.5C24.3358 25.5 24 25.1642 24 24.75L24 18.75C24 18.3358 24.3358 18 24.75 18Z" fill="url(#paint5_linear_2201_226839)"/>
</g>
<defs>
<filter id="filter0_ii_2201_226839" x="5.10116" y="6.79378" width="23.3223" height="22.0649" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="1.52571" dy="1.52571"/>
<feGaussianBlur stdDeviation="1.52571"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.5 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_2201_226839"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-0.762856"/>
<feGaussianBlur stdDeviation="0.762856"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.66 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_2201_226839" result="effect2_innerShadow_2201_226839"/>
</filter>
<filter id="filter1_iii_2201_226839" x="12.0992" y="9.09917" width="24.8451" height="24.8456" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="1.68595" dy="1.68595"/>
<feGaussianBlur stdDeviation="1.68595"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.5 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_2201_226839"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-0.842975"/>
<feGaussianBlur stdDeviation="0.842975"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.66 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_2201_226839" result="effect2_innerShadow_2201_226839"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-0.842975"/>
<feGaussianBlur stdDeviation="0.842975"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.35 0"/>
<feBlend mode="normal" in2="effect2_innerShadow_2201_226839" result="effect3_innerShadow_2201_226839"/>
</filter>
<clipPath id="bgblur_0_2201_226839_clip_path" transform="translate(-12.0992 -9.09917)"><circle cx="24.5217" cy="21.5217" r="6.52174"/>
</clipPath><filter id="filter2_di_2201_226839" x="19.314" y="17.157" width="10.8719" height="10.8719" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.842975"/>
<feGaussianBlur stdDeviation="0.842975"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.169932 0 0 0 0 0.404803 0 0 0 0 0.733622 0 0 0 0.63 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2201_226839"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2201_226839" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-0.505785"/>
<feGaussianBlur stdDeviation="0.0842975"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0740097 0 0 0 0 0.259687 0 0 0 0 0.537056 0 0 0 0.19 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_2201_226839"/>
</filter>
<linearGradient id="paint0_linear_2201_226839" x1="16.0001" y1="4.66699" x2="16.0001" y2="27.3336" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="0.515" stop-color="#63A0FC" stop-opacity="0.5"/>
<stop offset="1" stop-color="#1466E1"/>
</linearGradient>
<linearGradient id="paint1_linear_2201_226839" x1="16.0001" y1="4.66699" x2="16.0001" y2="27.3336" gradientUnits="userSpaceOnUse">
<stop stop-color="#3882F3"/>
<stop offset="1" stop-color="white" stop-opacity="0.4"/>
</linearGradient>
<linearGradient id="paint2_linear_2201_226839" x1="24.5217" y1="15" x2="24.5217" y2="28.0435" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="0.515" stop-color="#63A0FC" stop-opacity="0.5"/>
<stop offset="1" stop-color="#1466E1"/>
</linearGradient>
<linearGradient id="paint3_linear_2201_226839" x1="24.5217" y1="15" x2="24.5217" y2="28.0435" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="white" stop-opacity="0.4"/>
</linearGradient>
<linearGradient id="paint4_linear_2201_226839" x1="24.75" y1="18" x2="24.75" y2="25.5" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="0.79" stop-color="white"/>
<stop offset="0.96" stop-color="#C5DDFF"/>
</linearGradient>
<linearGradient id="paint5_linear_2201_226839" x1="24.75" y1="18" x2="24.75" y2="25.5" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="0.79" stop-color="white"/>
<stop offset="0.96" stop-color="#C5DDFF"/>
</linearGradient>
</defs>
</svg>
