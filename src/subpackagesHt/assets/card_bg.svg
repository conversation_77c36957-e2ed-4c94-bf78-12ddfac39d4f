<svg width="80" height="50" viewBox="0 0 80 50" fill="none" xmlns="http://www.w3.org/2000/svg">
<mask id="mask0_2027_163245" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="80" height="50">
<rect width="80" height="49.9048" rx="4.57143" fill="url(#paint0_linear_2027_163245)"/>
</mask>
<g mask="url(#mask0_2027_163245)">
<rect x="0.27533" y="0.27533" width="79.4493" height="49.3541" rx="4.2961" fill="url(#paint1_linear_2027_163245)" stroke="url(#paint2_linear_2027_163245)" stroke-width="0.550661"/>
<g opacity="0.1">
<path d="M12.992 13.3726L10.3235 13.2811L12.7094 10.0233L15.4256 10.1166L17.519 13.528L14.9775 13.4408L13.9909 12.0711L12.992 13.3726Z" fill="#0254A6"/>
<path d="M15.3751 10.0734L12.7624 9.98373L14.1181 7.97729L15.3751 10.0734Z" fill="white" stroke="#0254A6" stroke-width="0.0807666"/>
<path d="M14.4134 8.40662L14.1196 7.9166L13.8025 8.38565L14.4134 8.40662Z" fill="#0254A6"/>
<rect x="12.8227" y="12.7466" width="2.39989" height="0.44502" transform="rotate(1.96553 12.8227 12.7466)" fill="#0254A6"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M15.9013 9.31897L15.3589 9.30035L17.576 12.9787L18.522 13.0112L18.5256 12.9072L17.9507 12.6609L15.9013 9.31897ZM10.3298 12.73L12.7939 9.21233L12.2514 9.19371L9.97777 12.3873L9.38735 12.5936L9.38379 12.6975L10.3298 12.73Z" fill="#0254A6"/>
<mask id="path-8-inside-1_2027_163245" fill="white">
<path fill-rule="evenodd" clip-rule="evenodd" d="M13.3425 8.4292L13.1214 7.97173L12.2515 9.1937L12.7939 9.21232L13.3425 8.4292ZM14.8653 8.48147L15.1172 8.04022L15.9013 9.31896L15.3589 9.30034L17.5761 12.9787L17.576 12.9787L14.8653 8.48147Z"/>
</mask>
<path fill-rule="evenodd" clip-rule="evenodd" d="M13.3425 8.4292L13.1214 7.97173L12.2515 9.1937L12.7939 9.21232L13.3425 8.4292ZM14.8653 8.48147L15.1172 8.04022L15.9013 9.31896L15.3589 9.30034L17.5761 12.9787L17.576 12.9787L14.8653 8.48147Z" fill="white"/>
<path d="M13.1214 7.97173L13.1942 7.93659L13.1348 7.81372L13.0557 7.92489L13.1214 7.97173ZM13.3425 8.4292L13.4087 8.47554L13.4358 8.43673L13.4152 8.39406L13.3425 8.4292ZM12.2515 9.1937L12.1857 9.14686L12.0985 9.26926L12.2487 9.27442L12.2515 9.1937ZM12.7939 9.21232L12.7912 9.29303L12.835 9.29454L12.8601 9.25865L12.7939 9.21232ZM15.1172 8.04022L15.186 7.998L15.1147 7.88167L15.047 8.00018L15.1172 8.04022ZM14.8653 8.48147L14.7951 8.44142L14.7716 8.48258L14.7961 8.52316L14.8653 8.48147ZM15.9013 9.31896L15.8986 9.39968L16.0487 9.40483L15.9702 9.27673L15.9013 9.31896ZM15.3589 9.30034L15.3617 9.21962L15.2128 9.21451L15.2897 9.34203L15.3589 9.30034ZM17.5761 12.9787L17.567 13.0589L17.7298 13.0773L17.6452 12.937L17.5761 12.9787ZM17.576 12.9787L17.5069 13.0204L17.5274 13.0545L17.567 13.0589L17.576 12.9787ZM13.0487 8.00687L13.2698 8.46434L13.4152 8.39406L13.1942 7.93659L13.0487 8.00687ZM12.3173 9.24054L13.1872 8.01857L13.0557 7.92489L12.1857 9.14686L12.3173 9.24054ZM12.7967 9.1316L12.2542 9.11298L12.2487 9.27442L12.7912 9.29303L12.7967 9.1316ZM13.2764 8.38286L12.7278 9.16598L12.8601 9.25865L13.4087 8.47554L13.2764 8.38286ZM15.047 8.00018L14.7951 8.44142L14.9354 8.52151L15.1873 8.08026L15.047 8.00018ZM15.9702 9.27673L15.186 7.998L15.0483 8.08244L15.8325 9.36118L15.9702 9.27673ZM15.9041 9.23824L15.3617 9.21962L15.3561 9.38106L15.8986 9.39968L15.9041 9.23824ZM15.2897 9.34203L17.5069 13.0204L17.6452 12.937L15.4281 9.25865L15.2897 9.34203ZM17.567 13.0589L17.567 13.0589L17.5851 12.8984L17.5851 12.8984L17.567 13.0589ZM14.7961 8.52316L17.5069 13.0204L17.6452 12.937L14.9344 8.43977L14.7961 8.52316Z" fill="#0254A6" mask="url(#path-8-inside-1_2027_163245)"/>
<mask id="mask1_2027_163245" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="6" y="5" width="16" height="7">
<path fill-rule="evenodd" clip-rule="evenodd" d="M7.13698 8.62721L7.21817 8.88953C7.52138 9.11299 8.28002 9.41777 9.13701 9.59453C10.126 9.79852 12.6793 10.6721 13.9613 11.9581C15.1796 10.7609 16.3936 10.378 17.4732 10.1964C18.3131 10.0551 19.5 9.73261 20.2643 9.52494C20.5405 9.44991 20.7614 9.38986 20.891 9.35876C20.9095 9.32565 20.9423 9.24419 20.9853 9.1275L20.8267 9.09512C20.7952 9.08869 20.7759 9.0568 20.7847 9.02589C20.7919 9.00083 20.8157 8.98422 20.8417 8.98609L21.0316 8.99971C21.058 8.92576 21.087 8.84303 21.118 8.75352L20.9374 8.71675C20.9056 8.71028 20.8863 8.67794 20.8956 8.64689C20.903 8.62215 20.9266 8.60588 20.9523 8.60774L21.1629 8.62292C21.1896 8.54498 21.2175 8.46314 21.2462 8.37847L21.0499 8.33857C21.0184 8.33214 20.999 8.30008 21.008 8.26913C21.0153 8.24419 21.039 8.22772 21.0649 8.2296L21.291 8.24597C21.3177 8.16663 21.3451 8.08541 21.3727 8.00313L21.1621 7.96038C21.1305 7.95396 21.1111 7.92188 21.1202 7.89092C21.1275 7.86601 21.1512 7.84956 21.1771 7.85144L21.4177 7.86895C21.4395 7.80409 21.4613 7.73886 21.4832 7.67364C21.4883 7.65832 21.4935 7.643 21.4986 7.62768L21.2721 7.5818C21.241 7.57552 21.2221 7.54397 21.2311 7.51361C21.2383 7.48936 21.2613 7.47336 21.2866 7.47514L21.5436 7.49329C21.5709 7.41211 21.5979 7.3315 21.6246 7.25221L21.3849 7.20373C21.3534 7.19737 21.334 7.16555 21.3428 7.13469C21.35 7.10959 21.3738 7.09296 21.3998 7.09488L21.6709 7.11479C21.7533 6.87048 21.8305 6.64367 21.8957 6.45798C21.8064 6.50948 21.7162 6.56187 21.6248 6.61495C20.7192 7.14078 19.698 7.73374 18.3632 8.18506C16.4297 8.83879 14.2097 10.2405 14.0147 10.4022C13.9393 10.363 13.6308 10.1846 13.2215 9.94797C12.2394 9.38021 10.6765 8.47673 10.3584 8.3524C8.98305 7.81476 7.99361 7.11399 7.05214 6.44719C6.80011 6.26868 6.55151 6.09261 6.29987 5.92276L6.50837 6.59639L6.74743 6.58877C6.77352 6.58794 6.7966 6.60553 6.80271 6.63091C6.81021 6.66211 6.78955 6.69309 6.75785 6.69815L6.55012 6.73126L6.62703 6.97973L6.84481 6.97341C6.87009 6.97267 6.89249 6.98962 6.89867 7.01415C6.90641 7.04486 6.88615 7.07559 6.85488 7.08058L6.66751 7.11051L6.74472 7.35996L6.93864 7.35392C6.96459 7.35311 6.98758 7.37053 6.99383 7.39573C7.00159 7.42703 6.98092 7.45828 6.94907 7.46338L6.78488 7.48968L6.86289 7.74172L7.03501 7.73641C7.06098 7.73561 7.08398 7.75305 7.09021 7.77826C7.09795 7.80956 7.07728 7.84079 7.04545 7.8459L6.90225 7.86889L6.98106 8.12348L7.1318 8.11888C7.15762 8.11809 7.18052 8.13533 7.1869 8.16035C7.19492 8.19176 7.17424 8.22327 7.14223 8.22842L7.01964 8.24812L7.09925 8.50534L7.22668 8.5015C7.25275 8.50071 7.2758 8.51829 7.28194 8.54363C7.28951 8.57487 7.26886 8.60594 7.23713 8.61106L7.13698 8.62721Z" fill="black"/>
</mask>
<g mask="url(#mask1_2027_163245)">
<path d="M21.3019 8.1564C21.2024 8.45158 21.1095 8.72534 21.0347 8.93675C20.9974 9.04215 20.9644 9.13182 20.9377 9.20116C20.9153 9.25923 20.8957 9.30059 20.8836 9.32536C20.4295 9.43667 19.0393 9.84316 17.9442 10.0707L17.4898 10.1577C16.4129 10.3388 15.2001 10.7195 13.9835 11.9027C13.3376 11.267 12.3886 10.7339 11.4782 10.3344C10.7852 10.0303 10.1126 9.80184 9.6095 9.66348L9.16763 9.55549C8.74154 9.46758 8.33999 9.34792 8.00747 9.22253C7.68116 9.09947 7.42397 8.97122 7.27519 8.8642L6.39553 6.02376C7.4748 6.76082 8.52134 7.60854 10.0456 8.25965L10.3665 8.39085C10.4761 8.43368 10.7426 8.57387 11.0875 8.76443C11.431 8.95425 11.85 9.19236 12.2627 9.42915C13.087 9.90213 13.8901 10.372 14.0191 10.4392L14.0431 10.4508L14.0632 10.4339C14.1095 10.3956 14.2828 10.2791 14.5491 10.1144C14.8142 9.95049 15.1695 9.74026 15.5791 9.51462C16.2968 9.11921 17.1803 8.67755 18.0353 8.35502L18.3994 8.22388C19.8267 7.74123 20.8962 7.09597 21.8457 6.54617C21.6926 6.98727 21.4861 7.60944 21.3019 8.1564Z" fill="white" stroke="#0254A6" stroke-width="0.0807666"/>
<path d="M20.3009 9.87627L20.8737 6.83656L20.8351 5.40069L22.697 5.31821L21.775 9.15104L20.3009 9.87627Z" fill="#0254A6"/>
<path d="M7.70245 9.44391L7.3394 6.37208L7.47633 4.94224L5.62449 4.73231L6.28155 8.61933L7.70245 9.44391Z" fill="#0254A6"/>
</g>
</g>
<rect opacity="0.1" x="37.3459" y="-2.35091" width="18.7225" height="52.8634" transform="rotate(-19.8383 37.3459 -2.35091)" fill="url(#paint3_linear_2027_163245)"/>
<g filter="url(#filter0_d_2027_163245)">
<rect x="6.09528" y="8" width="38.0952" height="7.61905" rx="3.80952" fill="url(#paint4_linear_2027_163245)"/>
</g>
<g filter="url(#filter1_d_2027_163245)">
<rect x="6.09528" y="20.5714" width="17.1429" height="4.57143" rx="2.28571" fill="#97D2FC"/>
</g>
<g filter="url(#filter2_d_2027_163245)">
<rect x="25.1428" y="20.5714" width="7.61905" height="4.57143" rx="2.28571" fill="#97D2FC"/>
</g>
<g opacity="0.8" filter="url(#filter3_d_2027_163245)">
<rect x="6.09528" y="35.4286" width="66.2857" height="4.57143" rx="2.28571" fill="url(#paint5_linear_2027_163245)"/>
</g>
<g opacity="0.1" filter="url(#filter4_i_2027_163245)">
<path d="M51.8214 4.90993C50.8554 2.24406 50.33 1.73788 48.381 1.45105V0L60.0921 0V1.45105C57.6516 1.7885 57.2957 2.36217 58.16 4.9943L63.1428 19.741L68.0238 5.14615C68.939 2.37904 68.617 1.80537 66.0917 1.45105V0L73.9048 0V1.45105C71.9049 1.73788 71.4304 2.22719 70.3626 5.19677L63.4139 24.3809H58.8041L51.8214 4.90993Z" fill="url(#paint6_linear_2027_163245)"/>
</g>
</g>
<defs>
<filter id="filter0_d_2027_163245" x="3.89263" y="6.89868" width="42.5005" height="12.0243" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1.10132"/>
<feGaussianBlur stdDeviation="1.10132"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.04 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2027_163245"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2027_163245" result="shape"/>
</filter>
<filter id="filter1_d_2027_163245" x="3.89263" y="19.4701" width="21.5482" height="8.97671" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1.10132"/>
<feGaussianBlur stdDeviation="1.10132"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.04 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2027_163245"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2027_163245" result="shape"/>
</filter>
<filter id="filter2_d_2027_163245" x="22.9402" y="19.4701" width="12.0243" height="8.97671" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1.10132"/>
<feGaussianBlur stdDeviation="1.10132"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.04 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2027_163245"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2027_163245" result="shape"/>
</filter>
<filter id="filter3_d_2027_163245" x="3.89263" y="34.3272" width="70.691" height="8.97671" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1.10132"/>
<feGaussianBlur stdDeviation="1.10132"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.04 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2027_163245"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2027_163245" result="shape"/>
</filter>
<filter id="filter4_i_2027_163245" x="48.381" y="0" width="25.5238" height="24.381" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="1.10132"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0154745 0 0 0 0 0.14184 0 0 0 0 0.233105 0 0 0 0.6 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_2027_163245"/>
</filter>
<linearGradient id="paint0_linear_2027_163245" x1="0" y1="0" x2="80" y2="0" gradientUnits="userSpaceOnUse">
<stop stop-color="#1FC3E3"/>
<stop offset="0.483433" stop-color="#6AE7FF"/>
<stop offset="1" stop-color="#1FC3E3"/>
</linearGradient>
<linearGradient id="paint1_linear_2027_163245" x1="0" y1="0" x2="80" y2="0" gradientUnits="userSpaceOnUse">
<stop stop-color="#98D4FF"/>
<stop offset="1" stop-color="#1F91E3"/>
</linearGradient>
<linearGradient id="paint2_linear_2027_163245" x1="66.4859" y1="-0.277779" x2="1.94016" y2="45.5468" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0.4"/>
<stop offset="1" stop-color="white" stop-opacity="0.1"/>
</linearGradient>
<linearGradient id="paint3_linear_2027_163245" x1="37.3459" y1="1.77905" x2="50.0111" y2="27.6601" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint4_linear_2027_163245" x1="25.1429" y1="8" x2="25.1429" y2="15.619" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#DCF0FF"/>
</linearGradient>
<linearGradient id="paint5_linear_2027_163245" x1="39.2381" y1="35.4286" x2="39.2381" y2="40" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#DCF0FF"/>
</linearGradient>
<linearGradient id="paint6_linear_2027_163245" x1="73.9048" y1="12.1905" x2="48.381" y2="12.1905" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0.4"/>
<stop offset="1" stop-color="white"/>
</linearGradient>
</defs>
</svg>
