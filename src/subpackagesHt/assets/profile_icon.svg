<svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_ii_2201_226826)">
<path d="M11 23C8.79086 23 7 21.2091 7 19V10C7 7.79086 8.79086 6 11 6H25C27.2091 6 29 7.79086 29 10V19C29 21.2091 27.2091 23 25 23H11Z" fill="#1466E1"/>
<path d="M11 23C8.79086 23 7 21.2091 7 19V10C7 7.79086 8.79086 6 11 6H25C27.2091 6 29 7.79086 29 10V19C29 21.2091 27.2091 23 25 23H11Z" fill="url(#paint0_linear_2201_226826)" fill-opacity="0.4"/>
</g>
<path d="M11 6.54395H25C26.909 6.54395 28.4561 8.09102 28.4561 10V19C28.4561 20.909 26.909 22.4561 25 22.4561H11C9.09102 22.4561 7.54395 20.909 7.54395 19V10C7.54395 8.09102 9.09102 6.54395 11 6.54395Z" stroke="url(#paint1_linear_2201_226826)" stroke-width="1.08696"/>
<foreignObject x="-2.90083" y="3.09917" width="33.8017" height="28.8017"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(2.95px);clip-path:url(#bgblur_0_2201_226826_clip_path);height:100%;width:100%"></div></foreignObject><g filter="url(#filter1_iii_2201_226826)" data-figma-bg-blur-radius="5.90083">
<path d="M7 26C4.79086 26 3 24.2091 3 22V13C3 10.7909 4.79086 9 7 9H21C23.2091 9 25 10.7909 25 13V22C25 24.2091 23.2091 26 21 26H7Z" fill="url(#paint2_linear_2201_226826)"/>
<path d="M7 9.54395H21C22.909 9.54395 24.4561 11.091 24.4561 13V22C24.4561 23.909 22.909 25.4561 21 25.4561H7C5.09102 25.4561 3.54395 23.909 3.54395 22V13C3.54395 11.091 5.09102 9.54395 7 9.54395Z" stroke="url(#paint3_linear_2201_226826)" stroke-width="1.08696"/>
</g>
<g filter="url(#filter2_di_2201_226826)">
<path d="M7.34375 22.375C6.23918 22.375 5.30391 21.4225 5.85333 20.4643C6.02501 20.1648 6.23772 19.8869 6.48786 19.6392C7.22043 18.9136 8.214 18.506 9.25 18.506C10.286 18.506 11.2796 18.9136 12.0121 19.6392C12.2623 19.8869 12.475 20.1648 12.6467 20.4643C13.1961 21.4225 12.2608 22.375 11.1562 22.375H7.34375ZM9.25 18.0223C7.63135 18.0223 6.32031 16.7238 6.32031 15.1205C6.32031 13.5173 7.63135 12.2188 9.25 12.2188C10.8687 12.2188 12.1797 13.5173 12.1797 15.1205C12.1797 16.7238 10.8687 18.0223 9.25 18.0223Z" fill="url(#paint4_linear_2201_226826)"/>
</g>
<g filter="url(#filter3_di_2201_226826)">
<path d="M21.0443 20.4177H19.2886C18.9221 20.4177 18.625 20.7148 18.625 21.0813C18.625 21.4478 18.9221 21.7449 19.2886 21.7449H21.0443C21.4109 21.7449 21.708 21.4478 21.708 21.0813C21.708 20.7148 21.4109 20.4177 21.0443 20.4177Z" fill="url(#paint5_linear_2201_226826)"/>
<path d="M21.0443 17.0995H14.6011C14.2346 17.0995 13.9375 17.3966 13.9375 17.7631C13.9375 18.1296 14.2346 18.4267 14.6011 18.4267H21.0443C21.4109 18.4267 21.708 18.1296 21.708 17.7631C21.708 17.3966 21.4109 17.0995 21.0443 17.0995Z" fill="url(#paint6_linear_2201_226826)"/>
<path d="M21.0443 13.7812H16.1636C15.7971 13.7812 15.5 14.0784 15.5 14.4449C15.5 14.8114 15.7971 15.1085 16.1636 15.1085H21.0443C21.4109 15.1085 21.708 14.8114 21.708 14.4449C21.708 14.0784 21.4109 13.7812 21.0443 13.7812Z" fill="url(#paint7_linear_2201_226826)"/>
</g>
<defs>
<filter id="filter0_ii_2201_226826" x="7" y="5.15702" width="23.686" height="19.5289" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="1.68595" dy="1.68595"/>
<feGaussianBlur stdDeviation="1.68595"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.5 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_2201_226826"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-0.842975"/>
<feGaussianBlur stdDeviation="0.842975"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.66 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_2201_226826" result="effect2_innerShadow_2201_226826"/>
</filter>
<filter id="filter1_iii_2201_226826" x="-2.90083" y="3.09917" width="33.8017" height="28.8017" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="1.68595" dy="1.68595"/>
<feGaussianBlur stdDeviation="1.68595"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.5 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_2201_226826"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-0.842975"/>
<feGaussianBlur stdDeviation="0.842975"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.66 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_2201_226826" result="effect2_innerShadow_2201_226826"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-0.842975"/>
<feGaussianBlur stdDeviation="0.842975"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.35 0"/>
<feBlend mode="normal" in2="effect2_innerShadow_2201_226826" result="effect3_innerShadow_2201_226826"/>
</filter>
<clipPath id="bgblur_0_2201_226826_clip_path" transform="translate(2.90083 -3.09917)"><path d="M7 26C4.79086 26 3 24.2091 3 22V13C3 10.7909 4.79086 9 7 9H21C23.2091 9 25 10.7909 25 13V22C25 24.2091 23.2091 26 21 26H7Z"/>
</clipPath><filter id="filter2_di_2201_226826" x="4.12304" y="11.4346" width="10.2539" height="13.2927" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.784117"/>
<feGaussianBlur stdDeviation="0.784117"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.169932 0 0 0 0 0.404803 0 0 0 0 0.733622 0 0 0 0.63 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2201_226826"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2201_226826" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-0.47047"/>
<feGaussianBlur stdDeviation="0.0784117"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0740097 0 0 0 0 0.259687 0 0 0 0 0.537056 0 0 0 0.19 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_2201_226826"/>
</filter>
<filter id="filter3_di_2201_226826" x="12.3693" y="12.9971" width="10.907" height="11.1003" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.784117"/>
<feGaussianBlur stdDeviation="0.784117"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.169932 0 0 0 0 0.404803 0 0 0 0 0.733622 0 0 0 0.63 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2201_226826"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2201_226826" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-0.47047"/>
<feGaussianBlur stdDeviation="0.0784117"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0740097 0 0 0 0 0.259687 0 0 0 0 0.537056 0 0 0 0.19 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_2201_226826"/>
</filter>
<linearGradient id="paint0_linear_2201_226826" x1="18" y1="6" x2="18" y2="23" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="0.515" stop-color="#63A0FC" stop-opacity="0.5"/>
<stop offset="1" stop-color="#1466E1"/>
</linearGradient>
<linearGradient id="paint1_linear_2201_226826" x1="18" y1="6" x2="18" y2="23" gradientUnits="userSpaceOnUse">
<stop stop-color="#3882F3"/>
<stop offset="1" stop-color="white" stop-opacity="0.4"/>
</linearGradient>
<linearGradient id="paint2_linear_2201_226826" x1="14" y1="9" x2="14" y2="26" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="0.515" stop-color="#63A0FC" stop-opacity="0.5"/>
<stop offset="1" stop-color="#1466E1"/>
</linearGradient>
<linearGradient id="paint3_linear_2201_226826" x1="14" y1="9" x2="14" y2="26" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="white" stop-opacity="0.4"/>
</linearGradient>
<linearGradient id="paint4_linear_2201_226826" x1="10.8209" y1="12.2187" x2="10.8209" y2="22.375" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="0.227063" stop-color="white"/>
<stop offset="0.516381" stop-color="#DBEAFF"/>
<stop offset="0.649401" stop-color="white"/>
<stop offset="0.96" stop-color="#C5DDFF"/>
</linearGradient>
<linearGradient id="paint5_linear_2201_226826" x1="19.3852" y1="13.7812" x2="19.3852" y2="21.7449" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="0.19" stop-color="#C5DDFF"/>
<stop offset="0.401488" stop-color="white"/>
<stop offset="0.565" stop-color="#DBEAFF"/>
<stop offset="0.79" stop-color="white"/>
<stop offset="0.96" stop-color="#C5DDFF"/>
</linearGradient>
<linearGradient id="paint6_linear_2201_226826" x1="19.3852" y1="13.7812" x2="19.3852" y2="21.7449" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="0.19" stop-color="#C5DDFF"/>
<stop offset="0.401488" stop-color="white"/>
<stop offset="0.565" stop-color="#DBEAFF"/>
<stop offset="0.79" stop-color="white"/>
<stop offset="0.96" stop-color="#C5DDFF"/>
</linearGradient>
<linearGradient id="paint7_linear_2201_226826" x1="19.3852" y1="13.7812" x2="19.3852" y2="21.7449" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="0.19" stop-color="#C5DDFF"/>
<stop offset="0.401488" stop-color="white"/>
<stop offset="0.565" stop-color="#DBEAFF"/>
<stop offset="0.79" stop-color="white"/>
<stop offset="0.96" stop-color="#C5DDFF"/>
</linearGradient>
</defs>
</svg>
