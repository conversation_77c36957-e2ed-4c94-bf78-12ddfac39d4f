<svg width="57" height="57" viewBox="0 0 57 57" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_ii_2141_212837)">
<path d="M44.1677 37.9588H23.1471C22.7987 37.9588 22.4645 37.8203 22.2182 37.574C21.9718 37.3276 21.8334 36.9934 21.8334 36.645V12.9969C21.8334 12.6485 21.9718 12.3143 22.2182 12.0679C22.4645 11.8215 22.7987 11.6831 23.1471 11.6831H44.1677C44.5161 11.6831 44.8503 11.8215 45.0967 12.0679C45.343 12.3143 45.4815 12.6485 45.4815 12.9969V36.645C45.4815 36.9934 45.343 37.3276 45.0967 37.574C44.8503 37.8203 44.5161 37.9588 44.1677 37.9588Z" fill="url(#paint0_linear_2141_212837)"/>
<path d="M44.1677 37.9588H23.1471C22.7987 37.9588 22.4645 37.8203 22.2182 37.574C21.9718 37.3276 21.8334 36.9934 21.8334 36.645V12.9969C21.8334 12.6485 21.9718 12.3143 22.2182 12.0679C22.4645 11.8215 22.7987 11.6831 23.1471 11.6831H44.1677C44.5161 11.6831 44.8503 11.8215 45.0967 12.0679C45.343 12.3143 45.4815 12.6485 45.4815 12.9969V36.645C45.4815 36.9934 45.343 37.3276 45.0967 37.574C44.8503 37.8203 44.5161 37.9588 44.1677 37.9588Z" fill="url(#paint1_linear_2141_212837)" fill-opacity="0.4"/>
</g>
<path d="M23.1468 11.7827H44.1673C44.449 11.7827 44.7209 11.8811 44.9369 12.0581L45.0257 12.1382C45.2533 12.3657 45.3811 12.6748 45.3812 12.9966V36.645C45.3812 36.9267 45.2838 37.1986 45.1068 37.4146L45.0257 37.5034C44.7981 37.7309 44.4892 37.8589 44.1673 37.8589H23.1468C22.8653 37.8588 22.5942 37.7604 22.3783 37.5835L22.2884 37.5034C22.0609 37.2758 21.933 36.9669 21.933 36.645V12.9966C21.933 12.7151 22.0315 12.4439 22.2084 12.228L22.2884 12.1382C22.4875 11.9391 22.749 11.8162 23.0267 11.7886L23.1468 11.7827Z" stroke="url(#paint2_linear_2141_212837)" stroke-width="0.2"/>
<foreignObject x="6.18253" y="9.95581" width="37.6481" height="40.2756"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(3.5px);clip-path:url(#bgblur_0_2141_212837_clip_path);height:100%;width:100%"></div></foreignObject><g filter="url(#filter1_iii_2141_212837)" data-figma-bg-blur-radius="7">
<path d="M16.1825 43.2315C14.5257 43.2315 13.1825 41.8883 13.1825 40.2315V19.9558C13.1825 18.299 14.5257 16.9558 16.1825 16.9558H33.8306C35.4875 16.9558 36.8306 18.299 36.8306 19.9558V40.2315C36.8306 41.8883 35.4875 43.2315 33.8306 43.2315H16.1825Z" fill="url(#paint3_linear_2141_212837)"/>
<path d="M16.1825 17.0554H33.831C35.4324 17.0556 36.7304 18.3543 36.7304 19.9558V40.2312C36.7304 41.8327 35.4324 43.1314 33.831 43.1316H16.1825C14.5809 43.1316 13.2821 41.8328 13.2821 40.2312V19.9558C13.2821 18.3542 14.5809 17.0554 16.1825 17.0554Z" stroke="url(#paint4_linear_2141_212837)" stroke-width="0.2"/>
</g>
<g opacity="0.4" filter="url(#filter2_f_2141_212837)">
<path d="M34.341 29.4229V38.8293" stroke="white"/>
</g>
<g filter="url(#filter3_di_2141_212837)">
<path d="M19.7518 24.8389C19.7518 24.1133 20.34 23.5251 21.0656 23.5251H28.9483C29.6739 23.5251 30.2621 24.1133 30.2621 24.8389C30.2621 25.5645 29.6739 26.1527 28.9483 26.1527H21.0656C20.34 26.1527 19.7518 25.5645 19.7518 24.8389ZM19.7518 30.0941C19.7518 29.3685 20.34 28.7803 21.0656 28.7803H28.9483C29.6739 28.7803 30.2621 29.3685 30.2621 30.0941C30.2621 30.8196 29.6739 31.4078 28.9483 31.4078H21.0656C20.34 31.4078 19.7518 30.8196 19.7518 30.0941ZM19.7518 35.3492C19.7518 34.6236 20.34 34.0354 21.0656 34.0354H25.007C25.7326 34.0354 26.3208 34.6236 26.3208 35.3492C26.3208 36.0748 25.7326 36.663 25.007 36.663H21.0656C20.34 36.663 19.7518 36.0748 19.7518 35.3492Z" fill="url(#paint5_linear_2141_212837)"/>
</g>
<defs>
<filter id="filter0_ii_2141_212837" x="21.8334" y="10.6831" width="25.6481" height="29.2756" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="2" dy="2"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.5 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_2141_212837"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-1"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.66 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_2141_212837" result="effect2_innerShadow_2141_212837"/>
</filter>
<filter id="filter1_iii_2141_212837" x="6.18253" y="9.95581" width="37.6481" height="40.2756" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="2" dy="2"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.5 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_2141_212837"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-1"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.66 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_2141_212837" result="effect2_innerShadow_2141_212837"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-1"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.35 0"/>
<feBlend mode="normal" in2="effect2_innerShadow_2141_212837" result="effect3_innerShadow_2141_212837"/>
</filter>
<clipPath id="bgblur_0_2141_212837_clip_path" transform="translate(-6.18253 -9.95581)"><path d="M16.1825 43.2315C14.5257 43.2315 13.1825 41.8883 13.1825 40.2315V19.9558C13.1825 18.299 14.5257 16.9558 16.1825 16.9558H33.8306C35.4875 16.9558 36.8306 18.299 36.8306 19.9558V40.2315C36.8306 41.8883 35.4875 43.2315 33.8306 43.2315H16.1825Z"/>
</clipPath><filter id="filter2_f_2141_212837" x="30.841" y="26.4229" width="7" height="15.4065" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="1.5" result="effect1_foregroundBlur_2141_212837"/>
</filter>
<filter id="filter3_di_2141_212837" x="17.7518" y="22.5251" width="14.5103" height="17.1379" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.169932 0 0 0 0 0.404803 0 0 0 0 0.733622 0 0 0 0.63 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2141_212837"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2141_212837" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-0.6"/>
<feGaussianBlur stdDeviation="0.1"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0740097 0 0 0 0 0.259687 0 0 0 0 0.537056 0 0 0 0.19 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_2141_212837"/>
</filter>
<linearGradient id="paint0_linear_2141_212837" x1="33.6574" y1="11.6831" x2="33.6574" y2="37.9588" gradientUnits="userSpaceOnUse">
<stop stop-color="#1466E1"/>
<stop offset="1" stop-color="#0B387B"/>
</linearGradient>
<linearGradient id="paint1_linear_2141_212837" x1="33.6574" y1="11.6831" x2="33.6574" y2="37.9588" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="0.515" stop-color="#63A0FC" stop-opacity="0.5"/>
<stop offset="1" stop-color="#1466E1"/>
</linearGradient>
<linearGradient id="paint2_linear_2141_212837" x1="33.6574" y1="11.6831" x2="33.6574" y2="37.9588" gradientUnits="userSpaceOnUse">
<stop stop-color="#3882F3"/>
<stop offset="1" stop-color="white" stop-opacity="0.4"/>
</linearGradient>
<linearGradient id="paint3_linear_2141_212837" x1="25.0066" y1="16.9558" x2="25.0066" y2="43.2315" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="0.515" stop-color="#63A0FC" stop-opacity="0.5"/>
<stop offset="1" stop-color="#1466E1"/>
</linearGradient>
<linearGradient id="paint4_linear_2141_212837" x1="25.0066" y1="16.9558" x2="25.0066" y2="43.2315" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="white" stop-opacity="0.4"/>
</linearGradient>
<linearGradient id="paint5_linear_2141_212837" x1="25.007" y1="23.5251" x2="25.007" y2="36.663" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="0.19" stop-color="#C5DDFF"/>
<stop offset="0.401488" stop-color="white"/>
<stop offset="0.565" stop-color="#DBEAFF"/>
<stop offset="0.79" stop-color="white"/>
<stop offset="0.96" stop-color="#C5DDFF"/>
</linearGradient>
</defs>
</svg>
