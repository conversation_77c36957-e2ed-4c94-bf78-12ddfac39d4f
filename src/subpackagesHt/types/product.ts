/**
 * 产品类型定义
 */

// 产品类型枚举
export enum ProductType {
  EmployeeCard = 1, // 员工次卡
  PassengerHighCabin = 2, // 旅客高舱
}

// 产品有效期类型枚举
export enum EffectType {
  LongTerm = 1, // 长期有效
  SameDay = 2, // 当日有效
}

// 产品状态枚举
export enum ProductStatus {
  OnShelf = 1, // 上架
  OffShelf = 2, // 下架
}

// 产品详情类型 (根据swagger文档中的HtProduct对象)
export interface Product {
  id: number;
  productName: string;
  price: string;
  productType: ProductType;
  effectType: EffectType;
  effectTime: string;
  times: number;
  status: ProductStatus;
  image?: string;
  discribe?: string; // 产品描述，注意这里是swagger中的拼写
  deleted?: number; // 删除标识，1：正常；2：删除
}

// 产品标签类型 (用于UI展示)
export interface ProductTag {
  id: string;
  name: string;
}

// 产品UI展示类型 (包含了标签等UI需要的信息)
export interface ProductDisplay extends Omit<Product, 'price'> {
  price: number; // 转换为数字类型，方便UI展示
  tags: ProductTag[];
}

// 产品列表接口返回类型
export interface ProductListResponse {
  code: number;
  message: string;
  data: Product[];
}

// 产品详情接口返回类型
export interface ProductDetailResponse {
  code: number;
  message: string;
  data: Product;
}
