import { create } from 'zustand';
import { showToast } from '@tarojs/taro';
import request from '@ht/api/apiConfig';

// 用于防止重复请求的变量
let isRequestInProgress = false;
let lastRequestTime = 0;
const REQUEST_THROTTLE_MS = 2000; // 请求节流时间（毫秒）

/**
 * 菜单项接口
 */
export interface MenuItem {
  id: string;
  name: string;
  path: string;
  icon?: string;
  children?: MenuItem[];
  parentId?: string;
  sort?: number;
  type?: string;
  permission?: string;
  [key: string]: any; // 允许其他属性
}

/**
 * 菜单数据接口
 */
export interface MenuData {
  menus: MenuItem[];
  perms: string[];
}

const SUB_SYSTEM_CODE = 'HT-MINI';

/**
 * 菜单Store状态接口
 */
interface MenuState {
  // 菜单数据
  menuData: MenuData;
  // 加载状态
  loading: boolean;
  // 是否已初始化
  initialized: boolean;
  // 获取菜单数据
  fetchMenuData: (forceRefresh?: boolean) => Promise<void>;
  // 检查用户是否有特定权限
  hasPermission: (permission: string) => boolean;
  // 根据路径查找菜单项
  findMenuByPath: (path: string) => MenuItem | null;
}

/**
 * 菜单Store
 */
export const useMenuStore = create<MenuState>((set, get) => ({
  // 初始状态
  menuData: {
    menus: [],
    perms: [],
  },
  loading: false,
  initialized: false,

  // 获取菜单数据
  fetchMenuData: async (forceRefresh = false) => {
    // 检查是否有请求正在进行中
    if (isRequestInProgress) {
      // console.log('已有菜单数据请求正在进行中，忽略此次请求');
      return;
    }

    // 检查距离上次请求的时间间隔
    const now = Date.now();
    if (!forceRefresh && now - lastRequestTime < REQUEST_THROTTLE_MS) {
      // console.log(`距离上次请求时间不足${REQUEST_THROTTLE_MS}ms，忽略此次请求`);
      return;
    }

    // 如果不是强制刷新，且已初始化，则直接使用现有数据
    if (!forceRefresh && get().initialized) {
      // console.log('菜单数据已初始化，直接使用store中的数据:', get().menuData);
      return;
    }

    // 设置请求锁和加载状态
    isRequestInProgress = true;
    lastRequestTime = now;
    set({ loading: true });

    try {
      // 调用接口获取菜单数据
      const res = await request({
        path: '/uc/api/auth/menu',
        method: 'GET',
        query: {
          subSystemCode: SUB_SYSTEM_CODE,
        },
        isloading: true, // 我们自己管理loading状态
      });

      // console.log('从接口获取菜单数据成功:', res);

      // 检查接口返回是否成功
      if (res && res.code === 200 && res.data) {
        // 构建菜单数据
        const newMenuData: MenuData = {
          menus: res.data.menus || [],
          perms: res.data.perms[SUB_SYSTEM_CODE] || [],
        };

        // 更新菜单数据状态和初始化标志
        set({ menuData: newMenuData, initialized: true });
        // console.log('菜单数据已更新到store');
      } else {
        // console.error('获取菜单数据失败:', res);
        showToast({
          title: res?.message || '获取菜单数据失败',
          icon: 'none',
        });
      }
    } catch (error) {
      // console.error('获取菜单数据出错:', error);
      showToast({
        title: '获取菜单数据失败',
        icon: 'none',
      });
    } finally {
      // 释放请求锁和加载状态
      isRequestInProgress = false;
      set({ loading: false });
    }
  },

  // 检查用户是否有特定权限
  hasPermission: (permission: string): boolean => {
    return get().menuData.perms.includes(permission);
  },

  // 根据路径查找菜单项
  findMenuByPath: (path: string): MenuItem | null => {
    const findInMenus = (
      menus: MenuItem[],
      targetPath: string,
    ): MenuItem | null => {
      for (const menu of menus) {
        if (menu.path === targetPath) {
          return menu;
        }
        if (menu.children && menu.children.length > 0) {
          const found = findInMenus(menu.children, targetPath);
          if (found) return found;
        }
      }
      return null;
    };

    return findInMenus(get().menuData.menus, path);
  },
}));
