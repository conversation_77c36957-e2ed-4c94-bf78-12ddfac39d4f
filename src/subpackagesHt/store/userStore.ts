import { create } from 'zustand';
import request from '@ht/api/apiConfig';

// 用于防止重复请求的变量
let isRequestInProgress = false;
let lastRequestTime = 0;
const REQUEST_THROTTLE_MS = 2000; // 请求节流时间（毫秒）

/**
 * 用户基本信息接口
 */
export interface UserInfo {
  name: string;
  jobNumber: string;
  avatar: string;
}

/**
 * 员工信息接口
 */
export interface EmployeeInfo {
  name?: string;
  phone?: string;
  jobNumber?: string;
  [key: string]: any; // 允许其他属性
}

/**
 * 验证员工信息是否包含必要字段
 */
export const isValidEmployeeInfo = (info: EmployeeInfo): boolean => {
  return !!(info && info.name && info.phone && info.jobNumber);
};

/**
 * 用户Store状态接口
 */
interface UserState {
  // 用户基本信息
  userInfo: UserInfo;
  // 完整的用户数据
  userData: any;
  // 加载状态
  loading: boolean;
  // 是否已初始化
  initialized: boolean;
  // 获取用户信息
  fetchUserInfo: (forceRefresh?: boolean) => Promise<void>;
}

/**
 * 用户Store
 */
export const useUserStore = create<UserState>((set, get) => ({
  // 初始状态
  userInfo: {
    name: '',
    jobNumber: '',
    avatar: '',
  },
  userData: null,
  loading: false,
  initialized: false,

  // 获取用户信息
  fetchUserInfo: async (forceRefresh = false) => {
    // 检查是否有请求正在进行中
    if (isRequestInProgress) {
      // console.log('已有用户信息请求正在进行中，忽略此次请求');
      return;
    }

    // 检查距离上次请求的时间间隔
    const now = Date.now();
    if (!forceRefresh && now - lastRequestTime < REQUEST_THROTTLE_MS) {
      // console.log(`距离上次请求时间不足${REQUEST_THROTTLE_MS}ms，忽略此次请求`);
      return;
    }

    // 如果不是强制刷新，且已初始化，则直接使用现有数据
    if (!forceRefresh && get().initialized) {
      // console.log('用户数据已初始化，直接使用store中的数据:', get().userInfo);
      return;
    }

    // 设置请求锁和加载状态
    isRequestInProgress = true;
    lastRequestTime = now;
    set({ loading: true });

    try {
      // 如果不是强制刷新，且已初始化，则直接使用现有数据
      if (!forceRefresh && get().initialized) {
        // console.log('用户数据已初始化，直接使用store中的数据:', get().userInfo);
        return;
      }

      // 本地没有数据或强制刷新，调用接口
      const res = await request({
        path: '/api/high-tank/app/user/employee_card',
        method: 'GET',
        isloading: true, // 我们自己管理loading状态
      });

      // console.log('从接口获取用户信息成功:', res);

      // 检查接口返回是否成功
      if (res && res.data) {
        // 构建用户信息
        const newUserInfo = {
          name: res.data.employeeName || '',
          jobNumber: res.data.jobNumber || '',
          avatar: '', // API 中没有头像字段，使用空字符串
        };

        // 更新状态
        set({
          userInfo: newUserInfo,
          userData: res.data,
          initialized: true,
        });

        // console.log('用户信息已更新到store');
      }
    } finally {
      // 释放请求锁和加载状态
      isRequestInProgress = false;
      set({ loading: false });
    }
  },
}));
