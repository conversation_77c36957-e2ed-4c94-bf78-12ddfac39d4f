import { useEffect, useState } from 'react';
import Taro from '@tarojs/taro';
import { View, Image } from '@tarojs/components';
import { useMenu } from '@ht/utils/hooks/useMenu';
import { getImageUrl } from '@/subpackagesHt/utils/image';
import './index.less';

/**
 * 高舱子包入口页面
 *
 * 根据用户权限决定跳转到哪个页面：
 * - 如果用户有 buy_menu 权限，跳转到高舱购页面
 * - 如果用户有 check_menu 权限，跳转到核销页面
 * - 如果用户有 my_menu 权限，跳转到我的页面
 * - 如果用户没有任何权限，显示无权限提示
 */
export default function Entry() {
  const { hasPermission, loading, menuData } = useMenu();
  const [isRedirecting, setIsRedirecting] = useState(false);

  useEffect(() => {
    // 等待菜单数据加载完成
    if (!loading && menuData.perms.length > 0 && !isRedirecting) {
      setIsRedirecting(true);

      // 检查用户权限并跳转到相应页面
      if (hasPermission('buy_menu')) {
        // 用户有高舱购权限，跳转到高舱购页面
        // console.log('用户有高舱购权限，跳转到高舱购页面');
        Taro.reLaunch({ url: '/subpackagesHt/pages/home/<USER>' });
      } else if (hasPermission('check_menu')) {
        // 用户有核销权限，跳转到核销页面
        // console.log('用户有核销权限，跳转到核销页面');
        Taro.reLaunch({ url: '/subpackagesHt/pages/verification/index' });
      } else if (hasPermission('my_menu')) {
        // 用户有我的权限，跳转到我的页面
        // console.log('用户有我的权限，跳转到我的页面');
        Taro.reLaunch({ url: '/subpackagesHt/pages/me/index' });
      } else {
        // 用户没有任何权限，显示无权限提示
        // console.log('用户没有任何权限，返回主页');
        Taro.showToast({
          title: '您没有访问权限',
          icon: 'none',
          duration: 2000,
        });

        // 延迟返回主页
        setTimeout(() => {
          Taro.reLaunch({ url: '/pages/index/index' });
        }, 2000);
      }
    }
  }, [loading, menuData, hasPermission, isRedirecting]);

  return (
    <View className="entry-page">
      {/* 加载中提示 */}
      <View className="loading-container">
        <Image
          className="loading-icon"
          src={getImageUrl('loading.svg')}
          mode="aspectFit"
        />
        <View className="loading-text">
          {loading ? '正在加载权限信息...' : '正在跳转...'}
        </View>
      </View>
    </View>
  );
}
