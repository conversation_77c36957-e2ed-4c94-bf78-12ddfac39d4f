import { useEffect } from 'react';
import { View, Text, Image } from '@tarojs/components';
import {
  navigateTo,
  getStorageSync,
  setStorageSync,
  showModal,
} from '@tarojs/taro';
import CustomTabBar from '@ht/components/CustomTabBar';
import NavBar from '@ht/components/NavBar';
import request from '@ht/api/apiConfig';
import { useMenu } from '@ht/utils/hooks/useMenu';
import { getImageUrl } from '@/subpackagesHt/utils/image';
import './index.less';

export default function Home() {
  // 使用菜单hook获取菜单数据
  const { menuData, hasPermission } = useMenu();

  // 跳转到员工次卡列表页面
  const goToEmployeeCardList = () => {
    navigateTo({
      url: '/subpackagesHt/pages/product/employee-card-list/index?productType=1', // productType=1 表示员工次卡
    });
  };

  // 跳转到高舱休息室列表页面
  const goToLoungeList = () => {
    navigateTo({
      url: '/subpackagesHt/pages/product/employee-card-list/index?productType=2', // productType=2 表示旅客高舱
    });
  };

  // 跳转到我的销售订单页面
  const goToMyOrders = () => {
    // 只传递type参数，表示查看"我的销售订单"
    navigateTo({
      url: `/subpackagesHt/pages/order/index?type=my`, // 只传递type参数
    });
  };

  // 跳转到全部销售订单页面
  const goToAllSalesInfo = () => {
    // 传递type参数，表示查看"全部销售订单"
    navigateTo({
      url: '/subpackagesHt/pages/order/index?type=all', // 传递type参数
    });
  };

  // 判断是否展示补全信息弹窗
  useEffect(() => {
    // 在useEffect内部定义一个异步函数
    const checkFirstVisit = async () => {
      // 检查本地是否有 ht-first_InformationCompletion 字段
      const htFirstInformationCompletion = getStorageSync(
        'ht-first_InformationCompletion',
      );

      // 首次访问，且有权限，调用补充信息验证接口
      if (
        !htFirstInformationCompletion &&
        menuData['perms']?.length &&
        hasPermission('my_InformationCompletion')
      ) {
        // console.log('首次访问，调用补充信息验证接口');

        try {
          // 调用补充信息验证接口
          const res = await request({
            path: '/api/high-tank/app/user/validateReplenishInfo',
            method: 'POST',
            isloading: true, // 不显示加载提示
          });

          if (res && res.code === 200) {
            // 接口调用成功，存储标记
            setStorageSync('ht-first_InformationCompletion', 'Y');

            // 如果 res.data 为 false，表示用户信息不完整，需要补全
            if (!res.data) {
              // 显示补全信息弹窗
              showModal({
                title: '补全信息提示',
                content: '检测到您还未补全信息，是否立即补全？',
                confirmText: '确认',
                cancelText: '取消',
                confirmColor: '#1663F8',
                success: result => {
                  if (result.confirm) {
                    // 用户点击确认，跳转到补全信息页面
                    navigateTo({
                      url: '/subpackagesHt/pages/me/complete-info/index',
                    });
                  }
                },
              });
            }
          }
        } catch (error) {
          // console.error('补充信息验证失败:', error);
        }
      }
    };

    // 立即调用异步函数
    checkFirstVisit();
  }, [menuData, hasPermission]);

  return (
    <View className="home-page">
      {/* 顶部标题栏 */}
      <NavBar title="高舱休息室销售系统" showBack={false} />

      {/* 产品专区 - 只有当用户有任一产品权限时才显示 */}
      {(hasPermission('buy_subCard') || hasPermission('buy_lounge')) && (
        <View className="product-section">
          <View className="section-header">
            <Text className="section-title">产品专区</Text>
          </View>

          <View className="card-group">
            {/* 员工次卡 - 需要 buy_subCard 权限 */}
            {hasPermission('buy_subCard') && (
              <View
                className="product-card cyan-card"
                onClick={goToEmployeeCardList}
              >
                <View className="card-bg">
                  <Image
                    className="card-v-image"
                    src={getImageUrl('employee_card.svg')}
                    mode="aspectFill"
                  />
                </View>
              </View>
            )}

            {/* 旅客高舱 - 需要 buy_lounge 权限 */}
            {hasPermission('buy_lounge') && (
              <View className="product-card blue-card" onClick={goToLoungeList}>
                <View className="card-bg">
                  <Image
                    className="card-v-image"
                    src={getImageUrl('high_cabin_card.svg')}
                    mode="aspectFill"
                  />
                </View>
              </View>
            )}
          </View>
        </View>
      )}

      {/* 订单管理 - 只有当用户有任一订单管理权限时才显示 */}
      {(hasPermission('buy_myOrder') || hasPermission('buy_allOrder')) && (
        <View className="product-section">
          <View className="section-header">
            <Text className="section-title">订单管理</Text>
          </View>

          <View className="order-management">
            {/* 我的销售订单 - 需要 buy_myOrder 权限 */}
            {hasPermission('buy_myOrder') && (
              <View className="order-item" onClick={goToMyOrders}>
                <Image
                  className="order-icon"
                  src={getImageUrl('order_icon.svg')}
                  mode="aspectFit"
                />
                <Text className="order-title">我的销售订单</Text>
              </View>
            )}

            {/* 全部销售订单 - 需要 buy_allOrder 权限 */}
            {hasPermission('buy_allOrder') && (
              <View className="order-item" onClick={goToAllSalesInfo}>
                <Image
                  className="order-icon"
                  src={getImageUrl('sales_info_icon.svg')}
                  mode="aspectFit"
                />
                <Text className="order-title">全部销售订单</Text>
              </View>
            )}
          </View>
        </View>
      )}

      {/* 自定义底部标签栏 */}
      <CustomTabBar />
    </View>
  );
}
