/* 首页样式 */
.home-page {
  min-height: 100vh;
  background-color: #f0f4fa;
  padding-bottom: 100px; /* 为底部标签栏留出空间 */
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}

/* 页面背景渐变 */
.home-page::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 240px;
  background: linear-gradient(
    to bottom,
    #cfe1ff 0%,
    #e3eeff 54.08%,
    #f0f4fa 100%
  );
  z-index: -1;
}

/* 产品专区 */
.product-section {
  width: 100vw;
  box-sizing: border-box;
  padding: 16px;
  margin-top: 31px;
}

.section-header {
  margin-bottom: 8px;
  display: flex;
  align-items: center;
}

.section-title {
  font-size: 16.64px;
  font-weight: 600;
  color: #1d1f20;
  font-family: 'MiSans', sans-serif;
  line-height: 1.5em;
}

/* 卡片组 */
.card-group {
  display: flex;
  flex-direction: column;
  gap: 8.32px;
}

/* 订单管理 */
.order-management {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  gap: 16px;
  margin-top: 8px;
}

.order-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: #ffffff;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.05);
}

.order-icon {
  width: 48px;
  height: 48px;
  margin-bottom: 8px;
}

.order-title {
  font-size: 14px;
  font-weight: 500;
  color: #4f5170;
  line-height: 1.5em;
  font-family: 'MiSans', sans-serif;
  text-align: center;
}

/* 产品卡片 */
.product-card {
  background-color: #fff;
  border-radius: 8px;
  overflow: hidden;
  height: 93.6px;
  position: relative;
}

/* 青色卡片 - 员工次卡 */
.cyan-card {
  background: #ffffff;
  box-shadow: inset 0px 0px 12.48px rgba(255, 255, 255, 0.8);
  position: relative;
  overflow: hidden;
}

.cyan-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #2bd5f8 0%, #c4f5ff 87%, #65dbf3 100%);
  z-index: -1;
}

/* 蓝色卡片 - 旅客高舱 */
.blue-card {
  background: #ffffff;
  box-shadow: inset 0px 0px 16px rgba(255, 255, 255, 0.9);
  position: relative;
  overflow: hidden;
}

.blue-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #4987ff 3.15%, #78a6ff 100%);
  z-index: -1;
}

.card-bg {
  position: relative;
  width: 100%;
  height: 100%;
}

.card-circle {
  position: absolute;
  width: 40px;
  height: 40px;
  left: -22px;
  top: -23px;
  background-color: rgba(255, 255, 255, 0.4);
  border-radius: 50%;
  border: 1px solid;
  border-image: linear-gradient(135deg, #ffffff, rgba(255, 255, 255, 0)) 1;
  filter: blur(2.08px);
}

.card-v-image {
  position: absolute;
  width: 100%;
  height: 100%;
  right: 0;
  top: 0;
}

.card-mask {
  position: absolute;
  width: 100px;
  height: 100px;
  right: -48px;
  top: 25px;
  opacity: 0.7;
}

.card-content {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 12.48px;
  z-index: 1;
}

.card-title {
  font-size: 14.56px;
  font-weight: 600;
  color: #206597;
  font-family: 'MiSans', sans-serif;
  line-height: 1.57em;
}

.card-button {
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 4.16px;
  padding: 0 6.24px;
}

.button-text {
  font-size: 10.4px;
  font-weight: 500;
  color: #206597;
  line-height: 1.8em;
  font-family: 'MiSans', sans-serif;
}
