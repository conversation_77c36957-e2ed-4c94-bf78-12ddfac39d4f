import { View, Text } from '@tarojs/components';

interface DetailRowProps {
  label: string;
  value: string | number;
  className?: string;
}

export default function DetailRow({
  label,
  value,
  className = '',
}: DetailRowProps) {
  return (
    <View className={`detail-row ${className}`}>
      <Text className="detail-label">{label}</Text>
      <Text className="detail-value">{value || '-'}</Text>
    </View>
  );
}
