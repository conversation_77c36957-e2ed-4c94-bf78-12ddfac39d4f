/* 订单详情页面样式 */
.order-detail-page {
  min-height: 100vh;
  background-color: #f0f4fa;
  display: flex;
  flex-direction: column;
  position: relative;
}

/* 页面背景渐变 */
.order-detail-page::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 240px;
  background: linear-gradient(
    to bottom,
    #cfe1ff 0%,
    #e3eeff 54.08%,
    #f0f4fa 100%
  );
  z-index: -1;
}

/* 页面内容 */
.page-body {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 16px;
  width: 100%;
  box-sizing: border-box;
}

/* 加载中状态 */
.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  width: 100%;
}

.loading-text {
  font-family: 'MiSans', sans-serif;
  font-size: 14px;
  color: #9aa2ca;
}

/* 订单卡片 */
.order-card {
  background-color: #ffffff;
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.05);
  border: 1px solid #ffffff;
  margin-bottom: 16px;
}

/* 取消订单信息卡片 */
.cancel-info-card {
  border: 1px solid #e6eaf0;
  background-color: #f9f9f9;
}

/* 退款信息卡片 */
.refund-info-card {
  border: 1px solid #e6eaf0;
  background-color: #ffffff;
}

/* 退款信息列表 */
.refund-info-list {
  display: flex;
  flex-direction: column;
}

.refund-info-item {
  display: flex;
  flex-direction: column;
  margin-bottom: 16px;
}

.refund-info-item:last-child {
  margin-bottom: 0;
}

/* 退款状态头部 */
.refund-status-header {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
}

.refund-status-icon {
  width: 32px;
  height: 32px;
  border-radius: 16px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.refund-status-icon.green {
  background-color: #00b42a;
}

.refund-status-icon.blue {
  background-color: #0052d9;
}

.refund-status-icon.orange {
  background-color: #ff7d00;
}

.refund-status-icon.purple {
  background-color: #722ed1;
}

.refund-status-icon.red {
  background-color: #f5222d;
}

.status-icon-text {
  font-family: 'MiSans', sans-serif;
  font-size: 16px;
  font-weight: 500;
  color: #ffffff;
  line-height: 1;
}

.refund-status-title {
  font-family: 'MiSans', sans-serif;
  font-size: 16px;
  font-weight: 500;
  color: #1d1f20;
  line-height: 1.5em;
}

/* 退款信息详情 */
.refund-info-details {
  display: flex;
  flex-direction: row;
  margin-left: 16px;
}

.refund-info-line {
  width: 1px;
  background-color: #e6eaf0;
  margin-right: 16px;
  position: relative;
  flex-shrink: 0;
}

.refund-info-line::before {
  content: '';
  position: absolute;
  top: 0;
  left: -4px;
  width: 8px;
  height: 8px;
  border-radius: 4px;
  background-color: #e6eaf0;
}

.refund-info-line.last {
  background-color: transparent;
}

.refund-info-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
  flex: 1;
  padding-bottom: 16px;
}

.refund-detail-row {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  gap: 16px;
  word-wrap: break-word;
  word-break: break-all;
}

.refund-detail-label {
  font-family: 'MiSans', sans-serif;
  font-size: 14px;
  font-weight: 400;
  color: #4f5170;
  line-height: 1.57em;
  width: 70px;
  flex-shrink: 0;
}

.refund-detail-value {
  font-family: 'MiSans', sans-serif;
  font-size: 14px;
  font-weight: 400;
  color: #1d1f20;
  line-height: 1.57em;
  flex: 1;
  word-wrap: break-word;
  word-break: break-all;
}

/* 卡片头部 */
.card-header {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  position: relative;
}

.card-title {
  font-family: 'MiSans', sans-serif;
  font-size: 16px;
  font-weight: 500;
  color: #1d1f20;
  line-height: 1.5em;
}

.product-card {
  position: relative;
}

/* 状态印章基础样式 */
.status-stamp {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 60px;
  height: 60px;
  border-radius: 30px;
  display: flex;
  justify-content: center;
  align-items: center;
  transform: rotate(30deg);
}

/* 待支付状态 - 红色 */
.status-pending-payment {
  background-color: #ff4d4f;
}

/* 已取消状态 - 灰色 */
.status-cancelled {
  background-color: #86909c;
}

/* 待使用状态 - 蓝色 */
.status-pending-use {
  background-color: #0052d9;
}

/* 退款中状态 - 橙色 */
.status-refunding {
  background-color: #ff7d00;
}

/* 已退款状态 - 绿色 */
.status-refunded {
  background-color: #00b42a;
}

/* 使用中状态 - 绿色 */
.status-using {
  background-color: #00b42a;
}

/* 已使用状态 - 灰色 */
.status-used {
  background-color: #737578;
}

/* 退款失败状态 - 红色 */
.status-refund-failed {
  background-color: #f5222d;
}

.stamp-text {
  font-family: 'MiSans', sans-serif;
  font-size: 14px;
  font-weight: 500;
  color: #ffffff;
  line-height: 1.5em;
}

/* 订单头部 */
.order-header {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 4px 0 12px;
}

.order-title-container {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.order-type-tag {
  display: flex;
  align-items: center;
  padding: 0 4px;
  height: 20px;
  border-radius: 0 6px 0 6px;
}

.order-type-tag.blue {
  background-color: #d7e5ff;
  box-shadow: inset 0px 0px 16px rgba(255, 255, 255, 0.9);
}

.order-type-tag.cyan {
  background-color: #a4edfd;
  box-shadow: inset 0px 0px 12.48px rgba(255, 255, 255, 0.8);
}

.tag-text {
  font-family: 'MiSans', sans-serif;
  font-size: 12px;
  font-weight: 500;
  line-height: 1.67em;
}

.order-type-tag.blue .tag-text {
  color: #0650df;
}

.order-type-tag.cyan .tag-text {
  color: #206597;
}

.order-title {
  font-family: 'MiSans', sans-serif;
  font-size: 16px;
  font-weight: 500;
  color: #1d1f20;
  line-height: 1.5em;
}

/* 核销码部分 */
.verification-code-section {
  margin: 12px 0;
}

.verification-code-label {
  font-family: 'MiSans', sans-serif;
  font-size: 14px;
  font-weight: 400;
  color: #4f5170;
  line-height: 1.57em;
  margin-bottom: 8px;
}

.verification-code-container {
  display: flex;
  flex-direction: row;
  gap: 8px;
}

.verification-code-digit {
  font-family: 'MiSans', sans-serif;
  font-size: 16px;
  font-weight: 500;
  color: #0052d9;
  line-height: 1.5em;
}

/* 优惠券码部分 */
.coupon-code-section {
  margin: 12px 0;
}

.coupon-code-label {
  font-family: 'MiSans', sans-serif;
  font-size: 14px;
  font-weight: 400;
  color: #4f5170;
  line-height: 1.57em;
  margin-bottom: 8px;
}

.coupon-code-value {
  font-family: 'MiSans', sans-serif;
  font-size: 16px;
  font-weight: 500;
  color: #1d1f20;
  line-height: 1.5em;
  word-break: break-all;
  display: block;
  margin-bottom: 8px;
}

.coupon-code-value:last-child {
  margin-bottom: 0;
}

/* 产品详情 */
.product-details {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  margin-top: 16px;
}

.product-detail-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.product-detail-label {
  font-family: 'MiSans', sans-serif;
  font-size: 14px;
  font-weight: 400;
  color: #4f5170;
  line-height: 1.57em;
  margin-bottom: 4px;
}

.product-detail-value {
  font-family: 'MiSans', sans-serif;
  font-size: 14px;
  font-weight: 500;
  color: #1d1f20;
  line-height: 1.57em;
}

/* 订单详情 */
.order-details {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.detail-row {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  gap: 16px;
}

.detail-label {
  font-family: 'MiSans', sans-serif;
  font-size: 14px;
  font-weight: 400;
  color: #4f5170;
  line-height: 1.57em;
  width: 80px;
  flex-shrink: 0;
}

.detail-value {
  font-family: 'MiSans', sans-serif;
  font-size: 14px;
  font-weight: 400;
  color: #1d1f20;
  line-height: 1.57em;
  flex: 1;
}

/* 底部占位区域 */
.bottom-placeholder {
  height: 60px; /* 确保内容不被固定按钮遮挡 */
  width: 100%;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}

/* 固定在底部的按钮区域 */
.fixed-bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 16px;
  background-color: #ffffff;
  box-shadow: 0px -2px 8px rgba(0, 0, 0, 0.05);
  z-index: 100;
  display: flex;
  flex-direction: row;
  justify-content: center;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}

.action-button {
  width: 100%;
  height: 44px;
  border-radius: 8px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-family: 'MiSans', sans-serif;
  font-size: 16px;
  font-weight: 500;
  line-height: 1.5em;
}

.refund-button {
  background-color: #ffffff;
  color: #0052d9;
  border: 1px solid #0052d9;
}

.offline-refund-button {
  background-color: #ffffff;
  color: #f5222d;
  border: 1px solid #f5222d;
}

/* 双按钮容器 */
.dual-button-container {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  width: 100%;
  gap: 16px;
}

/* 取消订单按钮 */
.cancel-button {
  background-color: #ffffff;
  color: #0052d9;
  border: 1px solid #0052d9;
  flex: 1;
}

/* 去支付按钮 */
.pay-button {
  background-color: #0052d9;
  color: #ffffff;
  border: none;
  flex: 1;
}

/* 模态框通用样式 */
.refund-modal-overlay,
.cancel-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.refund-modal {
  width: 90%;
  max-width: 600px;
  background-color: #ffffff;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  display: flex;
  flex-direction: column;
}

.refund-modal-content {
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.refund-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.refund-modal-title {
  font-family: 'MiSans', sans-serif;
  font-size: 20px;
  font-weight: 600;
  color: #000000;
  line-height: 1.33em;
}

.refund-modal-close {
  width: 24px;
  height: 24px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
}

.close-icon {
  font-size: 24px;
  color: #6f7c8f;
  line-height: 1;
}

.refund-modal-divider {
  height: 1px;
  background-color: #e6eaf0;
  width: 100%;
}

.refund-modal-body {
  display: flex;
  flex-direction: column;
  gap: 16px;
  width: 100%;
}

.refund-form-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
  width: 100%;
}

.refund-form-label {
  font-family: 'MiSans', sans-serif;
  font-size: 16px;
  font-weight: 600;
  color: #23242d;
  line-height: 1.33em;
}

.refund-input-container {
  background-color: #f2f4fa;
  border-radius: 8px;
  padding: 6px 8px;
}

.refund-reason-input {
  width: 100%;
  height: 120px;
  background-color: transparent;
  border: none;
  font-family: 'MiSans', sans-serif;
  font-size: 14px;
  color: #1d1f20;
  line-height: 1.57em;
  box-sizing: border-box;
  resize: none;
}

.refund-reason-input::placeholder {
  color: #9aa2ca;
}

.refund-tag-group {
  display: flex;
  flex-direction: row;
  gap: 12px;
  flex-wrap: wrap;
}

.refund-tag {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 4px 12px;
  border-radius: 4px;
  background-color: #ffffff;
  border: 1px solid #9aa2ca;
}

.refund-tag.active {
  background-color: rgba(22, 99, 248, 0.1);
  border: 1px solid #0052d9;
}

.refund-tag-text {
  font-family: 'MiSans', sans-serif;
  font-size: 14px;
  font-weight: 500;
  color: #4f5170;
  line-height: 1.33em;
}

.refund-tag.active .refund-tag-text {
  color: #0052d9;
}

.refund-modal-footer {
  display: flex;
  justify-content: space-between;
  padding: 12px 16px;
  border-top: 1px solid #e6eaf0;
}

.refund-cancel-btn {
  flex: 1;
  height: 48px;
  background-color: #ffffff;
  color: #0052d9;
  border: 1px solid #0052d9;
  border-radius: 12px;
  font-family: 'MiSans', sans-serif;
  font-size: 16px;
  font-weight: 400;
  line-height: 48px;
  margin-right: 8px;
}

.refund-submit-btn {
  flex: 1;
  height: 48px;
  background-color: #0052d9;
  color: #ffffff;
  border: none;
  border-radius: 12px;
  font-family: 'MiSans', sans-serif;
  font-size: 16px;
  font-weight: 600;
  line-height: 48px;
  margin-left: 8px;
}

/* 取消订单模态框样式 */
.cancel-modal {
  width: 80%;
  max-width: 320px;
  background-color: #ffffff;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.cancel-modal-content {
  padding: 24px 16px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.cancel-icon-container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 80px;
  height: 80px;
  background-color: #fff2e8;
  border-radius: 40px;
}

.cancel-icon {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 40px;
  height: 40px;
  background-color: #ff7d00;
  border-radius: 20px;
}

.cancel-icon-text {
  font-family: 'MiSans', sans-serif;
  font-size: 24px;
  font-weight: 600;
  color: #ffffff;
  line-height: 1;
}

.cancel-modal-title {
  font-family: 'MiSans', sans-serif;
  font-size: 18px;
  font-weight: 600;
  color: #1d1f20;
  line-height: 1.33em;
}

.cancel-modal-text {
  font-family: 'MiSans', sans-serif;
  font-size: 14px;
  font-weight: 400;
  color: #4f5170;
  line-height: 1.57em;
  text-align: center;
}

.cancel-modal-buttons {
  display: flex;
  justify-content: space-between;
  width: 100%;
  gap: 12px;
  margin-top: 8px;
}

.cancel-btn {
  flex: 1;
  height: 44px;
  background-color: #ffffff;
  color: #0052d9;
  border: 1px solid #0052d9;
  border-radius: 8px;
  font-family: 'MiSans', sans-serif;
  font-size: 16px;
  font-weight: 400;
  line-height: 44px;
}

.confirm-btn {
  flex: 1;
  height: 44px;
  background-color: #0052d9;
  color: #ffffff;
  border: none;
  border-radius: 8px;
  font-family: 'MiSans', sans-serif;
  font-size: 16px;
  font-weight: 500;
  line-height: 44px;
}

/* 产品使用信息卡片样式 - 员工次卡专用 */
.product-use-info-card {
  background-color: #ffffff;
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.05);
  border: 1px solid #ffffff;
  margin-bottom: 16px;
}

.product-use-info-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.product-use-info-item {
  background-color: #f8f9fb;
  border-radius: 8px;
  padding: 16px;
  border: 1px solid #e6eaf0;
}

/* 用户信息头部 */
.use-info-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  background: #fff;
  border-radius: 12px;
  padding: 12px 16px;
}

.user-info {
  display: flex;
  gap: 4px;
  align-items: center;
}

.user-name {
  font-family: 'MiSans', sans-serif;
  font-size: 16px;
  font-weight: 500;
  color: #1d1f20;
  line-height: 1.5em;
}

.user-no {
  font-family: 'MiSans', sans-serif;
  font-size: 14px;
  font-weight: 400;
  color: #4f5170;
  line-height: 1.57em;
}

.user-type-tag {
  display: flex;
  align-items: center;
  padding: 2px 4px;
  height: 16px;
  border-radius: 4px;
}

.user-type-tag.employee {
  background-color: #e8effd;
  color: #0052d9;
  border: 1px solid #0052d9;
}

.user-type-tag.friend {
  background-color: #f4fff6;
  color: #00b42a;
  border: 1px solid #00b42a;
}

.user-type-tag .tag-text {
  font-family: 'MiSans', sans-serif;
  font-size: 12px;
  font-weight: 500;
  line-height: 16px;
}

/* 航班信息和使用信息区域 */
.flight-info-section,
.usage-info-section {
  margin-bottom: 16px;
}

.flight-info-section:last-child,
.usage-info-section:last-child {
  margin-bottom: 0;
}

.section-title {
  font-family: 'MiSans', sans-serif;
  font-size: 14px;
  font-weight: 500;
  color: #1d1f20;
  line-height: 1.57em;
  margin-bottom: 8px;
}

.flight-info-details,
.usage-info-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
}
