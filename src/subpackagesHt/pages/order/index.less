/* 全部销售订单页面样式 */
.all-orders-page {
  min-height: 100vh;
  background-color: #f0f4fa;
  display: flex;
  flex-direction: column;
  position: relative;
}

/* 页面背景渐变 */
.all-orders-page::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 240px;
  background: linear-gradient(
    to bottom,
    #cfe1ff 0%,
    #e3eeff 54.08%,
    #f0f4fa 100%
  );
  z-index: -1;
}

/* 页面内容 */
.page-body {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 16px;
  width: 100%;
  box-sizing: border-box;
}

/* 搜索栏 */
.search-bar {
  display: flex;
  flex-direction: row;
  align-items: center;
  background-color: #ffffff;
  border-radius: 8px;
  padding: 5px 8px;
  margin-bottom: 8px;
}

.search-icon {
  width: 24px;
  height: 24px;
  color: #9aa2ca;
  margin-right: 4px;
}

.search-input {
  flex: 1;
  font-family: 'MiSans', sans-serif;
  font-size: 14px;
  font-weight: 400;
  color: #4f5170;
  line-height: 1.57em;
}

.placeholder {
  color: #9aa2ca;
}

/* 状态标签栏 */
.status-bar {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-bottom: 8px;
}

.status-tabs {
  min-width: 75%;
  // flex: 1;
  white-space: nowrap;
}

.status-tab {
  display: inline-flex;
  justify-content: center;
  align-items: center;
  padding: 5px 8px;
  margin-right: 4px;
  border-radius: 6px;
}

.status-tab.active {
  background-color: #ffffff;
}

.tab-text {
  font-family: 'MiSans', sans-serif;
  font-size: 14px;
  font-weight: 400;
  color: #4c6787;
  line-height: 1.57em;
}

.status-tab.active .tab-text {
  font-weight: 500;
  color: #0052d9;
}

.status-divider {
  width: 1px;
  height: 16px;
  background-color: #c5cef9;
  margin: 0 5px;
}

.filter-button {
  display: flex;
  flex-direction: row;
  align-items: center;
  white-space: nowrap;
  gap: 4px;
  padding: 5px 8px;
  border-radius: 6px;
}

.filter-icon {
  width: 24px;
  height: 24px;
  color: #4c6787;
}

.filter-text {
  font-family: 'MiSans', sans-serif;
  font-size: 14px;
  font-weight: 400;
  color: #4c6787;
  line-height: 1.57em;
}

/* 订单列表 */
.order-list {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 8px 0;
}

/* 订单卡片 */
.order-card {
  background-color: #ffffff;
  border-radius: 12px;
  padding: 12px 16px;
  box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.05);
  border: 1px solid #ffffff;
  margin-bottom: 16px; /* 添加底部间距，分隔每个订单卡片 */
}

/* 最后一个订单卡片不需要底部间距 */
.order-card:last-child {
  margin-bottom: 0;
}

/* 加载中状态 */
.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100px;
  width: 100%;
}

.loading-text {
  font-family: 'MiSans', sans-serif;
  font-size: 14px;
  color: #9aa2ca;
}

/* 空列表提示 */
.empty-list {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  width: 100%;
}

.empty-text {
  font-family: 'MiSans', sans-serif;
  font-size: 14px;
  color: #9aa2ca;
}

/* 订单头部 */
.order-header {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 4px 0;
}

.order-title-container {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 4px;
  flex: 1;
}

.order-type-tag {
  display: flex;
  align-items: center;
  padding: 0 4px;
  height: 20px;
  border-radius: 0 6px 0 6px;
}

.order-type-tag.blue {
  background-color: #d7e5ff;
  box-shadow: inset 0px 0px 16px rgba(255, 255, 255, 0.9);
}

.order-type-tag.cyan {
  background-color: #a4edfd;
  box-shadow: inset 0px 0px 12.48px rgba(255, 255, 255, 0.8);
}

.tag-text {
  font-family: 'MiSans', sans-serif;
  font-size: 12px;
  font-weight: 500;
  line-height: 1.67em;
}

.order-type-tag.blue .tag-text {
  color: #0650df;
}

.order-type-tag.cyan .tag-text {
  color: #206597;
}

.order-title {
  font-family: 'MiSans', sans-serif;
  font-size: 14px;
  font-weight: 500;
  color: #1d1f20;
  line-height: 1.57em;
  flex: 1;
  word-wrap: break-word;
  word-break: break-all;
  overflow: hidden;
}

.order-status {
  display: flex;
  justify-content: center;
  align-items: center;
}

.status-text {
  font-family: 'MiSans', sans-serif;
  font-size: 12px;
  font-weight: 500;
  line-height: 1.67em;
}

/* 待支付状态 - 红色 */
.status-pending-payment {
  color: #ff4d4f;
}

/* 已取消状态 - 灰色 */
.status-cancelled {
  color: #86909c;
}

/* 待使用状态 - 蓝色 */
.status-pending-use {
  color: #0052d9;
}

/* 退款中状态 - 橙色 */
.status-refunding {
  color: #ff7d00;
}

/* 已退款状态 - 绿色 */
.status-refunded {
  color: #00b42a;
}

/* 使用中状态 - 绿色 */
.status-using {
  color: #00b42a;
}

/* 已使用状态 - 灰色 */
.status-used {
  color: #737578;
}

/* 退款失败状态 - 红色 */
.status-refund-failed {
  color: #f5222d;
}

/* 分割线 */
.order-divider {
  height: 1px;
  background-color: #9aa2ca;
  opacity: 0.2;
  margin: 8px 0;
}

/* 订单详情 */
.order-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.detail-row {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 12px;
}

.detail-label {
  font-family: 'MiSans', sans-serif;
  font-size: 12px;
  font-weight: 400;
  color: #4f5170;
  line-height: 1.67em;
  width: 48px;
}

.detail-value {
  font-family: 'MiSans', sans-serif;
  font-size: 12px;
  font-weight: 400;
  color: #4f5170;
  line-height: 1.67em;
}

/* 筛选弹窗样式 */
.filter-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.filter-modal {
  width: 90%;
  max-width: 600px;
  background-color: #ffffff;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  display: flex;
  flex-direction: column;
}

.filter-modal-content {
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.filter-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.filter-modal-title {
  font-family: 'MiSans', sans-serif;
  font-size: 20px;
  font-weight: 600;
  color: #000000;
  line-height: 1.33em;
}

.filter-modal-close {
  width: 24px;
  height: 24px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
}

.close-icon {
  width: 24px;
  height: 24px;
  color: #6f7c8f;
}

.filter-modal-divider {
  height: 1px;
  background-color: #e6eaf0;
  width: 100%;
  margin: 8px 0;
}

.filter-section {
  display: flex;
  flex-direction: column;
  gap: 12px;
  width: 100%;
}

.filter-section-title {
  font-family: 'MiSans', sans-serif;
  font-size: 16px;
  font-weight: 600;
  color: #23242d;
  line-height: 1.33em;
}

.filter-options {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  width: 100%;
}

.filter-option {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 7px 20px;
  border-radius: 8px;
  background-color: #ffffff;
  border: 1px solid #9aa2ca;
  cursor: pointer;
}

.filter-option.active {
  background-color: rgba(22, 99, 248, 0.1);
  border: 1px solid #0052d9;
}

.filter-option-text {
  font-family: 'MiSans', sans-serif;
  font-size: 14px;
  font-weight: 500;
  color: #4f5170;
  line-height: 1.33em;
}

.filter-option.active .filter-option-text {
  color: #0052d9;
}

/* 日期选择器样式 */
.date-picker-container {
  display: flex;
  align-items: center;
  width: 100%;
  margin-top: 8px;
}

.date-picker-item {
  flex: 1;
  height: 40px;
}

.date-picker-value {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 12px;
  height: 40px;
  background-color: #ffffff;
  border: 1px solid #9aa2ca;
  border-radius: 8px;
}

.date-text {
  font-family: 'MiSans', sans-serif;
  font-size: 14px;
  font-weight: 400;
  color: #4f5170;
  line-height: 1.33em;
}

.date-icon {
  width: 16px;
  height: 16px;
  color: #6f7c8f;
}

.date-separator {
  margin: 0 8px;
  font-family: 'MiSans', sans-serif;
  font-size: 14px;
  font-weight: 400;
  color: #4f5170;
}

.filter-modal-footer {
  display: flex;
  justify-content: space-between;
  padding: 12px 16px;
  gap: 10px;
  border-top: 1px solid #f0f4fa;
}

.filter-cancel-btn {
  flex: 1;
  height: 48px;
  background-color: #ffffff;
  color: #0052d9;
  border: 1px solid #0052d9;
  border-radius: 12px;
  font-family: 'MiSans', sans-serif;
  font-size: 16px;
  font-weight: 400;
  line-height: 48px;
}

.filter-confirm-btn {
  flex: 1;
  height: 48px;
  background-color: #0052d9;
  color: #ffffff;
  border: none;
  border-radius: 12px;
  font-family: 'MiSans', sans-serif;
  font-size: 16px;
  font-weight: 600;
  line-height: 48px;
}
