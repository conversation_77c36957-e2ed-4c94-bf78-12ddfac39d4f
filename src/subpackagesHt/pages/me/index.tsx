import { View, Text, Image } from '@tarojs/components';
import { useLoad, navigateTo } from '@tarojs/taro';
import CustomTabBar from '@ht/components/CustomTabBar';
import NavBar from '@ht/components/NavBar';
import { useUserInfo } from '@ht/utils/hooks/useUserInfo';
import { useMenu } from '@ht/utils/hooks/useMenu';
import { getImageUrl } from '@ht/utils/image';
import './index.less';

// 定义菜单项接口
interface MenuItem {
  id: string;
  title: string;
  icon: string;
  path: string;
  permission?: string; // 权限码，可选
}

export default function Me() {
  // 使用自定义hook获取用户信息
  const { userInfo } = useUserInfo();
  // 使用菜单hook获取权限控制函数
  const { hasPermission } = useMenu();

  // 功能菜单项
  const menuItems: MenuItem[] = [
    {
      id: 'coupon',
      title: '我的优惠券',
      icon: getImageUrl('coupon_icon.svg'),
      path: '/subpackagesHt/pages/me/coupon/index',
      permission: 'my_discountCoupon', // 我的优惠券权限码
    },
    {
      id: 'record',
      title: '使用记录',
      icon: getImageUrl('record_icon.svg'),
      path: '/subpackagesHt/pages/me/record/index',
      permission: 'my_usageRecord', // 使用记录权限码
    },
    {
      id: 'profile',
      title: '个人信息',
      icon: getImageUrl('profile_icon.svg'),
      path: '/subpackagesHt/pages/me/profile/index',
      permission: 'my_personalDetails', // 个人信息权限码
    },
    {
      id: 'employee-card',
      title: '我的员工卡',
      icon: getImageUrl('employee_card_icon.svg'),
      path: '/subpackagesHt/pages/me/employee-card/index',
      permission: 'my_myEmployeeCard', // 我的员工卡权限码
    },
    {
      id: 'complete-info',
      title: '补全信息',
      icon: getImageUrl('complete_info_icon.svg'),
      path: '/subpackagesHt/pages/me/complete-info/index',
      permission: 'my_InformationCompletion', // 补全信息权限码
    },
  ];

  useLoad(() => {
    // console.log('个人中心页面加载');
    // 页面加载时可以选择刷新用户信息
    // refreshUserInfo(); // 如果需要强制刷新，可以取消注释
  });

  // 处理菜单项点击
  const handleMenuItemClick = (path: string) => {
    navigateTo({ url: path });
  };

  return (
    <View className="me-page">
      {/* 顶部标题栏 */}
      <NavBar title="高舱购" showBack={false} />

      {/* 用户信息区域 */}
      <View className="user-info">
        <View className="avatar-container">
          {userInfo.avatar ? (
            <Image className="avatar" src={userInfo.avatar} mode="aspectFill" />
          ) : (
            <View className="avatar-placeholder">
              <Text className="avatar-placeholder-text">
                {userInfo.name ? userInfo.name.charAt(0) : ''}
              </Text>
            </View>
          )}
        </View>
        <View className="user-details">
          <Text className="user-name">{userInfo.name || ''}</Text>
          <View className="employee-id">
            <Text className="id-text">No. {userInfo.jobNumber || ''}</Text>
          </View>
        </View>
      </View>

      {/* 功能菜单区域 */}
      <View className="menu-container">
        {menuItems.map(item => {
          // 检查用户是否有该菜单项的权限
          // 如果没有定义权限码，默认显示
          if (item.permission && !hasPermission(item.permission)) {
            return null; // 没有权限，不显示该菜单项
          }

          return (
            <View
              key={item.id}
              className="menu-item"
              onClick={() => handleMenuItemClick(item.path)}
            >
              <View className="menu-icon">
                <Image className="icon" src={item.icon} mode="aspectFit" />
              </View>
              <Text className="menu-title">{item.title}</Text>
              <View className="arrow-icon">
                <Image
                  className="arrow"
                  src={getImageUrl('arrow_right_icon.svg')}
                  mode="aspectFit"
                />
              </View>
            </View>
          );
        })}
      </View>

      <CustomTabBar />
    </View>
  );
}
