import { View, Text, Image } from '@tarojs/components';
import { useLoad, navigateBack, showToast } from '@tarojs/taro';
import { useState } from 'react';
import NavBar from '@ht/components/NavBar';
import request from '@ht/api/apiConfig';
import { Barcode } from 'taro-code';
import { getImageUrl } from '@/subpackagesHt/utils/image';
import './index.less';

/**
 * 我的员工卡页面
 */
export default function EmployeeCard() {
  // 定义用户信息状态
  const [userInfo, setUserInfo] = useState({
    jobNumber: '',
    id: '',
  });

  // 定义加载状态
  const [loading, setLoading] = useState(true);

  // 获取用户信息
  const fetchUserInfo = async () => {
    setLoading(true);
    try {
      // 调用接口获取用户信息
      const response = await request({
        path: '/uc/api/auth/userInfo',
        method: 'GET',
      });

      // console.log('获取用户信息成功:', response);

      if (response && response.data) {
        // 更新用户信息
        setUserInfo({
          jobNumber: response.data.jobNumber || '',
          id: response.data.id || '',
        });
      } else {
        showToast({
          title: '获取用户信息失败',
          icon: 'none',
          duration: 2000,
        });
      }
    } catch (error) {
      // console.error('获取用户信息失败:', error);
      showToast({
        title: '获取用户信息失败',
        icon: 'none',
        duration: 2000,
      });
    } finally {
      setLoading(false);
    }
  };

  useLoad(() => {
    // console.log('我的员工卡页面加载');
    // 页面加载时获取用户信息
    fetchUserInfo();
  });

  // 返回上一页
  const handleBack = () => {
    navigateBack();
  };

  return (
    <View className="employee-card-page">
      {/* 顶部导航栏 */}
      <NavBar title="我的员工卡" onBack={handleBack} />

      {/* 内容区域 */}
      <View className="card-content">
        {/* 员工卡标签 */}
        <View className="card-tag">
          <Image
            className="tag-icon"
            src={getImageUrl('employee_card_tag.svg')}
            mode="aspectFit"
          />
        </View>

        {/* 员工卡 */}
        <View className="card-container">
          <View className="qr-code-container">
            {loading ? (
              <View className="loading-container">
                <Text className="loading-text">加载中...</Text>
              </View>
            ) : (
              <Barcode
                text={userInfo.id}
                style={{ width: '100%', height: '100%' }}
              />
            )}
          </View>
          <Text className="employee-id">{userInfo.jobNumber}</Text>
        </View>

        {/* 提示信息 */}
        <View className="tip-container">
          <Image
            className="info-icon"
            src={getImageUrl('information_icon.svg')}
            mode="aspectFit"
          />
          <Text className="tip-text">使用时向接待人员出示此二维码</Text>
        </View>
      </View>
    </View>
  );
}
