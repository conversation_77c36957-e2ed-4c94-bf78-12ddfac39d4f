/* 我的员工卡页面样式 */
.employee-card-page {
  min-height: 100vh;
  background-color: #f0f4fa;
  position: relative;
}

/* 页面背景渐变 */
.employee-card-page::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 240px;
  background: linear-gradient(
    to bottom,
    #cfe1ff 0%,
    #e3eeff 54.08%,
    #f0f4fa 100%
  );
  z-index: -1;
}

/* 内容区域 */
.card-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  padding: 16px;
  margin-top: 16px;
}

/* 员工卡标签 */
.card-tag {
  width: 64px;
  height: 64px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.tag-icon {
  width: 100%;
  height: 100%;
}

/* 员工卡容器 */
.card-container {
  width: 100%;
  max-width: 320px;
  background-color: #ffffff;
  border-radius: 12px;
  padding: 16px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.qr-code-container {
  width: 100%;
  height: 100px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.qr-code {
  width: 200px;
  height: 200px;
}

/* 加载状态 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
}

.loading-text {
  font-size: 14px;
  color: #666;
  font-family: 'MiSans', sans-serif;
}

.employee-id {
  font-size: 14px;
  font-weight: 400;
  color: #1d1f20;
  font-family: 'MiSans', sans-serif;
  line-height: 1.57em;
}

/* 提示信息 */
.tip-container {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 10px;
  background-color: #fff3eb;
  border: 1px solid #ec6f1e;
  border-radius: 6px;
}

.info-icon {
  width: 16px;
  height: 16px;
}

.tip-text {
  font-size: 14px;
  font-weight: 400;
  color: #ec6f1e;
  font-family: 'MiSans', sans-serif;
  line-height: 1.57em;
}

/* 刷新按钮 */
.refresh-button {
  margin-top: 16px;
  padding: 8px 16px;
  background-color: #3a7eff;
  border-radius: 6px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
}

.refresh-text {
  font-size: 14px;
  font-weight: 500;
  color: #ffffff;
  font-family: 'MiSans', sans-serif;
  line-height: 1.57em;
}
