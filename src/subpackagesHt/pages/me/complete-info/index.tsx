import { View, Text, Input, Picker } from '@tarojs/components';
import { useState, useEffect } from 'react';
import Taro, { useLoad } from '@tarojs/taro';
import NavBar from '@ht/components/NavBar';
import request from '@ht/api/apiConfig';
import BANKS from '@ht/utils/banks';
import './index.less';

// 将银行数据从 banks.ts 文件中提取并转换为我们需要的格式
const getAllBanks = () => {
  const allBanks: any[] = [];
  // 遍历所有字母分类
  Object.keys(BANKS).forEach(letter => {
    // 将每个分类下的银行添加到列表中
    BANKS[letter].forEach(bank => {
      return allBanks.push({
        name: bank.name,
        pinyin: bank.spell.toUpperCase(),
        firstLetter: bank.spell[0].toUpperCase(),
        id: bank.id,
        code: bank.code,
      });
    });
  });
  return allBanks;
};

// 银行列表（带拼音首字母）
const BANK_LIST = getAllBanks();

// 验证函数
// 验证中文姓名（2-20个汉字，支持少数民族姓名中间加入(.)间隔）
const validateName = (name: string): boolean => {
  // 匹配2-20个汉字，允许点(.)作为间隔
  const nameRegex =
    // eslint-disable-next-line no-useless-escape
    /^[\u4e00-\u9fa5]{1}[\u4e00-\u9fa5\.]{0,18}[\u4e00-\u9fa5]{1}$/;
  return nameRegex.test(name) && name.length >= 2 && name.length <= 20;
};

// 验证身份证号码（18位）
const validateIdCard = (idCard: string): boolean => {
  // 18位身份证号码正则
  const idCardRegex = /(^\d{17}(\d|X|x)$)/;
  if (!idCardRegex.test(idCard)) {
    return false;
  }

  // 简单校验，实际应用中可以添加更复杂的校验逻辑
  return true;
};

// 验证银行卡号（16-19位数字）
const validateBankCard = (bankCard: string): boolean => {
  // 16-19位数字
  const bankCardRegex = /^\d{16,19}$/;
  return bankCardRegex.test(bankCard);
};

export default function CompleteInfoPage() {
  // 表单状态
  const [formData, setFormData] = useState({
    name: '',
    idCard: '',
    bank: '',
    bankIndex: 0,
    bankCard: '',
    customBank: '', // 自定义银行输入
    useCustomBank: false, // 是否使用自定义银行
  });

  // 表单验证状态
  const [formValidation, setFormValidation] = useState({
    name: { valid: false, message: '' },
    idCard: { valid: false, message: '' },
    bank: { valid: false, message: '' },
    bankCard: { valid: false, message: '' },
  });

  // 银行列表
  const [bankList] = useState(BANK_LIST);

  // 表单是否全部有效
  const [isFormValid, setIsFormValid] = useState(false);

  // 页面加载状态
  const [pageLoading, setPageLoading] = useState(true);

  // 页面加载时获取用户信息进行回显
  useLoad(() => {
    // console.log('补全信息页面加载');
    fetchUserReplenishInfo();
  });

  // 获取用户补全信息
  const fetchUserReplenishInfo = async () => {
    try {
      setPageLoading(true);

      const response = await request({
        path: '/api/high-tank/app/user/validateReplenishInfo',
        method: 'POST',
        isloading: false, // 我们自己管理loading状态
      });

      if (response && response.code === 200 && response.data) {
        const userInfo = response.data;

        // 回显用户信息到表单
        const newFormData = {
          name: userInfo.cardHolder || '',
          idCard: userInfo.idNo || '',
          bank: userInfo.depositBank || '',
          bankIndex: 0,
          bankCard: userInfo.bankCardNo || '',
          customBank: '',
          useCustomBank: false,
        };

        // 如果有开户银行信息，尝试在银行列表中找到对应的索引
        if (userInfo.depositBank) {
          const bankIndex = bankList.findIndex(
            bank => bank.name === userInfo.depositBank,
          );
          if (bankIndex !== -1) {
            newFormData.bankIndex = bankIndex;
            newFormData.useCustomBank = false;
          } else {
            // 如果在银行列表中找不到，使用自定义银行输入
            newFormData.customBank = userInfo.depositBank;
            newFormData.useCustomBank = true;
          }
        }

        setFormData(newFormData);

        // 验证已填写的字段
        if (newFormData.name) {
          validateField('name', newFormData.name);
        }
        if (newFormData.idCard) {
          validateField('idCard', newFormData.idCard);
        }
        if (newFormData.bank) {
          validateField('bank', newFormData.bank);
        }
        if (newFormData.bankCard) {
          validateField('bankCard', newFormData.bankCard);
        }
      }
    } catch (error) {
      // console.error('获取用户补全信息失败:', error);
      Taro.showToast({
        title: '获取用户信息失败',
        icon: 'none',
        duration: 2000,
      });
    } finally {
      setPageLoading(false);
    }
  };

  // 处理输入变化
  const handleInputChange = (field: string, value: string) => {
    setFormData({
      ...formData,
      [field]: value,
    });

    // 验证输入
    validateField(field, value);
  };

  // 验证单个字段
  const validateField = (field: string, value: string) => {
    let isValid = false;
    let message = '';

    switch (field) {
      case 'name':
        isValid = validateName(value);
        message = isValid ? '' : '姓名应为2-20个汉字';
        break;
      case 'idCard':
        isValid = validateIdCard(value);
        message = isValid ? '' : '请输入18位有效身份证号码';
        break;
      case 'bank':
        isValid = !!value;
        message = isValid ? '' : '请选择或输入开户银行';
        break;
      case 'customBank':
        isValid = !!value;
        message = isValid ? '' : '请输入开户银行';
        // 更新bank字段的验证状态
        setFormValidation(prev => ({
          ...prev,
          bank: { valid: isValid, message: message },
        }));
        break;
      case 'bankCard':
        isValid = validateBankCard(value);
        message = isValid ? '' : '请输入16-19位银行卡号';
        break;
      default:
        break;
    }

    // 更新验证状态
    if (field !== 'customBank') {
      setFormValidation(prev => ({
        ...prev,
        [field]: { valid: isValid, message: message },
      }));
    }
  };

  // 处理银行选择变化
  const handleBankChange = (e: any) => {
    const index = e.detail.value;
    const selectedBank = bankList[index].name;
    setFormData({
      ...formData,
      bankIndex: index,
      bank: selectedBank,
      useCustomBank: false,
    });

    // 验证银行选择
    validateField('bank', selectedBank);
  };

  // 切换到自定义银行输入
  const toggleCustomBank = () => {
    setFormData({
      ...formData,
      useCustomBank: !formData.useCustomBank,
      bank: formData.useCustomBank ? '' : formData.customBank,
    });

    // 验证银行字段
    validateField('bank', formData.useCustomBank ? '' : formData.customBank);
  };

  // 处理自定义银行输入
  const handleCustomBankChange = (value: string) => {
    setFormData({
      ...formData,
      customBank: value,
      bank: value,
    });

    // 验证自定义银行输入
    validateField('customBank', value);
  };

  // 检查表单是否全部有效
  useEffect(() => {
    const allValid =
      formValidation.name.valid &&
      formValidation.idCard.valid &&
      formValidation.bank.valid &&
      formValidation.bankCard.valid;

    setIsFormValid(allValid);
  }, [formValidation]);

  // 处理表单提交
  const handleSubmit = async () => {
    // 验证所有字段
    validateField('name', formData.name);
    validateField('idCard', formData.idCard);
    validateField('bank', formData.bank);
    validateField('bankCard', formData.bankCard);

    // 如果表单无效，不提交
    if (!isFormValid) {
      Taro.showToast({
        title: '请完善表单信息',
        icon: 'none',
      });
      return;
    }

    // 提交表单
    Taro.showLoading({
      title: '提交中...',
    });

    try {
      // 构建请求参数，按照 ReplenishUserInfoDto 模型
      const requestData = {
        cardHolder: formData.name, // 持卡人
        idNo: formData.idCard, // 证件号
        depositBank: formData.bank, // 开户银行
        bankCardNo: formData.bankCard, // 银行卡号
      };

      // 调用补全用户信息接口
      const response = await request({
        path: '/api/high-tank/app/user/update_userInfo',
        method: 'POST',
        body: requestData,
        isloading: false, // 我们自己管理loading状态
      });

      Taro.hideLoading();

      // 检查接口返回是否成功
      if (response && response.code === 200) {
        // 提交成功
        Taro.showToast({
          title: '提交成功',
          icon: 'success',
          duration: 2000,
          success: () => {
            // 提交成功后返回上一页
            setTimeout(() => {
              Taro.navigateBack();
            }, 2000);
          },
        });
      } else {
        // 提交失败
        Taro.showToast({
          title: response?.message || '提交失败',
          icon: 'none',
          duration: 2000,
        });
      }
    } catch (error) {
      Taro.hideLoading();
      // console.error('提交用户信息出错:', error);
      Taro.showToast({
        title: '提交失败，请稍后重试',
        icon: 'none',
        duration: 2000,
      });
    }
  };

  return (
    <View className="complete-info-page">
      {/* 导航栏 */}
      <NavBar title="补全信息" showBack />

      {/* 主体内容 */}
      <View className="body">
        {/* 页面加载状态 */}
        {pageLoading && (
          <View className="loading-container">
            <Text className="loading-text">正在加载用户信息...</Text>
          </View>
        )}
        <View className="card">
          <Text className="card-title">补全信息</Text>

          {/* 持卡人姓名 */}
          <View className="form-row">
            <Text className="label">持卡人姓名</Text>
            <View
              className={`input-container ${
                formData.name && !formValidation.name.valid ? 'input-error' : ''
              }`}
            >
              <Input
                className="input"
                placeholder="请输入"
                value={formData.name}
                onInput={e => handleInputChange('name', e.detail.value)}
              />
            </View>
            {formData.name && !formValidation.name.valid && (
              <Text className="error-message">
                {formValidation.name.message}
              </Text>
            )}
          </View>

          {/* 身份证号码 */}
          <View className="form-row">
            <Text className="label">身份证号码</Text>
            <View
              className={`input-container ${
                formData.idCard && !formValidation.idCard.valid
                  ? 'input-error'
                  : ''
              }`}
            >
              <Input
                className="input"
                placeholder="请输入"
                value={formData.idCard}
                maxlength={18}
                onInput={e => handleInputChange('idCard', e.detail.value)}
              />
            </View>
            {formData.idCard && !formValidation.idCard.valid && (
              <Text className="error-message">
                {formValidation.idCard.message}
              </Text>
            )}
          </View>
          {/* 银行卡号 */}
          <View className="form-row">
            <Text className="label">银行卡号</Text>
            <View
              className={`input-container ${
                formData.bankCard && !formValidation.bankCard.valid
                  ? 'input-error'
                  : ''
              }`}
            >
              <Input
                className="input"
                placeholder="请输入16-19位银行卡号"
                type="number"
                maxlength={19}
                value={formData.bankCard}
                onInput={e => handleInputChange('bankCard', e.detail.value)}
              />
            </View>
            {formData.bankCard && !formValidation.bankCard.valid && (
              <Text className="error-message">
                {formValidation.bankCard.message}
              </Text>
            )}
          </View>
          {/* 开户银行 */}
          <View className="form-row">
            <Text className="label">开户银行</Text>
            {!formData.useCustomBank ? (
              <>
                <Picker
                  mode="selector"
                  range={bankList.map(bank => bank.name)}
                  onChange={handleBankChange}
                  value={formData.bankIndex}
                >
                  <View
                    className={`input-container ${
                      formData.bank ? '' : 'input-placeholder'
                    }`}
                  >
                    <Text
                      className="input"
                      style={{ color: formData.bank ? '#1D1F20' : '#9AA2CA' }}
                    >
                      {formData.bank || '请选择银行'}
                    </Text>
                    <View className="arrow-icon">
                      <Text
                        className="iconfont icon-arrow-down-s-line"
                        style={{ color: '#9AA2CA' }}
                      ></Text>
                    </View>
                  </View>
                </Picker>

                {/* 切换到手动输入 */}
                <View className="toggle-input" onClick={toggleCustomBank}>
                  <Text className="toggle-text">未找到银行？点击手动输入</Text>
                </View>
              </>
            ) : (
              <>
                {/* 自定义银行输入 */}
                <View
                  className={`input-container ${
                    formData.customBank === '' && !formValidation.bank.valid
                      ? 'input-error'
                      : ''
                  }`}
                >
                  <Input
                    className="input"
                    placeholder="请输入银行名称"
                    value={formData.customBank}
                    onInput={e => handleCustomBankChange(e.detail.value)}
                  />
                </View>

                {/* 切换回选择模式 */}
                <View className="toggle-input" onClick={toggleCustomBank}>
                  <Text className="toggle-text">返回银行列表选择</Text>
                </View>
              </>
            )}

            {/* 错误提示 */}
            {!formValidation.bank.valid && formData.bank === '' && (
              <Text className="error-message">
                {formValidation.bank.message}
              </Text>
            )}
          </View>
        </View>
      </View>

      {/* 底部按钮 */}
      <View className="tab-bar">
        <View
          className={`submit-button ${
            isFormValid ? '' : 'submit-button-disabled'
          }`}
          onClick={handleSubmit}
        >
          <Text className="button-text">提交</Text>
        </View>
      </View>
    </View>
  );
}
