.complete-info-page {
  background-color: #f0f4fa;
  min-height: 100vh;
  display: flex;
  flex-direction: column;

  .body {
    flex: 1;
    padding: 16px;
    display: flex;
    flex-direction: column;

    .loading-container {
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 40px 0;

      .loading-text {
        font-family: 'MiSans', sans-serif;
        font-size: 14px;
        color: #9aa2ca;
        line-height: 1.5;
      }
    }

    .card {
      background-color: #ffffff;
      border-radius: 12px;
      padding: 16px;
      display: flex;
      flex-direction: column;
      gap: 16px;
      box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.05);

      .card-title {
        font-family: 'MiSans', sans-serif;
        font-weight: 500;
        font-size: 16px;
        line-height: 1.5;
        color: #1d1f20;
      }

      .form-row {
        display: flex;
        flex-direction: column;
        gap: 8px;

        .label {
          font-family: 'MiSans', sans-serif;
          font-weight: 400;
          font-size: 14px;
          line-height: 1.57;
          color: #4f5170;
        }

        .input-container {
          display: flex;
          align-items: center;
          background-color: #f2f4fa;
          border-radius: 8px;
          padding: 6px 8px;
          border: 1px solid transparent;
          transition: all 0.3s ease;

          &.input-error {
            border-color: #ff4d4f;
            background-color: #fff1f0;
          }

          &.input-placeholder {
            color: #9aa2ca;
          }

          &.search-container {
            margin-bottom: 8px;
          }

          .input {
            flex: 1;
            border: none;
            background: transparent;
            font-family: 'MiSans', sans-serif;
            font-weight: 400;
            font-size: 14px;
            line-height: 1.57;
            color: #1d1f20;

            &::placeholder {
              color: #9aa2ca;
            }
          }

          .arrow-icon {
            width: 20px;
            height: 20px;
            color: #9aa2ca;
          }
        }

        .error-message {
          font-family: 'MiSans', sans-serif;
          font-size: 12px;
          color: #ff4d4f;
          line-height: 1.5;
        }

        .toggle-input {
          margin-top: 8px;
          display: flex;
          justify-content: flex-end;

          .toggle-text {
            font-family: 'MiSans', sans-serif;
            font-size: 12px;
            color: #0052d9;
            line-height: 1.5;
            text-decoration: underline;
          }
        }
      }
    }
  }

  .tab-bar {
    background-color: #ffffff;
    padding: 8px 16px;
    padding-bottom: constant(safe-area-inset-bottom);
    padding-bottom: env(safe-area-inset-bottom);

    .submit-button {
      background-color: #0052d9;
      border-radius: 12px;
      padding: 12px 32px;
      display: flex;
      justify-content: center;
      align-items: center;
      transition: all 0.3s ease;

      &.submit-button-disabled {
        background-color: #b0c4de;
        opacity: 0.7;
      }

      .button-text {
        font-family: 'MiSans', sans-serif;
        font-weight: 500;
        font-size: 16px;
        line-height: 1.5;
        color: #ffffff;
      }
    }
  }
}
