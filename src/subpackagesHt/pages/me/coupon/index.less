/* 我的优惠券页面样式 */
.coupon-page {
  min-height: 100vh;
  background-color: #f0f4fa;
  position: relative;
}

/* 页面背景渐变 */
.coupon-page::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 240px;
  background: linear-gradient(
    to bottom,
    #cfe1ff 0%,
    #e3eeff 54.08%,
    #f0f4fa 100%
  );
  z-index: -1;
}

/* 标签栏 */
.tab-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 16px 8px;
  margin-bottom: 12px;
}

.tab {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 8px;
  border-radius: 6px;
  cursor: pointer;
}

.tab.active {
  background-color: #ffffff;
}

.tab-text {
  font-size: 14px;
  font-weight: 400;
  line-height: 1.57em;
  color: #4c6787;
  font-family: 'MiSans', sans-serif;
}

.tab.active .tab-text {
  font-weight: 500;
  color: #0052d9;
}

/* 优惠券列表 */
.coupon-list {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0 16px 12px;
  overflow-y: auto;
}

/* 空状态 */
.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  width: 100%;
}

.empty-text {
  font-size: 14px;
  color: #999;
  font-family: 'MiSans', sans-serif;
}

/* 加载状态 */
.loading-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  width: 100%;
}

.loading-text {
  font-size: 14px;
  color: #666;
  font-family: 'MiSans', sans-serif;
}
