/**
 * 优惠券类型
 */
export enum CouponType {
  /** 高舱券 */
  LOUNGE = 'lounge',
  /** 员工次卡 */
  EMPLOYEE_CARD = 'employee_card',
}

/**
 * 优惠券状态
 */
export enum CouponStatus {
  /** 待使用 */
  UNUSED = 'unused',
  /** 已使用 */
  USED = 'used',
  /** 已失效 */
  EXPIRED = 'expired',
  /** 不可用 */
  UNAVAILABLE = 'unavailable',
}

/**
 * 优惠券信息
 */
export interface Coupon {
  /** 唯一标识 */
  id: string;
  /** 优惠券类型 */
  type: CouponType;
  /** 优惠券名称 */
  name: string;
  /** 优惠券状态 */
  status: CouponStatus;
  /** 优惠券描述 */
  description: string;
  /** 优惠券码 */
  codeNo: string;
  /** 核销码 */
  verificationCode?: string;
  /** 使用说明 */
  discribe: string;
  /** 是否展开使用说明 */
  isExpanded?: boolean;
}
