import { View, Text } from '@tarojs/components';
import {
  useLoad,
  navigateBack,
  stopPullDownRefresh,
  usePullDownRefresh,
} from '@tarojs/taro';
import { useState } from 'react';
import NavBar from '@ht/components/NavBar';
import request from '@ht/api/apiConfig';
import CouponCard from './CouponCard';
import { Coupon, CouponStatus, CouponType } from './types';
import './index.less';

/**
 * 我的优惠券页面
 */
export default function MyCoupons() {
  // 当前选中的标签
  const [activeTab, setActiveTab] = useState<CouponStatus>(CouponStatus.UNUSED);

  // 优惠券列表
  const [coupons, setCoupons] = useState<Coupon[]>([]);
  // 加载状态
  const [loading, setLoading] = useState(false);

  // 获取优惠券列表
  const fetchCoupons = async (status?: number) => {
    try {
      setLoading(true);
      // 构建请求参数，如果有status则添加到请求中
      const params: any = {};
      if (status !== undefined) {
        params.status = status;
      }

      const res = await request({
        path: '/api/high-tank/app/productCode/list/my',
        method: 'GET',
        isloading: true,
        query: params,
      });

      // console.log('获取优惠券列表成功:', res);

      if (res && res.data) {
        // 将API返回的数据转换为组件需要的格式
        const formattedCoupons: Coupon[] = res.data.map((item: any) => {
          // 根据API返回的状态确定优惠券状态
          let couponStatus = CouponStatus.UNUSED;
          if (item.status === 2) {
            couponStatus = CouponStatus.USED;
          } else if (item.status === 3) {
            couponStatus = CouponStatus.EXPIRED;
          } else if (item.status === 4) {
            couponStatus = CouponStatus.UNAVAILABLE;
          }

          // 根据产品类型确定优惠券类型
          const type =
            item.productType === 1
              ? CouponType.EMPLOYEE_CARD
              : CouponType.LOUNGE;

          return {
            type,
            name: item.productName || '',
            status: couponStatus,
            description: item.description || '无使用门槛',
            codeNo: item.codeNo || '',
            verificationCode: item.verificationCode || '',
            discribe: item.discribe || '暂无使用说明',
            isExpanded: false,
            // 使用codeNo作为唯一标识
            id: item.codeNo || '',
          };
        });

        setCoupons(formattedCoupons);
      }
    } catch (error) {
      // console.error('获取优惠券出错:', error);
    } finally {
      setLoading(false);
    }
  };

  useLoad(() => {
    // console.log('我的优惠券页面加载');
    // 页面加载时获取优惠券列表，默认获取待使用的优惠券（status=1）
    fetchCoupons(1);
  });

  // 下拉刷新
  usePullDownRefresh(() => {
    // console.log('下拉刷新');

    // 根据当前选中的标签获取对应的status参数
    let apiStatus: number;
    switch (activeTab) {
      case CouponStatus.UNUSED:
        apiStatus = 1; // 待使用
        break;
      case CouponStatus.USED:
        apiStatus = 2; // 已使用
        break;
      case CouponStatus.EXPIRED:
        apiStatus = 3; // 已失效
        break;
      case CouponStatus.UNAVAILABLE:
        apiStatus = 4; // 不可用
        break;
      default:
        apiStatus = 1;
    }

    // 刷新优惠券列表，传递当前选中标签对应的status参数
    fetchCoupons(apiStatus).then(() => {
      // 停止下拉刷新动画
      stopPullDownRefresh();
    });
  });

  // 处理标签切换
  const handleTabChange = (tab: CouponStatus) => {
    setActiveTab(tab);

    // 根据标签获取对应的status参数
    let apiStatus: number;
    switch (tab) {
      case CouponStatus.UNUSED:
        apiStatus = 1; // 待使用
        break;
      case CouponStatus.USED:
        apiStatus = 2; // 已使用
        break;
      case CouponStatus.EXPIRED:
        apiStatus = 3; // 已失效
        break;
      case CouponStatus.UNAVAILABLE:
        apiStatus = 4; // 不可用
        break;
      default:
        apiStatus = 1;
    }

    // 调用接口获取对应状态的优惠券
    fetchCoupons(apiStatus);
  };

  // 处理展开/收起说明
  const handleToggleExpand = (codeNo: string) => {
    setCoupons(
      coupons.map(coupon => {
        if (coupon.codeNo === codeNo) {
          return { ...coupon, isExpanded: !coupon.isExpanded };
        }
        return coupon;
      }),
    );
  };

  // 根据当前标签筛选优惠券
  const filteredCoupons = coupons.filter(coupon => coupon.status === activeTab);

  // 返回上一页
  const handleBack = () => {
    navigateBack();
  };

  return (
    <View className="coupon-page">
      {/* 顶部导航栏 */}
      <NavBar title="我的优惠券" onBack={handleBack} />

      {/* 标签栏 */}
      <View className="tab-container">
        <View
          className={`tab ${activeTab === CouponStatus.UNUSED ? 'active' : ''}`}
          onClick={() => handleTabChange(CouponStatus.UNUSED)}
        >
          <Text className="tab-text">待使用</Text>
        </View>
        <View
          className={`tab ${activeTab === CouponStatus.USED ? 'active' : ''}`}
          onClick={() => handleTabChange(CouponStatus.USED)}
        >
          <Text className="tab-text">已使用</Text>
        </View>
        <View
          className={`tab ${
            activeTab === CouponStatus.EXPIRED ? 'active' : ''
          }`}
          onClick={() => handleTabChange(CouponStatus.EXPIRED)}
        >
          <Text className="tab-text">已失效</Text>
        </View>
        <View
          className={`tab ${
            activeTab === CouponStatus.UNAVAILABLE ? 'active' : ''
          }`}
          onClick={() => handleTabChange(CouponStatus.UNAVAILABLE)}
        >
          <Text className="tab-text">不可用</Text>
        </View>
      </View>

      {/* 优惠券列表 */}
      <View className="coupon-list">
        {loading ? (
          // 加载中状态
          <View className="loading-state">
            <Text className="loading-text">加载中...</Text>
          </View>
        ) : filteredCoupons.length > 0 ? (
          // 有优惠券时显示列表
          filteredCoupons.map(coupon => (
            <CouponCard
              key={coupon.id}
              coupon={coupon}
              onToggleExpand={handleToggleExpand}
            />
          ))
        ) : (
          // 无优惠券时显示空状态
          <View className="empty-state">
            <Text className="empty-text">
              暂无
              {activeTab === CouponStatus.UNUSED
                ? '待使用'
                : activeTab === CouponStatus.USED
                  ? '已使用'
                  : activeTab === CouponStatus.EXPIRED
                    ? '已失效'
                    : '不可用'}
              的优惠券
            </Text>
          </View>
        )}
      </View>
    </View>
  );
}
