import { View, Text, Image } from '@tarojs/components';
import { useLoad, navigateBack, useRouter } from '@tarojs/taro';
import { useState } from 'react';
import NavBar from '@ht/components/NavBar';
import { Barcode } from 'taro-code';
import { getImageUrl } from '@/subpackagesHt/utils/image';
import './index.less';

/**
 * 优惠券条码页面
 */
export default function CouponBarcode() {
  const router = useRouter();
  const [couponCode, setCouponCode] = useState('');
  // 优惠券类型，可用于后续根据类型显示不同样式
  // const [couponType, setCouponType] = useState('');
  // 使用couponType变量，避免未使用警告
  // console.log('当前优惠券类型:', couponType);

  useLoad(() => {
    // console.log('优惠券条码页面加载');
    // 获取路由参数
    const { code, type } = router.params;
    if (code) {
      setCouponCode(code);
    }
    if (type) {
      // setCouponType(type);
    }
  });

  // 返回上一页
  const handleBack = () => {
    navigateBack();
  };

  return (
    <View className="coupon-barcode-page">
      {/* 顶部导航栏 */}
      <NavBar title="优惠券条码" onBack={handleBack} />

      {/* 内容区域 */}
      <View className="barcode-content">
        {/* 条形码卡片 */}
        <View className="barcode-card">
          <View className="barcode-container">
            <Barcode
              text={couponCode}
              style={{ width: '100%', height: '100%' }}
            />
          </View>
          <Text className="coupon-code">{couponCode}</Text>
        </View>

        {/* 提示信息 */}
        <View className="tip-container">
          <Image
            className="info-icon"
            src={getImageUrl('information_icon.svg')}
            mode="aspectFit"
          />
          <Text className="tip-text">使用时向接待人员出示此二维码</Text>
        </View>
      </View>
    </View>
  );
}
