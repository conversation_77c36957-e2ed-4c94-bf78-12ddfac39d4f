/* 优惠券条码页面样式 */
.coupon-barcode-page {
  min-height: 100vh;
  background-color: #f0f4fa;
  position: relative;
}

/* 页面背景渐变 */
.coupon-barcode-page::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 240px;
  background: linear-gradient(
    to bottom,
    #cfe1ff 0%,
    #e3eeff 54.08%,
    #f0f4fa 100%
  );
  z-index: -1;
}

/* 内容区域 */
.barcode-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  padding: 16px;
  margin-top: 16px;
}

/* 条形码卡片 */
.barcode-card {
  width: 100%;
  max-width: 320px;
  background-color: #ffffff;
  border-radius: 12px;
  padding: 16px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.barcode-container {
  width: 100%;
  height: 100px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.barcode-image {
  width: 200px;
  height: 200px;
}

.coupon-code {
  font-size: 14px;
  font-weight: 400;
  color: #1d1f20;
  font-family: 'MiSans', sans-serif;
  line-height: 1.57em;
}

/* 提示信息 */
.tip-container {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 10px;
  background-color: #fff3eb;
  border: 1px solid #ec6f1e;
  border-radius: 6px;
}

.info-icon {
  width: 16px;
  height: 16px;
}

.tip-text {
  font-size: 14px;
  font-weight: 400;
  color: #ec6f1e;
  font-family: 'MiSans', sans-serif;
  line-height: 1.57em;
}
