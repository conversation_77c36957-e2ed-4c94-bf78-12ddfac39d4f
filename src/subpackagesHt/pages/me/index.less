/* 个人中心页面样式 */
.me-page {
  min-height: 100vh;
  background-color: #f0f4fa;
  padding-bottom: 100px; /* 为底部标签栏留出空间 */
  position: relative;
}

/* 页面背景渐变 */
.me-page::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 240px;
  background: linear-gradient(
    to bottom,
    #cfe1ff 0%,
    #e3eeff 54.08%,
    #f0f4fa 100%
  );
  z-index: -1;
}

/* 用户信息区域 */
.user-info {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 20px 16px;
  margin-top: 20px;
}

.avatar-container {
  width: 64px;
  height: 64px;
  border-radius: 50%;
  border: 3.33px solid #ffffff;
  overflow: hidden;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

.avatar {
  width: 100%;
  height: 100%;
  border-radius: 99px;
  object-fit: cover;
}

/* 头像占位符 */
.avatar-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background: linear-gradient(135deg, #0052d9, #327cf7);
  border-radius: 50%;
}

.avatar-placeholder-text {
  font-family: 'MiSans', sans-serif;
  font-size: 24px;
  font-weight: 600;
  color: #ffffff;
  line-height: 1;
}

.user-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.user-name {
  font-size: 24px;
  font-weight: 700;
  color: #0a0649;
  font-family: 'Source Han Sans CN', sans-serif;
  line-height: 1.5em;
  background: linear-gradient(to right, #2c43a0, #0a0649, #2360f8);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.employee-id {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 4px 8px;
  background-color: #327cf7;
  border-radius: 6px;
  border: 1px solid #89b5ff;
  box-shadow: inset 0px 0px 8px rgba(255, 255, 255, 1);
}

.id-text {
  font-size: 12px;
  font-weight: 500;
  color: #ffffff;
  font-family: 'Source Han Sans CN', sans-serif;
  line-height: 1.5em;
}

/* 功能菜单区域 */
.menu-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 0 16px;
  margin-top: 32px;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 16px;
  background-color: #ffffff;
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.4);
  box-shadow: inset 0px 0px 8px rgba(255, 255, 255, 0.85);
  position: relative;
  overflow: hidden;
}

.menu-icon {
  width: 24px;
  height: 24px;
  margin-right: 12px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.icon {
  width: 100%;
  height: 100%;
}

.menu-title {
  flex: 1;
  font-size: 14px;
  font-weight: 500;
  color: #2b2b32;
  font-family: 'Source Han Sans CN', sans-serif;
  line-height: 1.57em;
}

.arrow-icon {
  width: 16px;
  height: 16px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.arrow {
  width: 100%;
  height: 100%;
}
