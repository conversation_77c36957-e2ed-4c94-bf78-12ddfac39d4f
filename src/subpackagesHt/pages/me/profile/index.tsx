import { View, Text, Image } from '@tarojs/components';
import { useLoad, navigateTo, showModal, showToast } from '@tarojs/taro';
import NavBar from '@ht/components/NavBar';
import request, { logout } from '@ht/api/apiConfig';
import { useUserInfo } from '@ht/utils/hooks/useUserInfo';
import './index.less';

export default function Profile() {
  // 使用自定义hook获取用户信息
  const { userInfo, loading } = useUserInfo();

  useLoad(() => {
    // console.log('个人信息页面加载');
  });

  // 处理修改密码
  const handleChangePassword = () => {
    navigateTo({
      url: '/subpackagesHt/pages/me/change-password/index',
    });
  };

  // 处理退出登录
  const handleLogout = () => {
    showModal({
      title: '提示',
      content: '确定要退出登录吗？',
      confirmColor: '#1663F8',
      success: async res => {
        if (res.confirm) {
          // 执行退出登录逻辑
          // console.log('用户确认退出登录');

          try {
            // 先调用后端退出登录接口
            showToast({
              title: '正在退出登录...',
              icon: 'loading',
              duration: 2000,
            });

            // 调用退出登录接口
            // const response = await request({
            await request({
              path: '/uc/api/auth/logout',
              method: 'GET',
              isloading: false, // 我们自己管理loading状态
            });

            // console.log('退出登录接口响应:', response);

            // 无论接口是否成功，都执行本地退出登录逻辑
            // 调用退出登录方法
            logout();
          } catch (error) {
            // console.error('退出登录接口调用失败:', error);
            // 即使接口调用失败，也执行本地退出登录逻辑
            logout();
          }
        }
      },
    });
  };

  return (
    <View className="profile-page">
      {/* 顶部导航栏 */}
      <NavBar title="个人信息" showBack />

      {/* 内容区域 */}
      <View className="profile-body">
        {loading ? (
          <View className="loading-container">
            <Text className="loading-text">加载中...</Text>
          </View>
        ) : (
          /* 用户信息区域 */
          <View className="user-info-section">
            <View className="avatar-container">
              {userInfo.avatar ? (
                <Image
                  className="avatar"
                  src={userInfo.avatar}
                  mode="aspectFill"
                />
              ) : (
                <View className="avatar-placeholder">
                  <Text className="avatar-placeholder-text">
                    {userInfo.name ? userInfo.name.charAt(0) : ''}
                  </Text>
                </View>
              )}
            </View>
            <Text className="user-name">{userInfo.name || ''}</Text>
            <View className="employee-id-tag">
              <Text className="id-text">No. {userInfo.jobNumber || ''}</Text>
            </View>
          </View>
        )}

        {/* 按钮组 */}
        <View className="button-group">
          <View className="change-password-btn" onClick={handleChangePassword}>
            <Text className="btn-text">修改密码</Text>
          </View>
          <View className="logout-btn" onClick={handleLogout}>
            <Text className="btn-text">退出登录</Text>
          </View>
        </View>
      </View>

      {/* 底部安全区域 */}
      <View className="safe-area-bottom" />
    </View>
  );
}
