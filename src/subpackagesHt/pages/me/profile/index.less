/* 个人信息页面样式 */
.profile-page {
  min-height: 100vh;
  background-color: #f0f4fa;
  display: flex;
  flex-direction: column;
  position: relative;
}

/* 页面背景渐变 */
.profile-page::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 240px;
  background: linear-gradient(
    to bottom,
    #cfe1ff 0%,
    #e3eeff 54.08%,
    #f0f4fa 100%
  );
  z-index: -1;
}

/* 内容区域 */
.profile-body {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 40px;
  padding: 16px;
  width: 100%;
  box-sizing: border-box;
}

/* 加载状态 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  width: 100%;
}

.loading-text {
  font-family: 'MiSans', sans-serif;
  font-size: 16px;
  color: #9aa2ca;
  line-height: 1.5em;
}

/* 用户信息区域 */
.user-info-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

/* 头像容器 */
.avatar-container {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  border: 3.33px solid #ffffff;
  overflow: hidden;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

.avatar {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 头像占位符 */
.avatar-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background: linear-gradient(135deg, #0052d9, #327cf7);
  border-radius: 50%;
}

.avatar-placeholder-text {
  font-family: 'MiSans', sans-serif;
  font-size: 40px;
  font-weight: 600;
  color: #ffffff;
  line-height: 1;
}

/* 用户名 */
.user-name {
  font-family: 'Source Han Sans CN', sans-serif;
  font-size: 24px;
  font-weight: 700;
  color: transparent;
  background: linear-gradient(to right, #2c43a0, #0a0649 16%, #2360f8);
  -webkit-background-clip: text;
  background-clip: text;
  line-height: 1.5em;
}

/* 员工ID标签 */
.employee-id-tag {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 4px 8px;
  background-color: #327cf7;
  border: 1px solid #89b5ff;
  border-radius: 6px;
  box-shadow: inset 0px 0px 8px rgba(255, 255, 255, 1);
}

.id-text {
  font-family: 'Source Han Sans CN', sans-serif;
  font-size: 12px;
  font-weight: 500;
  color: #ffffff;
  line-height: 1.5em;
}

/* 按钮组 */
.button-group {
  display: flex;
  flex-direction: column;
  gap: 16px;
  width: 100%;
}

/* 修改密码按钮 */
.change-password-btn {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 12px 16px;
  background-color: #ffffff;
  border: 1px solid #ffffff;
  border-radius: 12px;
}

/* 退出登录按钮 */
.logout-btn {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 12px 16px;
  background-color: rgba(255, 255, 255, 0.4);
  border-radius: 12px;
}

.btn-text {
  font-family: 'MiSans', sans-serif;
  font-size: 16px;
  font-weight: 500;
  color: #4f5170;
  line-height: 1.5em;
}

/* 底部安全区域 */
.safe-area-bottom {
  height: env(safe-area-inset-bottom);
  width: 100%;
  background-color: #f0f4fa;
}
