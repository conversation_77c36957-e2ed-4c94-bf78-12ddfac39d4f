/* 修改密码页面样式 */
.change-password-page {
  min-height: 100vh;
  background-color: #F0F4FA;
  display: flex;
  flex-direction: column;
  position: relative;
}

/* 页面背景渐变 */
.change-password-page::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 240px;
  background: linear-gradient(
    to bottom,
    #CFE1FF 0%,
    #E3EEFF 54.08%,
    #F0F4FA 100%
  );
  z-index: -1;
}

/* 内容区域 */
.password-body {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 16px;
  width: 100%;
  box-sizing: border-box;
}

/* 表单容器 */
.form-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
  background-color: #FFFFFF;
  border-radius: 12px;
  padding: 16px;
  width: 100%;
  box-sizing: border-box;
}

/* 表单项 */
.form-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
  width: 100%;
}

/* 标签容器 */
.label-container {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 2px;
}

/* 必填标记 */
.required-mark {
  font-family: 'MiSans', sans-serif;
  font-size: 14px;
  font-weight: 400;
  color: #F3564B;
  line-height: 1.57em;
}

/* 标签文本 */
.label-text {
  font-family: 'MiSans', sans-serif;
  font-size: 14px;
  font-weight: 400;
  color: #4F5170;
  line-height: 1.57em;
}

/* 输入框容器 */
.input-container {
  display: flex;
  flex-direction: row;
  align-items: center;
  background-color: #F2F4FA;
  border-radius: 8px;
  padding: 6px 8px;
  width: 100%;
  box-sizing: border-box;
}

/* 密码输入框 */
.password-input {
  flex: 1;
  font-family: 'MiSans', sans-serif;
  font-size: 14px;
  font-weight: 400;
  color: #4F5170;
  line-height: 1.57em;
}

/* 占位符样式 */
.placeholder {
  color: #9AA2CA;
}

/* 保存按钮 */
.save-button {
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #0052D9;
  border-radius: 12px;
  padding: 12px 32px;
  width: 100%;
  box-sizing: border-box;
  margin-top: 16px;
}

/* 保存按钮文本 */
.save-text {
  font-family: 'MiSans', sans-serif;
  font-size: 16px;
  font-weight: 500;
  color: #FFFFFF;
  line-height: 1.5em;
}

/* 取消按钮 */
.cancel-button {
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #FFFFFF;
  border-radius: 12px;
  padding: 12px 16px;
  width: 100%;
  box-sizing: border-box;
}

/* 取消按钮文本 */
.cancel-text {
  font-family: 'MiSans', sans-serif;
  font-size: 16px;
  font-weight: 500;
  color: #4F5170;
  line-height: 1.5em;
}

/* 底部安全区域 */
.safe-area-bottom {
  height: env(safe-area-inset-bottom);
  width: 100%;
  background-color: #F0F4FA;
}
