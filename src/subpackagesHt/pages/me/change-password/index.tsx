import { View, Text, Input } from '@tarojs/components';
import { useLoad, showToast, navigateBack } from '@tarojs/taro';
import { useState } from 'react';
import NavBar from '@ht/components/NavBar';
import request from '@ht/api/apiConfig';
import './index.less';

export default function ChangePassword() {
  // 密码状态
  const [oldPassword, setOldPassword] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');

  useLoad(() => {
    // console.log('修改密码页面加载');
  });

  // 处理保存按钮点击
  const handleSave = async () => {
    // 表单验证
    if (!oldPassword) {
      showToast({
        title: '请输入原密码',
        icon: 'none',
      });
      return;
    }

    if (!newPassword) {
      showToast({
        title: '请输入新密码',
        icon: 'none',
      });
      return;
    }

    if (!confirmPassword) {
      showToast({
        title: '请输入确认密码',
        icon: 'none',
      });
      return;
    }

    if (newPassword !== confirmPassword) {
      showToast({
        title: '两次输入的新密码不一致',
        icon: 'none',
      });
      return;
    }

    // 调用修改密码API
    const res = await request({
      path: '/uc/api/admin/ss/user/modifyPwd',
      method: 'POST',
      body: {
        oldPassword,
        newPassword,
        // userId 参数可选，不传则默认修改当前登录用户的密码
      },
      isloading: true, // 显示加载中提示
    });

    // 处理响应
    if (res.code === 200) {
      showToast({
        title: '密码修改成功',
        icon: 'success',
        duration: 2000,
        success: () => {
          // 延迟返回上一页
          setTimeout(() => {
            navigateBack();
          }, 2000);
        },
      });
    } else {
      showToast({
        title: res.message || '密码修改失败',
        icon: 'none',
        duration: 2000,
      });
    }
  };

  // 处理取消按钮点击
  const handleCancel = () => {
    navigateBack();
  };

  return (
    <View className="change-password-page">
      {/* 顶部导航栏 */}
      <NavBar title="修改密码" showBack />

      {/* 内容区域 */}
      <View className="password-body">
        {/* 表单区域 */}
        <View className="form-container">
          {/* 原密码 */}
          <View className="form-item">
            <View className="label-container">
              <Text className="required-mark">*</Text>
              <Text className="label-text">原密码</Text>
            </View>
            <View className="input-container">
              <Input
                className="password-input"
                password
                placeholder="请输入"
                placeholderClass="placeholder"
                value={oldPassword}
                onInput={e => setOldPassword(e.detail.value)}
              />
            </View>
          </View>

          {/* 新密码 */}
          <View className="form-item">
            <View className="label-container">
              <Text className="required-mark">*</Text>
              <Text className="label-text">新密码</Text>
            </View>
            <View className="input-container">
              <Input
                className="password-input"
                password
                placeholder="请输入"
                placeholderClass="placeholder"
                value={newPassword}
                onInput={e => setNewPassword(e.detail.value)}
              />
            </View>
          </View>

          {/* 重复新密码 */}
          <View className="form-item">
            <View className="label-container">
              <Text className="required-mark">*</Text>
              <Text className="label-text">重复新密码</Text>
            </View>
            <View className="input-container">
              <Input
                className="password-input"
                password
                placeholder="请输入"
                placeholderClass="placeholder"
                value={confirmPassword}
                onInput={e => setConfirmPassword(e.detail.value)}
              />
            </View>
          </View>
        </View>

        {/* 保存按钮 */}
        <View className="save-button" onClick={handleSave}>
          <Text className="save-text">保存</Text>
        </View>

        {/* 取消按钮 */}
        <View className="cancel-button" onClick={handleCancel}>
          <Text className="cancel-text">取消</Text>
        </View>
      </View>

      {/* 底部安全区域 */}
      <View className="safe-area-bottom" />
    </View>
  );
}
