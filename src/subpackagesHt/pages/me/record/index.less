.record-page {
  background-color: #f0f4fa;
  min-height: 100vh;
  display: flex;
  flex-direction: column;

  .record-list {
    flex: 1;
    padding: 16px;
    display: flex;
    flex-direction: column;
    box-sizing: border-box;

    .record-item {
      background-color: #ffffff;
      border-radius: 12px;
      padding: 16px;
      display: flex;
      flex-direction: column;
      gap: 16px;
      box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.05);
      overflow: hidden;
      margin-bottom: 16px; /* 添加底部间距，确保记录项之间有足够的间隔 */

      &:last-child {
        margin-bottom: 0; /* 最后一个记录项不需要底部间距 */
      }

      &.relative-record {
        padding: 0;

        .user-info-area {
          background-color: #0052d9;
          padding: 16px;

          .user-info {
            display: flex;
            flex-direction: column;
            gap: 4px;

            .user-name {
              font-family: 'MiSans', sans-serif;
              font-weight: 500;
              font-size: 16px;
              line-height: 1.5;
              color: #ffffff;
            }

            .user-phone {
              font-family: 'MiSans', sans-serif;
              font-weight: 400;
              font-size: 14px;
              line-height: 1.57;
              color: rgba(255, 255, 255, 0.8);
            }
          }
        }

        .record-info {
          padding: 16px;
        }
      }

      .record-info {
        display: flex;
        flex-direction: column;
        gap: 16px;

        .section-title {
          font-family: 'MiSans', sans-serif;
          font-weight: 500;
          font-size: 16px;
          line-height: 1.5;
          color: #1d1f20;
          margin-bottom: -8px;
        }

        .info-row {
          display: flex;
          justify-content: space-between;
          align-items: center;

          .label {
            font-family: 'MiSans', sans-serif;
            font-weight: 400;
            font-size: 14px;
            line-height: 1.57;
            color: #4f5170;
            width: 90px;
          }

          .value {
            font-family: 'MiSans', sans-serif;
            font-weight: 500;
            font-size: 14px;
            line-height: 1.57;
            color: #1d1f20;
            flex: 1;
            text-align: right;
          }

          .product-card {
            display: flex;
            align-items: center;
            gap: 4px;

            .product-tag {
              background-color: #a4edfd;
              border: 1px solid #65a7d6;
              border-radius: 0 6px 0 6px;
              padding: 2px 4px;
              box-shadow: inset 0px 0px 12px rgba(255, 255, 255, 0.8);

              .tag-text {
                font-family: 'MiSans', sans-serif;
                font-weight: 500;
                font-size: 12px;
                line-height: 1.67;
                color: #206597;
              }
            }

            .product-name {
              font-family: 'MiSans', sans-serif;
              font-weight: 500;
              font-size: 14px;
              line-height: 1.57;
              color: #333333;
            }
          }
        }
      }
    }

    .empty-state,
    .loading-state {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 32px 16px;

      .empty-image {
        width: 120px;
        height: 120px;
        margin-bottom: 16px;
      }

      .empty-text,
      .loading-text {
        font-family: 'MiSans', sans-serif;
        font-weight: 400;
        font-size: 14px;
        line-height: 1.57;
        color: #9aa2ca;
        text-align: center;
      }
    }
  }

  .tab-container {
    display: flex;
    background-color: #ffffff;
    padding: 4px 16px 8px;

    .tab {
      flex: 1;
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 8px;
      font-family: 'MiSans', sans-serif;
      font-weight: 400;
      font-size: 14px;
      line-height: 1.57;
      color: #4c6787;
      position: relative;

      &.active {
        color: #0052d9;
        font-weight: 500;

        &::after {
          content: '';
          position: absolute;
          bottom: 0;
          left: 50%;
          transform: translateX(-50%);
          width: 16px;
          height: 2px;
          background-color: #0052d9;
          border-radius: 1px;
        }
      }
    }
  }
}
