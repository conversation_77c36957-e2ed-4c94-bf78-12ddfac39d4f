import { View, Text, ScrollView } from '@tarojs/components';
import { useState } from 'react';
import { useLoad, showToast } from '@tarojs/taro';
import dayjs from 'dayjs';
import NavBar from '@ht/components/NavBar';
import request from '@ht/api/apiConfig';
// import { AES } from '@ht/utils/AES';
import { AES } from '@/utils/tools';
import './index.less';

// 接口返回的数据类型
interface ProductUseInfoVo {
  boardGate?: string; // 登机口
  codeNo: string; // 券码
  fltDate?: string; // 航班日期
  fltNo?: string; // 航班号
  passengerName?: string; // 旅客姓名
  phone?: string; // 手机号
  productName: string; // 产品名称
  seatNo?: string; // 座位号
  userNo?: string; // 工号
  verifyTime?: string; // 核销时间
  verifyUserName?: string; // 核销人姓名
  verifyUserNo?: string; // 核销人工号
}

// 标签页类型
type TabType = 'self' | 'relative';

// 定义记录类型
interface RecordItem {
  id: string;
  productName: string;
  productTag: string;
  type: TabType;
  status: 'used' | 'unused';
  userName: string;
  userCode: string;
  userPhone?: string; // 亲友电话号码
  verifierName: string;
  verifierCode: string;
  verifyTime: string;
  couponCode: string;
  // 航班信息
  flightNo?: string;
  flightDate?: string;
  gate?: string;
  seat?: string;
}

// 将接口返回的数据转换为 RecordItem 格式
const convertToRecordItem = (
  data: ProductUseInfoVo,
  type: TabType,
): RecordItem => {
  return {
    id: data.codeNo, // 使用券码作为唯一标识
    productName: data.productName,
    productTag: data.productName.includes('次卡') ? '员工次卡' : '旅客高舱',
    type,
    status: data.verifyTime ? 'used' : 'unused',
    userName: data.passengerName || '',
    userCode: data.userNo || '',
    userPhone: data.phone ? AES.decrypt(data.phone) : '',
    verifierName: data.verifyUserName || '',
    verifierCode: data.verifyUserNo || '',
    verifyTime: data.verifyTime
      ? dayjs(data.verifyTime).format('YYYY-MM-DD HH:mm:ss')
      : '',
    couponCode: data.codeNo,
    flightNo: data.fltNo,
    flightDate: data.fltDate,
    gate: data.boardGate,
    seat: data.seatNo,
  };
};

export default function RecordPage() {
  // 当前选中的标签页
  const [activeTab, setActiveTab] = useState<TabType>('self');
  // 记录列表
  const [records, setRecords] = useState<RecordItem[]>([]);
  // 加载状态
  const [loading, setLoading] = useState<boolean>(false);

  // 获取本人使用记录
  const fetchSelfRecords = async () => {
    try {
      setLoading(true);
      const response = await request({
        path: '/api/high-tank/app/user/queryProductUserInfo',
        method: 'GET',
      });

      if (response && response.code === 200 && response.data) {
        // 转换数据格式
        const recordItems = response.data.map((item: ProductUseInfoVo) =>
          convertToRecordItem(item, 'self'),
        );
        setRecords(recordItems);
      } else {
        showToast({
          title: response?.message || '获取记录失败',
          icon: 'none',
        });
        setRecords([]);
      }
    } catch (error) {
      // console.error('获取本人使用记录失败:', error);
      showToast({
        title: '获取记录失败',
        icon: 'none',
      });
      setRecords([]);
    } finally {
      setLoading(false);
    }
  };

  // 获取亲友使用记录
  const fetchRelativeRecords = async () => {
    try {
      setLoading(true);
      const response = await request({
        path: '/api/high-tank/app/user/queryProductUserInfoKin',
        method: 'GET',
      });

      if (response && response.code === 200 && response.data) {
        // 转换数据格式
        const recordItems = response.data.map((item: ProductUseInfoVo) =>
          convertToRecordItem(item, 'relative'),
        );
        setRecords(recordItems);
      } else {
        showToast({
          title: response?.message || '获取记录失败',
          icon: 'none',
        });
        setRecords([]);
      }
    } catch (error) {
      // console.error('获取亲友使用记录失败:', error);
      showToast({
        title: '获取记录失败',
        icon: 'none',
      });
      setRecords([]);
    } finally {
      setLoading(false);
    }
  };

  // 处理标签页切换
  const handleTabChange = (tab: TabType) => {
    setActiveTab(tab);
    // 根据标签页类型获取不同的记录
    if (tab === 'self') {
      fetchSelfRecords();
    } else {
      fetchRelativeRecords();
    }
  };

  // 页面加载时获取记录
  useLoad(() => {
    fetchSelfRecords();
  });

  // 渲染记录项
  const renderRecordItem = (record: RecordItem) => {
    // 根据记录类型渲染不同的内容
    if (record.type === 'relative') {
      return renderRelativeRecordItem(record);
    } else {
      return renderSelfRecordItem(record);
    }
  };

  // 渲染本人记录项
  const renderSelfRecordItem = (record: RecordItem) => {
    return (
      <View className="record-item" key={record.id}>
        <View className="record-info">
          <View className="info-row">
            <Text className="label">使用人姓名</Text>
            <Text className="value">{record.userName}</Text>
          </View>

          <View className="info-row">
            <Text className="label">使用人工号</Text>
            <Text className="value">{record.userCode}</Text>
          </View>

          {record.status === 'used' && (
            <>
              <View className="info-row">
                <Text className="label">核销人姓名</Text>
                <Text className="value">{record.verifierName}</Text>
              </View>

              <View className="info-row">
                <Text className="label">核销人工号</Text>
                <Text className="value">{record.verifierCode}</Text>
              </View>

              <View className="info-row">
                <Text className="label">核销时间</Text>
                <Text className="value">{record.verifyTime}</Text>
              </View>
            </>
          )}

          <View className="info-row">
            <Text className="label">使用券号</Text>
            <Text className="value">{record.couponCode}</Text>
          </View>

          <View className="info-row">
            <Text className="label">产品名称</Text>
            <View className="product-card">
              <View className="product-tag">
                <Text className="tag-text">{record.productTag}</Text>
              </View>
              <Text className="product-name">{record.productName}</Text>
            </View>
          </View>
        </View>
      </View>
    );
  };

  // 渲染亲友记录项
  const renderRelativeRecordItem = (record: RecordItem) => {
    return (
      <View className="record-item relative-record" key={record.id}>
        {/* 亲友信息区域 */}
        <View className="user-info-area">
          <View className="user-info">
            <Text className="user-name">{record.userName}</Text>
            <Text className="user-phone">{record.userPhone}</Text>
          </View>
        </View>

        <View className="record-info">
          {/* 航班信息 */}
          <Text className="section-title">航班信息</Text>

          <View className="info-row">
            <Text className="label">航班号</Text>
            <Text className="value">{record.flightNo}</Text>
          </View>

          <View className="info-row">
            <Text className="label">航班日期</Text>
            <Text className="value">{record.flightDate}</Text>
          </View>

          <View className="info-row">
            <Text className="label">登机口</Text>
            <Text className="value">{record.gate}</Text>
          </View>

          <View className="info-row">
            <Text className="label">座位号</Text>
            <Text className="value">{record.seat}</Text>
          </View>

          {/* 使用信息 */}
          <Text className="section-title">使用信息</Text>

          {record.status === 'used' && (
            <>
              <View className="info-row">
                <Text className="label">核销人姓名</Text>
                <Text className="value">{record.verifierName}</Text>
              </View>

              <View className="info-row">
                <Text className="label">核销人工号</Text>
                <Text className="value">{record.verifierCode}</Text>
              </View>

              <View className="info-row">
                <Text className="label">核销时间</Text>
                <Text className="value">{record.verifyTime}</Text>
              </View>
            </>
          )}

          <View className="info-row">
            <Text className="label">使用券号</Text>
            <Text className="value">{record.couponCode}</Text>
          </View>

          <View className="info-row">
            <Text className="label">产品名称</Text>
            <View className="product-card">
              <View className="product-tag">
                <Text className="tag-text">{record.productTag}</Text>
              </View>
              <Text className="product-name">{record.productName}</Text>
            </View>
          </View>
        </View>
      </View>
    );
  };

  // 渲染空状态
  const renderEmptyState = () => {
    return (
      <View className="empty-state">
        <Text className="empty-text">暂无使用记录</Text>
      </View>
    );
  };

  return (
    <View className="record-page">
      {/* 导航栏 */}
      <NavBar title="产品使用记录" showBack />

      {/* 标签页 */}
      <View className="tab-container">
        <View
          className={`tab ${activeTab === 'self' ? 'active' : ''}`}
          onClick={() => handleTabChange('self')}
        >
          <Text>本人</Text>
        </View>
        <View
          className={`tab ${activeTab === 'relative' ? 'active' : ''}`}
          onClick={() => handleTabChange('relative')}
        >
          <Text>亲友</Text>
        </View>
      </View>

      {/* 记录列表 */}
      <ScrollView className="record-list" scrollY enableBackToTop>
        {loading ? (
          <View className="loading-state">
            <Text className="loading-text">加载中...</Text>
          </View>
        ) : records.length > 0 ? (
          records.map(record => renderRecordItem(record))
        ) : (
          renderEmptyState()
        )}
      </ScrollView>
    </View>
  );
}
