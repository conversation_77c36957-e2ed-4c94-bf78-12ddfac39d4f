import { View, Text, Image, Button, Input, Picker } from '@tarojs/components';
import {
  useLoad,
  useRouter,
  showToast,
  navigateTo,
  scanCode,
} from '@tarojs/taro';
import { useState } from 'react';
import dayjs from 'dayjs';
import NavBar from '@ht/components/NavBar';
import { getScanResults } from '@ht/utils/scanParsing';
import { getImageUrl } from '@/subpackagesHt/utils/image';
import './index.less';

/**
 * 旅客信息填写页面
 */
export default function PassengerInfo() {
  const router = useRouter();
  const { productId, productType = '2' } = router.params;

  // 旅客信息
  const [passengerName, setPassengerName] = useState('');
  const [passengerPhone, setPassengerPhone] = useState('');

  // 航班信息
  const [flightNumber, setFlightNumber] = useState('');
  const [flightDate, setFlightDate] = useState('');
  const [gateNumber, setGateNumber] = useState('');
  const [seatNumber, setSeatNumber] = useState('');

  // 日期选择器相关状态已移除，使用Taro原生Picker

  useLoad(() => {
    // console.log('旅客信息填写页面加载，产品ID:', productId);
  });

  // 获取当前日期，格式为YYYY-MM-DD
  const getCurrentDate = () => {
    return dayjs().format('YYYY-MM-DD');
  };

  // 获取结束日期（当前日期后一年）
  const getEndDate = () => {
    return dayjs().add(1, 'year').format('YYYY-MM-DD');
  };

  // 日期选择相关函数已移除，使用Taro原生Picker

  // 定义旅客信息类型
  interface BoardingInfo {
    name?: string;
    phone?: string;
    flightNumber?: string;
    flightDate?: string;
    gateNumber?: string;
    seatNumber?: string;
    [key: string]: any; // 允许其他属性
  }

  // 设置所有旅客信息的辅助函数
  const setPassengerInfo = (info: BoardingInfo | null) => {
    if (!info) return;

    const {
      name = '',
      phone = '',
      flightNumber: flight = '',
      flightDate: date = '',
      gateNumber: gate = '',
      seatNumber: seat = '',
    } = info;

    setPassengerName(name);
    setPassengerPhone(phone);
    setFlightNumber(flight);
    setFlightDate(date);
    setGateNumber(gate);
    setSeatNumber(seat);
  };

  // 验证扫码结果是否包含必要信息
  const isValidBoardingInfo = (info: BoardingInfo | null): boolean => {
    return !!(info && info.name && info.flightNumber && info.flightDate);
  };

  // 将扫码结果转换为旅客信息
  const convertScanResultToBoardingInfo = (
    scanResult: any,
  ): BoardingInfo | null => {
    if (!scanResult) {
      return null;
    }

    return {
      name: scanResult.paxName || '',
      phone: '', // 扫码结果中通常没有手机号，需要用户手动输入
      flightNumber: scanResult.flightNo || '',
      flightDate: scanResult.flightDate || '',
      gateNumber: '', //
      seatNumber: scanResult.seatNo || '', //
    };
  };

  // 扫码录入旅客信息
  const handleScanCode = () => {
    // 调用扫码API
    scanCode({
      onlyFromCamera: false, // 允许从相册选择二维码
      scanType: ['qrCode', 'barCode'], // 扫码类型，支持二维码和条形码
      success: res => {
        try {
          // 获取扫码结果
          const scanResult = res.result;
          // console.log('扫码结果:', scanResult);

          // 使用工具函数解析扫码结果
          const scanResultInfo = getScanResults(scanResult);

          // 转换为旅客信息格式
          const boardingInfo = convertScanResultToBoardingInfo(scanResultInfo);

          // 验证旅客信息
          if (isValidBoardingInfo(boardingInfo)) {
            // 设置旅客信息
            setPassengerInfo(boardingInfo);
          } else {
            // console.warn('扫码结果缺少必要信息:', scanResultInfo);

            showToast({
              title: '登机牌信息不完整，请手动填写',
              icon: 'none',
            });
          }
        } catch (error) {
          // console.error('处理扫码结果失败:', error);

          showToast({
            title: '解析登机牌信息失败，请手动填写',
            icon: 'none',
          });
        }
      },
      // fail: error => {
      fail: () => {
        // console.error('扫码失败:', error);
        showToast({
          title: '扫码失败',
          icon: 'none',
        });
      },
    });
  };

  // 验证手机号码格式
  const isValidPhoneNumber = (phone: string): boolean => {
    // 中国大陆手机号码格式：1开头的11位数字
    const phoneRegex = /^1[3-9]\d{9}$/;
    return phoneRegex.test(phone);
  };

  // 验证航班号格式（最长6位）
  const isValidFlightNumber = (flight: string): boolean => {
    return flight.length <= 6;
  };

  // 验证登机口格式（最长6位）
  const isValidGateNumber = (gate: string): boolean => {
    return gate.length <= 6;
  };

  // 验证座位号格式（最长5位）
  const isValidSeatNumber = (seat: string): boolean => {
    return seat.length <= 5;
  };

  // 验证旅客姓名格式（不允许输入数字，支持英文、汉字与分隔符[.]）
  const isValidPassengerName = (name: string): boolean => {
    // 匹配英文字母、汉字和点号，不允许数字
    const nameRegex = /^[a-zA-Z\u4e00-\u9fa5.]+$/;
    return nameRegex.test(name);
  };

  // 下一步按钮点击事件
  const handleNextStep = () => {
    // 表单验证
    if (!passengerName) {
      showToast({
        title: '请输入旅客姓名',
        icon: 'none',
      });
      return;
    }
    // 验证旅客姓名格式（不允许输入数字，支持英文、汉字与分隔符[.]）
    if (!isValidPassengerName(passengerName)) {
      showToast({
        title: '旅客姓名不允许输入数字，仅支持英文、汉字与分隔符[.]',
        icon: 'none',
      });
      return;
    }
    if (!passengerPhone) {
      showToast({
        title: '请输入手机号码',
        icon: 'none',
      });
      return;
    }
    // 验证手机号码格式
    if (!isValidPhoneNumber(passengerPhone)) {
      showToast({
        title: '请输入正确的手机号码',
        icon: 'none',
      });
      return;
    }
    if (!flightNumber) {
      showToast({
        title: '请输入航班号',
        icon: 'none',
      });
      return;
    }
    // 验证航班号长度（最长6位）
    if (!isValidFlightNumber(flightNumber)) {
      showToast({
        title: '航班号长度不能超过6位',
        icon: 'none',
      });
      return;
    }
    if (!flightDate) {
      showToast({
        title: '请选择航班日期',
        icon: 'none',
      });
      return;
    }
    // 验证登机口长度（如果有输入）
    if (gateNumber && !isValidGateNumber(gateNumber)) {
      showToast({
        title: '登机口长度不能超过6位',
        icon: 'none',
      });
      return;
    }
    // 验证座位号长度（如果有输入）
    if (seatNumber && !isValidSeatNumber(seatNumber)) {
      showToast({
        title: '座位号长度不能超过5位',
        icon: 'none',
      });
      return;
    }

    // 跳转到信息确认页面
    navigateTo({
      url: `/subpackagesHt/pages/product/employee-card-list/confirm/index?productId=${productId}&productType=${productType}&passengerName=${passengerName}&passengerPhone=${passengerPhone}&flightNumber=${flightNumber}&flightDate=${flightDate}&gateNumber=${gateNumber}&seatNumber=${seatNumber}`,
    });
  };

  // 判断表单是否填写完成
  const isFormComplete =
    passengerName && passengerPhone && flightNumber && flightDate;

  return (
    <View className="passenger-info">
      {/* 顶部导航栏 - 使用透明背景 */}
      <NavBar title="旅客信息填写" transparent />

      {/* 内容区域 */}
      <View className="passenger-body">
        {/* 航班信息卡片 */}
        <View className="info-card">
          {/* 卡片头部 */}
          <View className="card-header-row">
            <Text className="card-title">航班信息</Text>
            {/* 扫码录入按钮 */}
            <View className="scan-button" onClick={handleScanCode}>
              <Image
                className="scan-icon"
                src={getImageUrl('qr_scan_icon.svg')}
                mode="aspectFit"
              />
              <Text className="scan-text">扫登机牌快速录入</Text>
            </View>
          </View>

          {/* 航班信息表单 */}
          <View className="form-group">
            <View className="form-item">
              <Text className="form-label">航班号</Text>
              <Input
                className="form-input"
                placeholder="请输入"
                value={flightNumber}
                maxlength={6}
                onInput={e => setFlightNumber(e.detail.value)}
              />
            </View>
            <View className="form-item">
              <Text className="form-label">航班日期</Text>
              <View className="date-input-container">
                <Picker
                  mode="date"
                  value={flightDate || getCurrentDate()}
                  onChange={e => setFlightDate(e.detail.value)}
                  start={getCurrentDate()}
                  end={getEndDate()}
                >
                  <View className="picker-view">
                    <Input
                      className="form-input date-input"
                      placeholder="请选择"
                      value={flightDate}
                      disabled
                    />
                    <Image
                      className="calendar-icon"
                      src={getImageUrl('calendar.svg')}
                      mode="aspectFit"
                    />
                  </View>
                </Picker>
              </View>
            </View>
            <View className="form-item">
              <Text className="form-label">登机口</Text>
              <Input
                className="form-input"
                placeholder="请输入"
                value={gateNumber}
                maxlength={6}
                onInput={e => setGateNumber(e.detail.value)}
              />
            </View>
            <View className="form-item">
              <Text className="form-label">座位号</Text>
              <Input
                className="form-input"
                placeholder="请输入"
                value={seatNumber}
                maxlength={5}
                onInput={e => setSeatNumber(e.detail.value)}
              />
            </View>
          </View>
        </View>

        {/* 旅客信息卡片 */}
        <View className="info-card">
          <View className="card-header">
            <Text className="card-title">旅客信息</Text>
          </View>
          <View className="form-group">
            <View className="form-item">
              <Text className="form-label">旅客姓名</Text>
              <Input
                className="form-input"
                placeholder="请输入"
                value={passengerName}
                onInput={e => setPassengerName(e.detail.value)}
              />
            </View>
            <View className="form-item">
              <Text className="form-label">手机号码</Text>
              <Input
                className="form-input"
                placeholder="请输入"
                type="number"
                maxlength={11}
                value={passengerPhone}
                onInput={e => setPassengerPhone(e.detail.value)}
              />
            </View>
          </View>
        </View>
      </View>

      <View className="passenger-footer">
        <Button
          className={`next-button ${isFormComplete ? 'active' : ''}`}
          onClick={handleNextStep}
          disabled={!isFormComplete}
        >
          下一步
        </Button>
      </View>
    </View>
  );
}
