/* 旅客信息填写页面样式 */
.passenger-info {
  min-height: 100vh;
  background-color: #f0f4fa;
  display: flex;
  flex-direction: column;
  position: relative;
}

/* 页面背景渐变 */
.passenger-info::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 240px;
  background: linear-gradient(
    to bottom,
    #cfe1ff 0%,
    #e3eeff 54.08%,
    #f0f4fa 100%
  );
  z-index: -1;
}

/* 内容区域 */
.passenger-body {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 16px;
  padding-bottom: 80px; /* 为底部按钮留出空间 */
  position: relative;
  z-index: 1;
  width: 100%;
  box-sizing: border-box;
}

/* 信息卡片 */
.info-card {
  background-color: #ffffff;
  border-radius: 12px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  width: 100%;
  margin-bottom: 12px;
  border: 1px solid #ffffff;
}

/* 卡片头部行 */
.card-header-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #f0f4fa;
}

.card-header {
  padding: 16px;
  border-bottom: 1px solid #f0f4fa;
}

.card-title {
  font-size: 16px;
  font-weight: 500;
  color: #1d1f20;
  font-family: 'MiSans', sans-serif;
  line-height: 1.5em;
}

/* 扫码按钮 - 精确匹配 Figma 设计 */
.scan-button {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f3f5ff;
  padding: 4px 8px;
  border-radius: 8px;
  cursor: pointer;
}

.scan-icon {
  width: 16px;
  height: 16px;
  margin-right: 4px;
  flex-shrink: 0;
}

.scan-text {
  font-size: 12px;
  font-weight: 400;
  color: #0052d9;
  font-family: 'MiSans', sans-serif;
  line-height: 1.67em;
}

/* 日期选择器样式 */
.picker-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
}

.picker-container {
  background-color: #ffffff;
  border-top-left-radius: 12px;
  border-top-right-radius: 12px;
  overflow: hidden;
}

.picker-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #f0f4fa;
}

.picker-title {
  font-size: 16px;
  font-weight: 500;
  color: #1d1f20;
  font-family: 'MiSans', sans-serif;
}

.picker-cancel {
  font-size: 14px;
  color: #737578;
  font-family: 'MiSans', sans-serif;
}

.picker-confirm {
  font-size: 14px;
  color: #1663f8;
  font-weight: 500;
  font-family: 'MiSans', sans-serif;
}

/* 表单区域 - 精确匹配 Figma 设计 */
.form-group {
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 16px;
  width: 100%;
  box-sizing: border-box;
}

.form-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-label {
  font-size: 14px;
  font-weight: 400;
  color: #4f5170;
  font-family: 'MiSans', sans-serif;
  line-height: 1.57em;
}

.form-input {
  height: 40px;
  padding: 6px 8px;
  font-size: 14px;
  color: #9aa2ca;
  background-color: #f2f4fa;
  border-radius: 8px;
  border: none;
  box-sizing: border-box;
  width: 100%;
}

.date-input-container {
  position: relative;
  width: 100%;
}

.picker-view {
  position: relative;
  width: 100%;
}

.date-input {
  padding-right: 32px;
}

.calendar-icon {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  width: 24px;
  height: 24px;
}

/* 产品信息 */
.product-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
}

.product-name {
  font-size: 14px;
  font-weight: 400;
  color: #1d1f20;
  font-family: 'MiSans', sans-serif;
  line-height: 1.57em;
}

.product-price {
  font-size: 16px;
  font-weight: 500;
  color: #1d1f20;
  font-family: 'MiSans', sans-serif;
  line-height: 1.5em;
}

/* 底部按钮 - 精确匹配 Figma 设计 */
.passenger-footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #ffffff;
  padding: 8px 16px;
  z-index: 100;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}

.next-button {
  width: 100%;
  height: 44px;
  line-height: 44px;
  background-color: #f4f5f5;
  color: #c4c7ca !important;
  font-size: 16px;
  font-weight: 400;
  text-align: center;
  border: none;
  padding: 0;
  margin: 0;
  font-family: 'MiSans', sans-serif;
  line-height: 1.5em;
  display: flex;
  justify-content: center;
  align-items: center;
}

.next-button.active {
  color: #ffffff !important;
  background-color: #1663f8;
}
