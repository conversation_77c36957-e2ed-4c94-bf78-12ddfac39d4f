import { View, Text } from '@tarojs/components';
import { useLoad, navigateTo, useRouter } from '@tarojs/taro';
import { useState } from 'react';
import request from '@ht/api/apiConfig';
import { ProductType, ProductDisplay, Product } from '../../../types/product';
import CardItem from '../../../components/CardItem';
import NavBar from '../../../components/NavBar';
import './index.less';

/**
 * 员工次卡列表页面
 */
export default function EmployeeCardList() {
  const [products, setProducts] = useState<ProductDisplay[]>([]);
  const [loading, setLoading] = useState(false);
  const router = useRouter();
  const { productType = '1' } = router.params; // 默认为员工次卡类型
  const productTypeNum = Number(productType);

  // 页面加载时获取产品列表
  useLoad(() => {
    fetchProductList(productTypeNum);
  });

  // 将产品数据转换为展示格式
  const convertToDisplayProducts = (
    productList: Product[],
  ): ProductDisplay[] => {
    return productList.map(product => ({
      ...product,
      price: parseFloat(product.price), // 将价格字符串转为数字
      tags: [
        {
          id: '1',
          name: product.productType === 1 ? '员工次卡' : '高舱休息室',
        },
        {
          id: '2',
          name: product.effectType === 1 ? '长期有效' : '当日有效',
        },
        {
          id: '3',
          name: product.times ? `${product.times}次` : '',
        },
        {
          id: '4',
          name: product?.effectTime ? `${product.effectTime}小时/次` : '',
        },
      ],
    }));
  };

  // 获取产品列表数据
  const fetchProductList = async (type: number = ProductType.EmployeeCard) => {
    try {
      setLoading(true);

      // 根据传入的类型调用产品列表接口
      const res = await request<{
        code: number;
        message: string;
        data: Product[];
      }>({
        path: '/api/high-tank/app/product/list',
        method: 'GET',
        query: { productType: type }, // 根据传入的类型获取产品列表
        isloading: true, // 使用内置的加载状态处理
      });

      // console.log(`产品列表接口返回数据(类型${type}):`, res);

      // 由于 request 函数会处理非 2xx 状态码的错误，
      // 这里只需要处理业务逻辑错误（code !== 200）
      if (res.code === 200 && Array.isArray(res.data)) {
        // 将接口返回的数据转换为展示所需的格式
        const displayProducts = convertToDisplayProducts(res.data);
        setProducts(displayProducts);
      } else {
        // 业务逻辑错误，显示错误信息
        // console.error('业务逻辑错误:', res.message);
        setProducts([]);
      }
    } catch (error) {
      // request 函数已经处理了网络错误和非 2xx 状态码
      // 这里只需要设置空数据
      // console.error('接口调用失败:', error);
      setProducts([]);
    } finally {
      setLoading(false);
    }
  };

  // 点击卡片处理函数
  const handleCardClick = (product: ProductDisplay) => {
    // 跳转到产品详情页，传递产品ID
    navigateTo({
      url: `/subpackagesHt/pages/product/employee-card-list/detail/index?id=${product.id}`,
    });
  };

  return (
    <View className="employee-card-list">
      {/* 顶部导航栏 */}
      <NavBar
        title={
          productTypeNum === ProductType.EmployeeCard ? '员工次卡' : '旅客高舱'
        }
        showBack
      />

      <View className="employee-card-list-content">
        {loading ? (
          <View className="loading-state">
            <Text className="loading-text">加载中...</Text>
          </View>
        ) : products.length > 0 ? (
          products.map(product => (
            <CardItem
              key={product.id}
              product={product}
              onClick={handleCardClick}
            />
          ))
        ) : (
          <View className="empty-state">
            <Text className="empty-text">
              {productTypeNum === ProductType.EmployeeCard
                ? '暂无员工次卡产品'
                : '暂无旅客高舱产品'}
            </Text>
            <View
              className="retry-button"
              onClick={() => fetchProductList(productTypeNum)}
            >
              <Text className="retry-text">重新加载</Text>
            </View>
          </View>
        )}
      </View>
    </View>
  );
}
