/* 产品确认页面样式 */
.confirm-page {
  min-height: 100vh;
  background-color: #f0f4fa;
  display: flex;
  flex-direction: column;
  position: relative;
}

/* 页面背景渐变 */
.confirm-page::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 240px;
  background: linear-gradient(
    to bottom,
    #cfe1ff 0%,
    #e3eeff 54.08%,
    #f0f4fa 100%
  );
  z-index: -1;
}

/* 加载状态 */
.loading-state {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
  margin: 16px;
}

.loading-text {
  font-family: 'MiSans', sans-serif;
  font-size: 14px;
  color: #9aa2ca;
}

/* 内容区域 */
.confirm-body {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 16px;
  padding-bottom: 80px; /* 为底部按钮留出空间 */
  width: 100%;
  box-sizing: border-box;
}

/* 信息卡片 */
.info-card {
  background-color: #ffffff;
  border-radius: 8px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  padding: 16px;
  gap: 12px;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #23242d;
  font-family: 'MiSans', sans-serif;
  line-height: 1.33em;
}

/* 产品卡片 */
.product-card {
  display: flex;
  flex-direction: column;
  width: 100%;
  border-radius: 12px;
  border: 1px solid #c3d8ff;
  overflow: hidden;
}

.product-card-content {
  position: relative;
  height: 120px;
  // background: linear-gradient(to right, #98d4ff, #1f91e3);
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.product-tag {
  position: absolute;
  top: 10px;
  left: 10px;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #ffffff;
  border-radius: 50%;
}

.tag-icon {
  width: 24px;
  height: 24px;
}

.product-title {
  font-family: 'YouSheBiaoTiYuan', sans-serif;
  font-size: 20px;
  font-weight: 400;
  color: #ffffff;
  text-shadow: 0px 2px 4px rgba(0, 0, 0, 0.05);
  z-index: 1;
}

.card-v-image {
  position: absolute;
  top: 0;
  right: 0;
  width: 100%;
  height: 100%;
}

.image-loading {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1;
}

.image-loading .loading-text {
  font-size: 12px;
  color: #666666;
}

.product-details {
  padding: 8px 16px;
  background-color: #ffffff;
}

.product-name {
  font-size: 14px;
  font-weight: 500;
  color: #333333;
  line-height: 1.57em;
}

.product-info-row {
  display: flex;
  gap: 12px;
  margin-top: 8px;
}

.product-info-text {
  font-size: 12px;
  font-weight: 400;
  color: #404245;
  line-height: 1.67em;
  opacity: 0.8;
}

/* 购买人信息 */
.buyer-info {
  display: flex;
  flex-direction: column;
  gap: 12px;
  background-color: #f5f8fc;
  border-radius: 12px;
  padding: 12px 16px;
}

/* 航班信息 */
.flight-info {
  display: flex;
  flex-direction: column;
  gap: 12px;
  background-color: #f5f8fc;
  border-radius: 12px;
  padding: 12px 16px;
}

.info-header {
  display: flex;
  align-items: center;
  gap: 8px;
}

.flight-number,
.passenger-name {
  font-size: 16px;
  font-weight: 400;
  color: #191919;
  font-family: 'OPPOSans', sans-serif;
  line-height: 1.5em;
}

.flight-date {
  font-size: 14px;
  font-weight: 400;
  color: #4f5170;
  font-family: 'OPPOSans', sans-serif;
  line-height: 1.57em;
}

.info-details {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 8px;
}

.info-item {
  display: flex;
  justify-content: center;
  gap: 8px;
}

/* 旅客信息 */
.passenger-info {
  display: flex;
  flex-direction: column;
  gap: 12px;
  background-color: #f5f8fc;
  border-radius: 12px;
  padding: 12px 16px;
}

/* 通用信息行 */
.info-row {
  display: flex;
  align-items: center;
  gap: 12px;
}

.info-label {
  font-size: 14px;
  font-weight: 400;
  color: #4f5170;
  width: 70px;
  flex-shrink: 0;
  line-height: 1.57em;
}

.info-value {
  font-size: 14px;
  font-weight: 500;
  color: #1d1f20;
  line-height: 1.57em;
}

/* 支付金额卡片 */
.payment-card {
  background-color: #ffffff;
  border-radius: 12px;
  padding: 8px 12px;
  width: 100%;
  box-sizing: border-box;
}

.payment-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.payment-label {
  font-size: 16px;
  font-weight: 400;
  color: #1d1f20;
  line-height: 1.5em;
}

.price-container {
  display: flex;
  align-items: baseline;
}

.price-symbol {
  font-size: 12px;
  font-weight: 500;
  color: #f84f2a;
  margin-right: 2px;
  line-height: 1.67em;
}

.price-value {
  font-size: 20px;
  font-weight: 500;
  color: #f84f2a;
  line-height: 1.4em;
}

/* 底部支付栏 */
.confirm-footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #ffffff;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px;
  z-index: 100;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}

.footer-price {
  display: flex;
  flex-direction: column;
}

.footer-price-label {
  font-size: 12px;
  font-weight: 400;
  color: #737578;
  line-height: 1.67em;
}

.footer-price-container {
  display: flex;
  align-items: baseline;
}

.footer-price-symbol {
  font-size: 12px;
  font-weight: 500;
  color: #f84f2a;
  margin-right: 2px;
  line-height: 1.67em;
}

.footer-price-value {
  font-size: 20px;
  font-weight: 500;
  color: #f84f2a;
  line-height: 1.4em;
}

.payment-button {
  height: 44px;
  line-height: 44px;
  padding: 0 32px;
  background-color: #f84f2a;
  color: #ffffff;
  font-size: 16px;
  font-weight: 600;
  border-radius: 99px;
  text-align: center;
  border: none;
}
