import { View, Text, Image, Button } from '@tarojs/components';
import { useLoad, useRouter, navigateTo, showToast } from '@tarojs/taro';
import { useState } from 'react';
import request from '@ht/api/apiConfig';
import NavBar from '@ht/components/NavBar';
import { useImageUrl } from '@/subpackagesHt/utils/useImageUrl';
import './index.less';

// 产品信息接口返回类型
interface ProductInfo {
  id: number;
  productName: string;
  price: string;
  productType: number;
  effectType: number;
  effectTime: string;
  times: number;
  status: number;
  discribe: string;
  image: string;
  deleted: number;
}

// 接口返回类型
interface ApiResponse {
  code: number;
  message: string;
  data: ProductInfo;
}

/**
 * 产品确认页面
 */
export default function ConfirmPage() {
  const router = useRouter();
  const {
    productId,
    buyerName,
    buyerPhone,
    buyerJobNumber,
    productType = '1', // 默认为员工次卡类型
    // 旅客高舱特有参数
    passengerName,
    passengerPhone,
    flightNumber,
    flightDate,
    gateNumber,
    seatNumber,
  } = router.params;

  // 转换产品类型为数字
  const productTypeNum = Number(productType);

  // 产品信息状态
  const [loading, setLoading] = useState(false);
  const [product, setProduct] = useState<ProductInfo | null>(null);

  // 使用图片处理Hook
  const { imageUrl: productImageUrl, loading: imageLoading } = useImageUrl(
    product?.image,
    'card_bg.svg',
  );

  useLoad(() => {
    // console.log('产品确认页面加载，产品ID:', productId);
    if (productId) {
      fetchProductInfo(productId);
    }
  });

  // 获取产品信息
  const fetchProductInfo = async (id: string) => {
    try {
      setLoading(true);

      // 调用产品信息接口
      const res = await request<ApiResponse>({
        path: '/api/high-tank/app/product/info',
        method: 'GET',
        query: { id }, // 使用查询参数传递 id
        isloading: true, // 显示加载提示
      });

      // console.log('产品信息接口返回数据:', res);

      if (res.code === 200 && res.data) {
        setProduct(res.data);
      } else {
        showToast({
          title: res.message || '获取产品信息失败',
          icon: 'none',
        });
      }
    } catch (err) {
      // console.error('获取产品信息失败:', err);
      showToast({
        title: '获取产品信息失败，请稍后重试',
        icon: 'none',
      });
    } finally {
      setLoading(false);
    }
  };

  // 创建订单接口返回类型
  interface CreateOrderResponse {
    code: number;
    message: string;
    data: string; // 订单号
  }

  // 立即支付
  const handlePayment = async () => {
    // 显示加载提示
    showToast({
      title: '正在创建订单...',
      icon: 'loading',
      mask: true,
      duration: 20000,
    });

    // 根据产品类型准备不同的请求参数
    let requestBody = {};

    if (productTypeNum === 1) {
      // 员工次卡
      requestBody = {
        productId: productId, // 产品ID，保持字符串格式
        passengerName: buyerName, // 员工姓名
        phone: buyerPhone, // 手机号
        userNo: buyerJobNumber, // 员工工号
      };
    } else {
      // 旅客高舱
      requestBody = {
        productId: productId, // 产品ID，保持字符串格式
        passengerName: passengerName, // 旅客姓名
        phone: passengerPhone, // 手机号
        fltNo: flightNumber, // 航班号
        fltDate: flightDate, // 航班日期
        boardGate: gateNumber, // 登机口
        seatNo: seatNumber, // 座位号
      };
    }

    // 根据产品类型调用不同的创建订单接口
    const apiPath =
      productTypeNum === 1
        ? '/api/high-tank/app/order/add'
        : '/api/high-tank/app/order/add/passenger';

    // console.log(`调用接口: ${apiPath}，参数:`, requestBody);

    // 调用创建订单接口
    const orderRes = await request<CreateOrderResponse>({
      path: apiPath,
      method: 'POST',
      body: requestBody,
      isloading: false,
    });

    // console.log('创建订单接口返回数据:', orderRes);

    // 隐藏加载提示
    showToast({
      title: '订单创建成功',
      icon: 'success',
    });

    if (orderRes.code === 200 && orderRes.data) {
      // 订单创建成功，跳转到支付页面
      const orderNo = orderRes.data;

      // 简化支付页面参数，只传递订单号
      navigateTo({
        url: `/subpackagesHt/pages/payment/index?orderNo=${orderNo}`,
      });
    } else {
      // 订单创建失败
      showToast({
        title: orderRes.message || '创建订单失败',
        icon: 'none',
        duration: 2000,
      });
    }
  };

  // 获取显示用的产品名称和价格
  const displayProductName = product?.productName || '';
  const displayPrice = product?.price || '';
  const displayTimes = product?.times || '';

  // 加载状态显示
  if (loading) {
    return (
      <View className="confirm-page">
        <NavBar title={productTypeNum === 1 ? '产品确认' : '信息确认'} />
        <View className="loading-state">
          <Text className="loading-text">加载中...</Text>
        </View>
      </View>
    );
  }

  return (
    <View className="confirm-page">
      {/* 顶部导航栏 */}
      <NavBar title={productTypeNum === 1 ? '产品确认' : '信息确认'} />

      {/* 内容区域 */}
      <View className="confirm-body">
        {/* 产品信息卡片 */}
        <View className="info-card">
          <Text className="card-title">产品信息</Text>
          <View className="product-card">
            <View className="product-card-content">
              <Image
                className="card-v-image"
                src={productImageUrl}
                mode="aspectFill"
              />
              {/* 图片加载状态 */}
              {imageLoading && (
                <View className="image-loading">
                  <Text className="loading-text">图片加载中...</Text>
                </View>
              )}
            </View>
            <View className="product-details">
              <Text className="product-name">{displayProductName}</Text>
              <View className="product-info-row">
                <Text className="product-info-text">数量:1</Text>
                <Text className="product-info-text">次数:{displayTimes}</Text>
              </View>
            </View>
          </View>
        </View>

        {/* 根据产品类型显示不同的信息 */}
        {productTypeNum === 1 ? (
          // 员工次卡 - 显示购买人信息
          <View className="info-card">
            <Text className="card-title">购买人信息</Text>
            <View className="buyer-info">
              <View className="info-row">
                <Text className="info-label">员工姓名</Text>
                <Text className="info-value">{buyerName}</Text>
              </View>
              <View className="info-row">
                <Text className="info-label">联系方式</Text>
                <Text className="info-value">{buyerPhone}</Text>
              </View>
              <View className="info-row">
                <Text className="info-label">员工工号</Text>
                <Text className="info-value">{buyerJobNumber}</Text>
              </View>
            </View>
          </View>
        ) : (
          // 旅客高舱 - 显示航班信息和旅客信息
          <>
            {/* 航班信息 */}
            <View className="info-card">
              <Text className="card-title">航班信息</Text>
              <View className="flight-info">
                <View className="info-header">
                  <Text className="flight-number">{flightNumber}</Text>
                  <Text className="flight-date">{flightDate}</Text>
                </View>
                <View className="info-details">
                  <View className="info-item">
                    <Text className="info-label">登机口</Text>
                    <Text className="info-value">{gateNumber}</Text>
                  </View>
                  <View className="info-item">
                    <Text className="info-label">座位号</Text>
                    <Text className="info-value">{seatNumber}</Text>
                  </View>
                </View>
              </View>
            </View>

            {/* 旅客信息 */}
            <View className="info-card">
              <Text className="card-title">旅客信息</Text>
              <View className="passenger-info">
                <View className="info-header">
                  <Text className="passenger-name">{passengerName}</Text>
                </View>
                <View className="info-details">
                  <View className="info-item">
                    <Text className="info-label">联系电话</Text>
                    <Text className="info-value">{passengerPhone}</Text>
                  </View>
                </View>
              </View>
            </View>
          </>
        )}

        {/* 支付金额卡片 */}
        <View className="payment-card">
          <View className="payment-row">
            <View>
              <Text className="payment-label">实际支付</Text>
            </View>
            <View className="price-container">
              <Text className="price-symbol">¥</Text>
              <Text className="price-value">{displayPrice}</Text>
            </View>
          </View>
        </View>
      </View>

      {/* 底部支付栏 */}
      <View className="confirm-footer">
        <View className="footer-price">
          <Text className="footer-price-label">订单总额</Text>
          <View className="footer-price-container">
            <Text className="footer-price-symbol">¥</Text>
            <Text className="footer-price-value">{displayPrice}</Text>
          </View>
        </View>
        <View>
          <Button className="payment-button" onClick={handlePayment}>
            立即支付
          </Button>
        </View>
      </View>
    </View>
  );
}
