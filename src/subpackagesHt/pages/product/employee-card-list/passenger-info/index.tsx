import { View, Text, Image, Button } from '@tarojs/components';
import {
  useLoad,
  useRouter,
  showToast,
  navigateTo,
  scanCode,
} from '@tarojs/taro';
import { useState } from 'react';
import NavBar from '@ht/components/NavBar';
import { fetchUserInfoById } from '@ht/utils/hooks/useUserInfo';
import { EmployeeInfo, isValidEmployeeInfo } from '@ht/store/userStore';
import { getImageUrl } from '@/subpackagesHt/utils/image';
import './index.less';

/**
 * 旅客信息填写页面
 */
export default function PassengerInfo() {
  const router = useRouter();
  const { productId, productType = '1' } = router.params;
  const [buyerName, setBuyerName] = useState('');
  const [buyerPhone, setBuyerPhone] = useState('');
  const [buyerJobNumber, setBuyerJobNumber] = useState('');

  // 是否已扫码获取员工信息
  const [hasScannedInfo, setHasScannedInfo] = useState(false);

  useLoad(() => {
    // console.log('旅客信息填写页面加载，产品ID:', productId);
  });

  // 设置所有员工信息的辅助函数
  const setEmployeeInfo = (info: EmployeeInfo) => {
    const { name = '', phone = '', jobNumber = '' } = info;

    setBuyerName(name);
    setBuyerPhone(phone);
    setBuyerJobNumber(jobNumber);
    setHasScannedInfo(true);
  };

  // 尝试解析扫码结果为员工信息
  const parseEmployeeInfo = (scanResult: string): EmployeeInfo | null => {
    try {
      // 尝试解析为JSON
      const data = JSON.parse(scanResult);
      return data;
    } catch (error) {
      // 如果不是JSON，尝试其他格式解析
      // 这里可以添加其他格式的解析逻辑

      // 例如，如果是特定格式的字符串，可以尝试正则表达式解析
      // const match = scanResult.match(/name:([^,]+),phone:([^,]+),id:([^,]+)/);
      // if (match) {
      //   return {
      //     name: match[1],
      //     phone: match[2],
      //     jobNumber: match[3]
      //   };
      // }

      // console.error('无法解析扫码结果:', error);
      return null;
    }
  };

  // 通过API获取用户信息
  const fetchUserInfo = async (userId: string) => {
    // 调用公共方法获取用户信息
    const employeeInfo = await fetchUserInfoById(userId);

    // 如果获取成功，设置员工信息
    if (employeeInfo) {
      setEmployeeInfo(employeeInfo);
      return true;
    }

    return false;
  };

  // 扫码录入员工信息
  const handleScanCode = () => {
    // 调用扫码API
    scanCode({
      onlyFromCamera: false, // 允许从相册选择二维码
      scanType: ['qrCode', 'barCode'], // 扫码类型，支持二维码和条形码
      success: async res => {
        try {
          // 获取扫码结果
          const scanResult = res.result;
          // console.log('扫码结果:', scanResult);

          // 尝试直接将扫码结果作为用户ID调用API
          const success = await fetchUserInfo(scanResult);

          // 如果API调用失败，尝试解析扫码结果
          if (!success) {
            // 解析扫码结果
            const employeeData = parseEmployeeInfo(scanResult);

            // 验证解析结果
            if (employeeData && isValidEmployeeInfo(employeeData)) {
              // 设置员工信息
              setEmployeeInfo(employeeData);

              showToast({
                title: '已获取员工信息',
                icon: 'success',
              });
            } else {
              // console.warn('扫码结果缺少必要信息或格式不正确');

              showToast({
                title: '未能识别到该员工',
                icon: 'none',
              });
            }
          }
        } catch (error) {
          // console.error('处理扫码结果失败:', error);

          showToast({
            title: '解析员工信息失败，请手动填写',
            icon: 'none',
          });
        }
      },
      // fail: error => {
      fail: () => {
        // console.error('扫码失败:', error);
        showToast({
          title: '扫码失败',
          icon: 'none',
        });
      },
    });
  };

  // 下一步按钮点击事件
  const handleNextStep = () => {
    // 表单验证
    if (!buyerName) {
      showToast({
        title: '请输入姓名',
        icon: 'none',
      });
      return;
    }
    if (!buyerPhone) {
      showToast({
        title: '请输入手机号',
        icon: 'none',
      });
      return;
    }
    if (!buyerJobNumber) {
      showToast({
        title: '请输入员工编号',
        icon: 'none',
      });
      return;
    }

    // 跳转到产品确认页面
    navigateTo({
      url: `/subpackagesHt/pages/product/employee-card-list/confirm/index?productId=${productId}&productType=${productType}&buyerName=${buyerName}&buyerPhone=${buyerPhone}&buyerJobNumber=${buyerJobNumber}`,
    });
  };

  // 判断表单是否填写完成
  const isFormComplete = buyerName && buyerPhone && buyerJobNumber;

  return (
    <View className="passenger-info">
      {/* 顶部导航栏 - 使用透明背景 */}
      <NavBar title="添加购买人信息" transparent />

      {/* 内容区域 */}
      <View className="passenger-body">
        {/* 购买人信息卡片 */}
        <View className="info-card">
          {/* 卡片头部 */}
          <View className="card-header-row">
            <Text className="card-title">购买人信息</Text>
            {/* 扫码录入按钮 */}
            <View className="scan-button" onClick={handleScanCode}>
              <Image
                className="scan-icon"
                src={getImageUrl('qr_scan_icon.svg')}
                mode="aspectFit"
              />
              <Text className="scan-text">扫员工条形码录入</Text>
            </View>
          </View>

          {hasScannedInfo ? (
            // 已扫码获取员工信息，显示员工信息
            <View className="employee-info">
              <View className="info-row">
                <Text className="info-label">员工姓名</Text>
                <Text className="info-value">{buyerName}</Text>
              </View>
              <View className="info-row">
                <Text className="info-label">联系方式</Text>
                <Text className="info-value">{buyerPhone}</Text>
              </View>
              <View className="info-row">
                <Text className="info-label">员工工号</Text>
                <Text className="info-value">{buyerJobNumber}</Text>
              </View>
            </View>
          ) : (
            <></>
            // 未扫码，显示表单
            // <View className="form-group">
            //   <View className="form-item">
            //     <Text className="form-label">姓名</Text>
            //     <Input
            //       className="form-input"
            //       placeholder="请输入姓名"
            //       value={buyerName}
            //       onInput={e => setBuyerName(e.detail.value)}
            //     />
            //   </View>
            //   <View className="form-item">
            //     <Text className="form-label">手机号</Text>
            //     <Input
            //       className="form-input"
            //       placeholder="请输入手机号"
            //       type="number"
            //       maxlength={11}
            //       value={buyerPhone}
            //       onInput={e => setBuyerPhone(e.detail.value)}
            //     />
            //   </View>
            //   <View className="form-item">
            //     <Text className="form-label">员工工号</Text>
            //     <View className="input-with-button">
            //       <Input
            //         className="form-input"
            //         placeholder="请输入员工工号"
            //         value={buyerJobNumber}
            //         onInput={e => setBuyerJobNumber(e.detail.value)}
            //       />
            //       <View
            //         className="search-button"
            //         onClick={() =>
            //           buyerJobNumber && fetchUserInfo(buyerJobNumber)
            //         }
            //       >
            //         <Text className="search-text">查询</Text>
            //       </View>
            //     </View>
            //   </View>
            // </View>
          )}
        </View>
      </View>

      <View className="passenger-footer">
        <Button
          className={`next-button ${isFormComplete ? 'active' : ''}`}
          onClick={handleNextStep}
          disabled={!isFormComplete}
        >
          下一步
        </Button>
      </View>
    </View>
  );
}
