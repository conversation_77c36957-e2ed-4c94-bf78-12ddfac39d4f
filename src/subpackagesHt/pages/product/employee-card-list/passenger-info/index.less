/* 旅客信息填写页面样式 */
.passenger-info {
  min-height: 100vh;
  background-color: #f0f4fa;
  display: flex;
  flex-direction: column;
  position: relative;
}

/* 页面背景渐变 - 精确匹配 Figma 设计 */
.passenger-info::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 240px;
  background: linear-gradient(
    to bottom,
    #cfe1ff 0%,
    #e3eeff 54.08%,
    #f0f4fa 100%
  );
  z-index: -1;
}

/* 内容区域 - 精确匹配 Figma 设计 */
.passenger-body {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 16px;
  padding-bottom: 80px; /* 为底部按钮留出空间 */
  position: relative;
  z-index: 1;
  width: 100%;
  box-sizing: border-box;
}

/* 信息卡片 */
.info-card {
  background-color: #ffffff;
  border-radius: 12px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  width: 100%;
  margin-bottom: 12px;
  border: 1px solid #ffffff;
}

/* 卡片头部行 */
.card-header-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #f0f4fa;
}

.card-title {
  font-size: 16px;
  font-weight: 500;
  color: #1d1f20;
  font-family: 'MiSans', sans-serif;
  line-height: 1.5em;
}

/* 扫码按钮 - 精确匹配 Figma 设计 */
.scan-button {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f3f5ff;
  padding: 4px 8px;
  border-radius: 8px;
  cursor: pointer;
}

.scan-icon {
  width: 16px;
  height: 16px;
  margin-right: 4px;
  flex-shrink: 0;
}

.scan-text {
  font-size: 12px;
  font-weight: 400;
  color: #0052d9;
  font-family: 'MiSans', sans-serif;
  line-height: 1.67em;
}

/* 员工信息显示区域 */
.employee-info {
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 16px;
  width: 100%;
  box-sizing: border-box;
}

.info-row {
  display: flex;
  align-items: center;
  gap: 12px;
}

.info-label {
  font-size: 14px;
  font-weight: 400;
  color: #4f5170;
  font-family: 'MiSans', sans-serif;
  line-height: 1.57em;
  width: 70px;
}

.info-value {
  font-size: 14px;
  font-weight: 500;
  color: #1d1f20;
  font-family: 'MiSans', sans-serif;
  line-height: 1.57em;
}

/* 表单区域 - 精确匹配 Figma 设计 */
.form-group {
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 16px;
  width: 100%;
  box-sizing: border-box;
}

.form-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-label {
  font-size: 14px;
  color: #737578;
}

.form-input {
  height: 44px;
  border: 1px solid #e7e8e9;
  border-radius: 8px;
  padding: 0 12px;
  font-size: 14px;
  color: #1d1f20;
  background-color: #ffffff;
  box-sizing: border-box;
  width: 100%;
}

/* 带按钮的输入框容器 */
.input-with-button {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
}

.input-with-button .form-input {
  flex: 1;
}

/* 查询按钮 */
.search-button {
  height: 44px;
  background-color: #1663f8;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 12px;
  cursor: pointer;
}

.search-text {
  font-size: 14px;
  color: #ffffff;
  font-weight: 500;
}

/* 产品信息 */
.product-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
}

.product-name {
  font-size: 14px;
  color: #1d1f20;
}

.product-price {
  font-size: 16px;
  font-weight: 500;
  color: #1d1f20;
}

/* 底部按钮 - 精确匹配 Figma 设计 */
.passenger-footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #ffffff;
  padding: 8px 16px;
  z-index: 100;
  border-top-left-radius: 12px;
  border-top-right-radius: 12px;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}

.next-button {
  width: 100%;
  height: 44px;
  line-height: 44px;
  background-color: #f4f5f5;
  color: #c4c7ca !important;
  font-size: 16px;
  font-weight: 400;
  text-align: center;
  border: none;
  margin: 0;
  font-family: 'MiSans', sans-serif;
  line-height: 1.5em;
  display: flex;
  justify-content: center;
  align-items: center;
}

.next-button.active {
  color: #ffffff !important;
  background-color: #1663f8;
}
