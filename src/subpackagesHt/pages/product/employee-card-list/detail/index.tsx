import { View, Text, Image, Button } from '@tarojs/components';
import { useLoad, useRouter, navigateTo, showToast } from '@tarojs/taro';
import { useState } from 'react';
import request from '@ht/api/apiConfig';
import { ProductDisplay, Product } from '@ht/types/product';
import NavBar from '@ht/components/NavBar';
import { getImageUrl } from '@/subpackagesHt/utils/image';
import { useImageUrl } from '@/subpackagesHt/utils/useImageUrl';
import './index.less';

/**
 * 员工次卡详情页面
 */
export default function EmployeeCardDetail() {
  const router = useRouter();
  const { id } = router.params;
  const [loading, setLoading] = useState(false);
  const [product, setProduct] = useState<ProductDisplay | null>(null);

  // 使用图片处理Hook
  const { imageUrl: productImageUrl, loading: imageLoading } = useImageUrl(
    product?.image,
    'detail_bg_blue.svg',
  );

  useLoad(() => {
    // console.log('员工次卡详情页面加载，产品ID:', id);
    fetchProductDetail();
  });

  // 将产品数据转换为展示格式
  const convertToDisplayProduct = (productData: Product): ProductDisplay => {
    return {
      ...productData,
      price: parseFloat(productData.price), // 将价格字符串转为数字
      tags: [
        {
          id: '1',
          name: productData.productType === 1 ? '员工次卡' : '高舱休息室',
        },
        {
          id: '2',
          name: productData.effectType === 1 ? '长期有效' : '当日有效',
        },
        {
          id: '3',
          name: productData.times ? `${productData.times}次` : '',
        },
        {
          id: '4',
          name: productData?.effectTime
            ? `${productData.effectTime}小时/次`
            : '',
        },
      ],
    };
  };

  // 获取产品详情数据
  const fetchProductDetail = async () => {
    if (!id) return;
    try {
      setLoading(true);

      // 调用产品详情接口
      const res = await request<{
        code: number;
        message: string;
        data: Product;
      }>({
        path: '/api/high-tank/app/product/info',
        method: 'GET',
        query: { id }, // 使用查询参数传递 id
        isloading: true, // 显示加载提示
      });

      // console.log('产品详情接口返回数据:', res);
      // console.log('产品详情接口请求参数:', {
      // path: '/api/high-tank/app/product/info',
      // id,
      // });

      if (res.code === 200 && res.data) {
        // 将接口返回的数据转换为展示所需的格式
        const displayProduct = convertToDisplayProduct(res.data);
        setProduct(displayProduct);
      } else {
        showToast({
          title: res.message || '获取产品详情失败',
          icon: 'none',
        });
        // 接口返回错误，不使用模拟数据
        // console.error('接口返回错误:', res.message);
      }
    } catch (error) {
      // console.error('获取产品详情失败:', error);
      showToast({
        title: '获取产品详情失败，请稍后重试',
        icon: 'none',
      });
    } finally {
      setLoading(false);
    }
  };

  // 立即购买按钮点击事件
  const handleBuyNow = () => {
    if (!product) return;

    // 根据产品类型决定跳转到不同的乘客信息页面
    const passengerInfoUrl =
      product.productType === 1
        ? `/subpackagesHt/pages/product/employee-card-list/passenger-info/index` // 员工次卡
        : `/subpackagesHt/pages/product/lounge-list/passenger-info/index`; // 旅客高舱

    // 跳转到对应的旅客信息填写页面
    navigateTo({
      url: `${passengerInfoUrl}?productId=${product.id}&productType=${product.productType}`,
    });
  };

  // 如果没有产品数据，显示加载中或错误提示
  if (!product) {
    return (
      <View className="product-detail">
        {/* 顶部导航栏 */}
        <NavBar title="产品详情" showBack />

        <View className="loading-state">
          <Text className="loading-text">
            {loading ? '加载中...' : '暂无产品数据，请返回重试'}
          </Text>
          {!loading && (
            <View
              className="retry-button"
              onClick={() => {
                if (id) fetchProductDetail();
              }}
            >
              <Text className="retry-text">重新加载</Text>
            </View>
          )}
        </View>
      </View>
    );
  }

  return (
    <View className="product-detail">
      {/* 顶部导航栏 - 使用透明背景 */}
      <NavBar title="产品详情" transparent />

      {/* 产品内容区 */}
      <View className="product-body">
        {/* 产品头部信息 - 蓝色背景卡片 */}
        <View className="product-header">
          <View className="blue-card">
            {/* 背景图片 - 使用图片处理Hook */}
            <Image
              className="card-bg-image"
              src={productImageUrl}
              mode="aspectFill"
            />
            {/* 图片加载状态 */}
            {imageLoading && (
              <View className="image-loading">
                <Text className="loading-text">图片加载中...</Text>
              </View>
            )}
            {/* V形图标 */}
            <Image
              className="card-v-image"
              src={getImageUrl('detail_v_blue.svg')}
              mode="aspectFill"
            />
            {/* 标签图标 */}
            <View className="card-tag-icon">
              <Image
                className="tag-bg"
                src={getImageUrl('detail_tag.svg')}
                mode="aspectFit"
              />
            </View>
            {/* 产品名称 */}
            <View className="card-title-container">
              <Text className="card-title">{product.productName}</Text>
            </View>
          </View>
        </View>

        {/* 产品信息卡片 */}
        <View className="product-info-card">
          <Text className="info-title">{product.productName}</Text>
          <View className="tag-group">
            {product.tags.map(tag => (
              <View key={tag.id} className="tag-item">
                <Text className="tag-text">{tag.name}</Text>
              </View>
            ))}
          </View>
        </View>

        {/* 产品描述 */}
        <View className="product-section">
          <Text className="section-title">产品描述</Text>
          <View className="section-content">
            <Text className="description-text">
              {product.discribe || '暂无产品说明'}
            </Text>
          </View>
        </View>
      </View>

      {/* 底部购买栏 */}
      <View className="product-footer">
        <View className="price-container">
          <View className="price-wrapper">
            <Text className="price-label">价格</Text>
            <View className="price-value">
              <Text className="price-symbol">¥</Text>
              <Text className="price-number">{product.price}</Text>
            </View>
          </View>
        </View>
        <View className="buy-container">
          <Button className="buy-button" onClick={handleBuyNow}>
            立即购买
          </Button>
        </View>
      </View>
    </View>
  );
}
