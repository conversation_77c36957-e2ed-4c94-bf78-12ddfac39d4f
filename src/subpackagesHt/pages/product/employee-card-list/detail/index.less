/* 产品详情页面 */
.product-detail {
  min-height: 100vh;
  background-color: #f0f4fa;
  padding-bottom: 80px; /* 为底部按钮留出空间 */
  position: relative;
  display: flex;
  flex-direction: column;
}

/* 加载状态 */
.loading-state {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 300px;
  background-color: #ffffff;
  border-radius: 12px;
  margin: 16px;
  box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.05);
}

.loading-text {
  font-family: 'MiSans', sans-serif;
  font-size: 14px;
  color: #9aa2ca;
  margin-bottom: 16px;
}

.retry-button {
  margin-top: 16px;
  padding: 8px 16px;
  background-color: #0052d9;
  border-radius: 4px;
  cursor: pointer;
}

.retry-text {
  font-family: 'MiSans', sans-serif;
  font-size: 14px;
  color: #ffffff;
}

/* 产品内容区 */
.product-body {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 16px;
}

/* 产品头部 - 蓝色背景卡片 */
.product-header {
  width: 100%;
}

.blue-card {
  position: relative;
  width: 100%;
  height: 200px;
  border-radius: 12px;
  overflow: hidden;
  background: linear-gradient(to right, #98d4ff, #1f91e3);
  box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.05);
}

.card-bg-image {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
}

.image-loading {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1;
}

.image-loading .loading-text {
  font-size: 12px;
  color: #666666;
}

.card-v-image {
  position: absolute;
  width: 80px;
  height: 80px;
  right: -10px;
  bottom: -10px;
  opacity: 0.1;
}

.card-tag-icon {
  position: absolute;
  width: 40px;
  height: 40px;
  left: 16px;
  top: 16px;
}

.tag-bg {
  width: 100%;
  height: 100%;
}

.card-title-container {
  position: absolute;
  left: 16px;
  bottom: 16px;
}

.card-title {
  font-size: 20px;
  font-weight: 400;
  color: #ffffff;
  text-shadow: 0px 2px 4px rgba(0, 0, 0, 0.05);
  font-family: 'YouSheBiaoTiYuan', 'YouSheBiaoTiHei', sans-serif; /* 使用设计图中的字体 */
}

/* 产品信息卡片 */
.product-info-card {
  background-color: #ffffff;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.05);
}

.info-title {
  font-family: 'MiSans', sans-serif;
  font-size: 16px;
  font-weight: 500;
  color: #333333;
  margin-bottom: 8px;
  line-height: 1.5;
}

.tag-group {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.tag-item {
  padding: 2px 4px;
  border-radius: 4px;
  border: 1px solid #e7e8e9;
  background-color: #ffffff;
}

.tag-text {
  font-family: 'MiSans', sans-serif;
  font-size: 12px;
  color: #404245;
  opacity: 0.8;
  line-height: 1.67;
}

/* 产品描述部分 */
.product-section {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.section-title {
  font-family: 'MiSans', sans-serif;
  font-size: 16px;
  font-weight: 500;
  color: #1d1f20;
  line-height: 1.5;
}

.section-content {
  background-color: #ffffff;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.05);
}

.description-text {
  font-family: 'MiSans', sans-serif;
  font-size: 14px;
  color: #737578;
  line-height: 1.57;
  white-space: pre-wrap;
}

/* 底部购买栏 */
.product-footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #ffffff;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.04);
  z-index: 100;
}

.price-container {
  display: flex;
  align-items: center;
  margin-left: 16px;
}

.price-wrapper {
  display: flex;
  flex-direction: column;
}

.price-label {
  font-family: 'MiSans', sans-serif;
  font-size: 12px;
  color: #737578;
  line-height: 1.67;
}

.price-value {
  display: flex;
  align-items: baseline;
}

.price-symbol {
  font-family: 'MiSans', sans-serif;
  font-size: 12px;
  font-weight: 500;
  color: #f84f2a;
  margin-right: 2px;
  line-height: 1.67;
}

.price-number {
  font-family: 'MiSans', sans-serif;
  font-size: 20px;
  font-weight: 500;
  color: #f84f2a;
  line-height: 1.4;
}

.buy-container {
  margin-left: 8px;
}

.buy-button {
  height: 44px;
  line-height: 44px;
  padding: 0 32px;
  background-color: #1663f8;
  color: #ffffff;
  font-family: 'MiSans', sans-serif;
  font-size: 16px;
  font-weight: 600;
  border-radius: 99px;
  text-align: center;
  border: none;
}

/* 底部安全区域 */
.safe-area-bottom {
  height: env(safe-area-inset-bottom);
  background-color: #ffffff;
}
