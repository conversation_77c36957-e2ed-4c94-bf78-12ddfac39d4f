.employee-card-verification-page {
  min-height: 100vh;
  background-color: #f0f4fa;
  display: flex;
  flex-direction: column;
  position: relative;
}

/* 加载状态 */
.loading-container {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
}

.loading-text {
  font-family: 'MiSans', sans-serif;
  font-size: 16px;
  font-weight: 400;
  color: #4f5170;
}

/* 页面背景渐变 */
.employee-card-verification-page::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 240px;
  background: linear-gradient(
    to bottom,
    #cfe1ff 0%,
    #e3eeff 54.08%,
    #f0f4fa 100%
  );
  z-index: -1;
}

/* 页面内容 */
.page-body {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 16px;
  gap: 12px;
  width: 100%;
  box-sizing: border-box;
}

/* 步骤指示器 */
.steps-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  margin-bottom: 12px;
}

.step {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.step-number {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: #ffffff;
  border: 1px solid #b6c6e2;
  display: flex;
  justify-content: center;
  align-items: center;
  font-family: 'MiSans', sans-serif;
  font-size: 14px;
  font-weight: 500;
  color: #404245;
}

.step.active .step-number {
  background-color: #0052d9;
  border: none;
  color: #ffffff;
}

.step-text {
  font-family: 'MiSans', sans-serif;
  font-size: 14px;
  font-weight: 400;
  color: #404245;
  line-height: 1.57em;
}

.step.active .step-text {
  font-weight: 500;
  color: #0052d9;
}

.step-divider {
  width: 24px;
  height: 1.5px;
  background-color: #b6c6e2;
}

/* 信息卡片 */
.info-card {
  background-color: #ffffff;
  border-radius: 12px;
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 12px;
  border: 1px solid #ffffff;
  box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.05);
}

.card-title {
  font-family: 'MiSans', sans-serif;
  font-size: 16px;
  font-weight: 500;
  color: #1d1f20;
  line-height: 1.5em;
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.info-label {
  font-family: 'MiSans', sans-serif;
  font-size: 14px;
  font-weight: 400;
  color: #4f5170;
  line-height: 1.57em;
}

.info-value {
  font-family: 'MiSans', sans-serif;
  font-size: 14px;
  font-weight: 500;
  color: #1d1f20;
  line-height: 1.57em;
}

/* 产品卡片 */
.product-card {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border-radius: 8px;
  border: 1px solid #efefff;
  margin-bottom: 12px;

  &:last-child {
    margin-bottom: 0;
  }
}

.product-image {
  width: 48px;
  height: 48px;
  border-radius: 4px;
  background: linear-gradient(to right, #1fc3e3, #6ae7ff, #1fc3e3);
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  overflow: hidden;
}

.product-image::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to right, #98d4ff, #1f91e3);
  opacity: 0.5;
  z-index: 1;
}

.product-image-v {
  font-family: 'MiSans', sans-serif;
  font-size: 24px;
  font-weight: bold;
  color: #ffffff;
  opacity: 0.1;
  position: relative;
  z-index: 2;
}

.product-content {
  display: flex;
  flex-direction: column;
  gap: 4px;
  flex: 1;
}

.product-name {
  font-family: 'MiSans', sans-serif;
  font-size: 14px;
  font-weight: 500;
  color: #333333;
  line-height: 1.57em;
}

.product-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.product-detail {
  font-family: 'MiSans', sans-serif;
  font-size: 12px;
  font-weight: 400;
  color: #404245;
  line-height: 1.67em;
  opacity: 0.8;
}

/* 底部按钮 */
.bottom-bar {
  padding: 8px 16px;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  box-sizing: border-box;
  background-color: #ffffff;
  border-top-left-radius: 12px;
  border-top-right-radius: 12px;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}

.next-button {
  flex: 1;
  width: 50%;
  height: 44px;
  line-height: 44px;
  background-color: #0052d9;
  color: #ffffff;
  font-family: 'MiSans', sans-serif;
  font-size: 16px;
  font-weight: 500;
  border-radius: 6px;
  text-align: center;
  border: none;
  transition: all 0.3s ease;
}

.next-button.disabled {
  background-color: #cccccc;
  color: #666666;
  cursor: not-allowed;
  opacity: 0.7;
}

.button-group {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  gap: 12px;
}

.prev-button {
  flex: 1;
  width: 50%;
  height: 44px;
  line-height: 44px;
  background-color: #ffffff;
  color: #0052d9;
  font-family: 'MiSans', sans-serif;
  font-size: 16px;
  font-weight: 500;
  border-radius: 6px;
  text-align: center;
  border: 1px solid #0052d9;
}

.submit-button {
  flex: 1;
  width: 50%;
  height: 44px;
  line-height: 44px;
  background-color: #0052d9;
  color: #ffffff;
  font-family: 'MiSans', sans-serif;
  font-size: 16px;
  font-weight: 500;
  border-radius: 6px;
  text-align: center;
  border: none;
}

/* 优惠券扫描卡片 */
.coupon-scan-card {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 16px;
  background-color: #f5f8ff;
  border: 1px dashed #1064ff;
  border-radius: 8px;
  cursor: pointer;
  position: relative;
}

.coupon-scan-icon {
  width: 24px;
  height: 24px;
}

.coupon-scan-text {
  font-family: 'MiSans', sans-serif;
  font-size: 16px;
  font-weight: 500;
  color: #1064ff;
  line-height: 1.5em;
}

/* 优惠券信息容器 */
.coupon-info-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 8px 0;
}

.coupon-info-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.coupon-info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.coupon-info-label {
  font-family: 'MiSans', sans-serif;
  font-size: 14px;
  font-weight: 400;
  color: #4f5170;
  line-height: 1.57em;
}

.coupon-info-value {
  font-family: 'MiSans', sans-serif;
  font-size: 14px;
  font-weight: 500;
  color: #1d1f20;
  line-height: 1.57em;
}

.coupon-refresh-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  cursor: pointer;
}

.refresh-icon {
  width: 16px;
  height: 16px;
}

.refresh-text {
  font-family: 'MiSans', sans-serif;
  font-size: 14px;
  font-weight: 400;
  color: #1064ff;
  line-height: 1.57em;
}

.coupon-product-tag {
  background-color: #e8f4ff;
  border-radius: 4px;
  padding: 2px 8px;
  height: 24px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.coupon-product-text {
  font-family: 'MiSans', sans-serif;
  font-size: 14px;
  font-weight: 500;
  color: #1064ff;
  line-height: 1.57em;
}

/* 添加亲友页面样式 */
.add-relative-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.add-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  padding: 4px 8px;
  background-color: #f3f5ff;
  border-radius: 8px;
  cursor: pointer;
}

.add-icon {
  width: 16px;
  height: 16px;
}

.add-text {
  font-family: 'MiSans', sans-serif;
  font-size: 12px;
  font-weight: 400;
  color: #0052d9;
  line-height: 1.67em;
}

.relative-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background-color: #f3f5ff;
  border-radius: 12px;
  margin-bottom: 0;
}

.relative-title {
  font-family: 'MiSans', sans-serif;
  font-size: 16px;
  font-weight: 500;
  color: #1d1f20;
  line-height: 1.5em;
}

.relative-tag {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0 8px;
  height: 20px;
  background: linear-gradient(90deg, #0052d9 0%, rgba(0, 82, 217, 0) 100%);
  border-radius: 4px 99px 99px 4px;
}

.relative-tag-text {
  font-family: 'MiSans', sans-serif;
  font-size: 12px;
  font-weight: 500;
  color: #ffffff;
  line-height: 1.67em;
}

.delete-icon {
  width: 20px;
  height: 20px;
  cursor: pointer;
}

.divider {
  width: 100%;
  height: 1px;
  background-color: #efefff;
  margin: 16px 0;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  margin-bottom: 16px;
}

.section-title {
  font-family: 'MiSans', sans-serif;
  font-size: 16px;
  font-weight: 500;
  color: #1d1f20;
  line-height: 1.5em;
}

.scan-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  padding: 4px 8px;
  background-color: #f3f5ff;
  border-radius: 8px;
  cursor: pointer;
}

.scan-icon {
  width: 16px;
  height: 16px;
}

.scan-text {
  font-family: 'MiSans', sans-serif;
  font-size: 12px;
  font-weight: 400;
  color: #0052d9;
  line-height: 1.67em;
}

.form-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-label {
  font-size: 14px;
  font-weight: 400;
  color: #4f5170;
  font-family: 'MiSans', sans-serif;
  line-height: 1.57em;
}

.form-input {
  height: 40px;
  padding: 6px 8px;
  font-size: 14px;
  color: #9aa2ca;
  background-color: #f2f4fa;
  border-radius: 8px;
  border: none;
  box-sizing: border-box;
  width: 100%;
}

.picker-view {
  position: relative;
  width: 100%;
}

.date-input {
  justify-content: space-between;
}

.placeholder {
  font-family: 'MiSans', sans-serif;
  font-size: 14px;
  font-weight: 400;
  color: #9aa2ca;
  line-height: 1.57em;
}

.calendar-icon {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  width: 24px;
  height: 24px;
}

/* 核销确认页面样式 */
.product-tag {
  background-color: #e8f4ff;
  border-radius: 4px;
  padding: 2px 8px;
  display: inline-flex;
  align-items: center;
}

.product-tag-text {
  font-family: 'MiSans', sans-serif;
  font-size: 14px;
  font-weight: 500;
  color: #0052d9;
  line-height: 1.57em;
}

.relative-info-card {
  background-color: #f2f6fc;
  border-radius: 12px;
  padding: 8px;
  margin-bottom: 12px;
}

.relative-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding: 12px 16px 12px 0;
  margin-bottom: 12px;
}

.relative-tag-container {
  display: flex;
  align-items: center;
  gap: 8px;
  position: relative;
}

.relative-name {
  font-family: 'MiSans', sans-serif;
  font-size: 16px;
  font-weight: 500;
  color: #1d1f20;
  line-height: 1.5em;
}

.relative-phone {
  font-family: 'MiSans', sans-serif;
  font-size: 14px;
  font-weight: 400;
  color: #737578;
  line-height: 1.57em;
}

.empty-relatives {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 24px 0;
}

.empty-text {
  font-family: 'MiSans', sans-serif;
  font-size: 14px;
  font-weight: 400;
  color: #9aa2ca;
  line-height: 1.57em;
}

.input-text {
  font-family: 'MiSans', sans-serif;
  font-size: 14px;
  font-weight: 400;
  color: #1d1f20;
  line-height: 1.57em;
}

/* 核销完成页面样式 */
.success-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 12px;
  padding: 32px 16px;
  background-color: #ffffff;
  border-radius: 12px;
  box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.05);
}

.success-icon {
  width: 64px;
  height: 64px;
  margin-bottom: 12px;
}

.success-text-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  margin-bottom: 24px;
}

.success-title {
  font-family: 'MiSans', sans-serif;
  font-size: 24px;
  font-weight: 500;
  color: #1d1f20;
  line-height: 1.33em;
}

.success-subtitle {
  font-family: 'MiSans', sans-serif;
  font-size: 16px;
  font-weight: 400;
  color: #737578;
  line-height: 1.5em;
}

/* 上线弹窗样式 */
.online-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(17, 24, 39, 0.25);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.online-modal-content {
  width: 80%;
  max-width: 320px;
  background-color: #ffffff;
  border-radius: 12px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  box-shadow: 0px 24px 48px -12px rgba(17, 24, 39, 0.25);
}

.online-modal-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 24px 24px 0;
  gap: 16px;
}

.online-icon-container {
  width: 40px;
  height: 40px;
  border-radius: 99px;
  background-color: #f3f5ff;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 8px;
}

.online-icon {
  width: 24px;
  height: 24px;
}

.online-time-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.online-status {
  font-family: 'MiSans', sans-serif;
  font-size: 16px;
  font-weight: 500;
  color: #23242d;
  line-height: 1.5em;
}

.online-time {
  font-family: 'MiSans', sans-serif;
  font-size: 20px;
  font-weight: 500;
  color: #23242d;
  line-height: 1.4em;
}

.online-modal-footer {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px 24px;
  gap: 16px;
}

.online-button {
  width: 100%;
  height: 44px;
  line-height: 44px;
  background-color: #0052d9;
  color: #ffffff;
  font-family: 'MiSans', sans-serif;
  font-size: 16px;
  font-weight: 500;
  border-radius: 99px;
  text-align: center;
  border: none;
}

.online-tip {
  font-family: 'MiSans', sans-serif;
  font-size: 14px;
  font-weight: 400;
  color: #9aa2ca;
  line-height: 1.57em;
  text-align: center;
}

/* 核销详情样式 */
.verification-details {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 16px;
  background-color: #f5f8ff;
  border-radius: 8px;
}

.details-title {
  font-family: 'MiSans', sans-serif;
  font-size: 16px;
  font-weight: 500;
  color: #1d1f20;
  line-height: 1.5em;
  margin-bottom: 8px;
}

.details-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.details-label {
  font-family: 'MiSans', sans-serif;
  font-size: 14px;
  font-weight: 400;
  color: #4f5170;
  line-height: 1.57em;
}

.details-value {
  font-family: 'MiSans', sans-serif;
  font-size: 14px;
  font-weight: 500;
  color: #1d1f20;
  line-height: 1.57em;
}
