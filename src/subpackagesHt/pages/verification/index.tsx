import { View, Text, Image, Button } from '@tarojs/components';
import {
  useLoad,
  useRouter,
  navigateTo,
  scanCode,
  showToast,
} from '@tarojs/taro';
import { useState, useEffect } from 'react';
import dayjs from 'dayjs';
import CustomTabBar from '@ht/components/CustomTabBar';
import NavBar from '@ht/components/NavBar';
import Dialog from '@ht/components/Dialog';
import { fetchUserInfoById } from '@ht/utils/hooks/useUserInfo';
import { useMenu } from '@ht/utils/hooks/useMenu';
import request from '@ht/api/apiConfig';
import { getImageUrl } from '@/subpackagesHt/utils/image';
import './index.less';

export default function Verification() {
  const router = useRouter();
  const { verificationCode } = router.params;

  // 使用菜单hook获取权限控制函数
  const { hasPermission } = useMenu();

  // 核销码
  const [code] = useState(verificationCode || '');

  // 弹窗状态
  const [dialogVisible, setDialogVisible] = useState(false);

  // 弹窗类型：error-错误提示，success-核销成功，online-上线，offline-下线
  const [dialogType, setDialogType] = useState('error');

  // 是否在线状态
  const [isOnline, setIsOnline] = useState(false);

  // 状态按钮是否加载中
  const [isStatusLoading, setIsStatusLoading] = useState(false);

  // 当前时间
  const [currentTime, setCurrentTime] = useState('');

  // 获取当前的在线状态
  const fetchWatchStatus = async () => {
    try {
      // 设置加载状态
      setIsStatusLoading(true);

      // 调用查询值班状态接口
      const response = await request({
        path: '/api/high-tank/app/user/queryWatchStatus',
        method: 'GET',
      });

      // console.log('获取在线状态结果:', response);

      if (response && response.code === 200) {
        // 根据接口返回设置在线状态
        // 状态值：1表示上线，2表示下线
        setIsOnline(response.data === 1);
      } else {
        // 接口调用失败，默认设置为离线状态
        setIsOnline(false);
        // console.error('获取在线状态失败:', response?.message);
      }
    } catch (error) {
      // console.error('获取在线状态失败:', error);
      // 默认设置为离线状态
      setIsOnline(false);
    } finally {
      // 无论成功失败，都取消加载状态
      setIsStatusLoading(false);
    }
  };

  useLoad(() => {
    // console.log('Verification page loaded. Code:', code);

    // 初始化当前时间
    updateCurrentTime();

    // 获取当前的在线状态
    fetchWatchStatus();
  });

  // 当弹窗显示时，每秒更新一次时间
  useEffect(() => {
    let timer: NodeJS.Timeout;

    if (
      dialogVisible &&
      (dialogType === 'online' || dialogType === 'offline')
    ) {
      // 初始化时间
      updateCurrentTime();

      // 每秒更新一次时间
      timer = setInterval(() => {
        updateCurrentTime();
      }, 1000);
    }

    // 清除定时器
    return () => {
      if (timer) {
        clearInterval(timer);
      }
    };
  }, [dialogVisible, dialogType]);

  // 更新当前时间
  const updateCurrentTime = () => {
    // 使用 dayjs 获取当前时间并格式化
    setCurrentTime(dayjs().format('HH:mm:ss'));
  };

  // 扫描员工卡号
  const handleScanEmployeeCard = () => {
    scanCode({
      scanType: ['qrCode', 'barCode', 'datamatrix', 'pdf417'], // 扫码类型，支持二维码和条形码
      success: async res => {
        // console.log('扫描结果:', res.result);

        // 使用扫描结果（员工ID）获取员工信息
        const result = await fetchUserInfoById(res.result);

        if (result) {
          // 员工信息获取成功

          // 使用jobNumber调用产品订单信息接口
          if (result.jobNumber) {
            try {
              const response = await request({
                path: '/api/high-tank/app/productCode/productOrderInfo',
                method: 'GET',
                query: { userNo: result.jobNumber },
                isloading: true,
              });

              // console.log('获取产品订单信息成功:', response);

              if (response && response.data) {
                // 核销成功，跳转到员工卡核销页面
                navigateTo({
                  url: `/subpackagesHt/pages/verification/employee-card-verification/index?jobNumber=${result.jobNumber}`,
                });
              } else {
                // // 未找到订单信息
                // showToast({
                //   title: '未找到该员工的订单信息',
                //   icon: 'none',
                //   duration: 2000,
                // });

                // 显示无可用产品弹窗
                setDialogType('no-product');
                setDialogVisible(true);
              }
            } catch (error) {
              // console.error('获取产品订单信息失败:', error);
              showToast({
                title: '获取订单信息失败',
                icon: 'none',
                duration: 2000,
              });
            }
          } else {
            // 员工信息中没有jobNumber
            showToast({
              title: '员工工号不存在',
              icon: 'none',
              duration: 2000,
            });
            setDialogType('error');
            setDialogVisible(true);
          }
        } else {
          // 员工信息获取失败，显示错误弹窗
          setDialogType('error');
          setDialogVisible(true);
        }
      },
      // fail: err => {
      fail: () => {
        // console.error('扫码失败:', err);
        showToast({
          title: '扫码失败',
          icon: 'none',
          duration: 2000,
        });
      },
    });
  };

  // 关闭弹窗
  const handleCloseDialog = () => {
    setDialogVisible(false);
  };

  // 处理上线
  const handleOnline = () => {
    updateCurrentTime();
    setDialogType('online');
    setDialogVisible(true);
  };

  // 处理下线
  const handleOffline = () => {
    updateCurrentTime();
    setDialogType('offline');
    setDialogVisible(true);
  };

  // 录入核销码
  const handleInputVerificationCode = () => {
    navigateTo({
      url: '/subpackagesHt/pages/verification/code-input/index',
    });
  };

  // 如果有核销码，显示核销码页面
  if (code) {
    return (
      <View className="verification-page">
        <NavBar title="核销码" showBack={false} />

        <View className="content">
          <View className="verification-content">
            <Text className="verification-title">核销码</Text>
            <Text className="verification-code">{code}</Text>
            <Text className="verification-tip">请向工作人员出示此码</Text>
          </View>
        </View>

        <CustomTabBar />
      </View>
    );
  }

  // 否则显示核销选项页面
  return (
    <View className="verification-page">
      <NavBar title="高舱休息室销售系统" showBack={false} />

      <View className="verification-container">
        <View className="section-header">
          <Text className="section-title">核销产品</Text>
          {/* 上下线按钮 - 需要 check_onLine 权限 */}
          {hasPermission('check_onLine') && (
            <View>
              <Button
                className={`status-button ${
                  isOnline ? 'offline-button-outside' : 'online-button-outside'
                }`}
                onClick={isOnline ? handleOffline : handleOnline}
                disabled={isStatusLoading}
              >
                {isStatusLoading ? (
                  <Text className="loading-text">加载中...</Text>
                ) : isOnline ? (
                  '下线'
                ) : (
                  '上线'
                )}
              </Button>
            </View>
          )}
        </View>

        <View className="card-group">
          {/* 如果没有任何核销权限，显示提示信息 */}
          {!hasPermission('check_subCard') && !hasPermission('check_lounge') ? (
            <View className="no-permission-tip">
              <Text className="tip-text">您暂无核销权限，请联系管理员开通</Text>
            </View>
          ) : (
            <>
              {/* 核销员工次卡 - 需要 check_subCard 权限 */}
              {hasPermission('check_subCard') && (
                <View
                  className="verification-card cyan-card"
                  onClick={handleScanEmployeeCard}
                >
                  <View className="card-circle left-circle"></View>
                  <View className="card-circle right-circle"></View>

                  <View className="card-qr-container">
                    <View className="qr-code-box">
                      <Image
                        className="qr-code-v"
                        src={getImageUrl('cancellation_of_employee_card.svg')}
                        mode="aspectFill"
                      />
                    </View>
                  </View>

                  <View className="card-content">
                    <Text className="card-title">核销员工次卡</Text>
                    <View className="card-action">
                      <View className="action-icon">
                        <Image
                          className="icon"
                          src={getImageUrl('qr_scan_icon.svg')}
                          mode="aspectFit"
                        />
                        <Text className="action-text">扫描员工卡号</Text>
                      </View>
                    </View>
                  </View>
                </View>
              )}

              {/* 核销高舱休息室 - 需要 check_lounge 权限 */}
              {hasPermission('check_lounge') && (
                <View
                  className="verification-card blue-card"
                  onClick={handleInputVerificationCode}
                >
                  <View className="card-circle left-circle"></View>
                  <View className="card-circle right-circle"></View>

                  <View className="card-qr-container">
                    <View className="qr-code-box">
                      <Image
                        className="qr-code-v"
                        src={getImageUrl('write_off_high_cabin.svg')}
                        mode="aspectFill"
                      />
                    </View>
                  </View>

                  <View className="card-content">
                    <Text className="card-title blue">核销高舱休息室</Text>
                    <View className="card-action">
                      <View className="action-icon blue">
                        <Image
                          className="icon"
                          src={getImageUrl('edit_icon.svg')}
                          mode="aspectFit"
                        />
                        <Text className="action-text blue">录入核销码</Text>
                      </View>
                    </View>
                  </View>
                </View>
              )}
            </>
          )}
        </View>
      </View>

      {/* 弹窗 */}
      {dialogType === 'error' ? (
        // 错误提示弹窗
        <Dialog
          visible={dialogVisible}
          title="未能识别到该员工"
          description="请核实员工信息后再次尝试"
          icon={getImageUrl('warning_icon.svg')}
          iconBgColor="#FFF3EB"
          confirmText="确定"
          onConfirm={handleCloseDialog}
          onCancel={handleCloseDialog}
        />
      ) : dialogType === 'no-product' ? (
        // 无可用产品弹窗
        <Dialog
          visible={dialogVisible}
          title="该员工名下无可用产品"
          description="请核实员工信息后再次尝试"
          icon={getImageUrl('warning_icon.svg')}
          iconBgColor="#FFF3EB"
          confirmText="确定"
          onConfirm={handleCloseDialog}
          onCancel={handleCloseDialog}
        />
      ) : dialogType === 'online' ? (
        // 上线弹窗
        <View
          className={`status-dialog ${dialogVisible ? 'visible' : ''}`}
          onClick={() => setDialogVisible(false)}
        >
          <View
            className="status-dialog-content"
            onClick={e => e.stopPropagation()} // 阻止冒泡，防止点击内容区域时关闭弹窗
          >
            <View className="status-dialog-header">
              <View className="status-icon-container">
                <View className="status-icon online-icon">
                  <Image
                    className="icon"
                    src={getImageUrl('alarm-icon.svg')}
                    mode="aspectFit"
                    style={{
                      width: '100%',
                      height: '100%',
                    }}
                  />
                </View>
              </View>
              <View className="status-time">
                <Text className="status-label">上线</Text>
                <Text className="status-time-text">{currentTime}</Text>
              </View>
            </View>

            <View className="status-dialog-footer">
              <Button
                className="online-button"
                onClick={async () => {
                  try {
                    // 设置加载状态
                    setIsStatusLoading(true);

                    // 显示加载提示
                    showToast({
                      title: '处理中...',
                      icon: 'loading',
                      duration: 2000,
                    });

                    // 调用上线接口
                    const response = await request({
                      path: '/api/high-tank/app/user/updateWatch',
                      method: 'GET',
                      query: { status: 1 }, // 1表示上线
                    });

                    // console.log('上线接口调用结果:', response);

                    if (response && response.data) {
                      // 关闭弹窗
                      setDialogVisible(false);
                      // 重新获取在线状态
                      fetchWatchStatus();
                      // 显示成功提示
                      showToast({
                        title: '已上线',
                        icon: 'success',
                        duration: 2000,
                      });
                    } else {
                      // 显示错误提示
                      showToast({
                        title: response?.message || '上线失败',
                        icon: 'error',
                        duration: 2000,
                      });
                    }
                  } catch (error) {
                    // console.error('上线接口调用失败:', error);
                    // 显示错误提示
                    showToast({
                      title: '上线失败',
                      icon: 'error',
                      duration: 2000,
                    });
                  } finally {
                    // 无论成功失败，都取消加载状态
                    setIsStatusLoading(false);
                  }
                }}
              >
                开始上线
              </Button>
              <Text className="status-tip">
                上线后可接收短信推送核销相关信息
              </Text>
            </View>
          </View>
        </View>
      ) : dialogType === 'offline' ? (
        // 下线弹窗
        <View
          className={`status-dialog ${dialogVisible ? 'visible' : ''}`}
          onClick={() => setDialogVisible(false)}
        >
          <View
            className="status-dialog-content"
            onClick={e => e.stopPropagation()} // 阻止冒泡，防止点击内容区域时关闭弹窗
          >
            <View className="status-dialog-header">
              <View className="status-icon-container">
                <View className="status-icon offline-icon">
                  <Image
                    className="icon"
                    src={getImageUrl('walk-fill.svg')}
                    mode="aspectFit"
                    style={{
                      width: '100%',
                      height: '100%',
                    }}
                  />
                </View>
              </View>
              <View className="status-time">
                <Text className="status-label">下线</Text>
                <Text className="status-time-text">{currentTime}</Text>
              </View>
            </View>

            <View className="status-dialog-footer">
              <Button
                className="offline-button"
                onClick={async () => {
                  try {
                    // 设置加载状态
                    setIsStatusLoading(true);

                    // 显示加载提示
                    showToast({
                      title: '处理中...',
                      icon: 'loading',
                      duration: 2000,
                    });

                    // 调用下线接口
                    const response = await request({
                      path: '/api/high-tank/app/user/updateWatch',
                      method: 'GET',
                      query: { status: 2 }, // 2表示下线
                    });

                    // console.log('下线接口调用结果:', response);

                    if (response && response.data) {
                      // 关闭弹窗
                      setDialogVisible(false);
                      // 重新获取在线状态
                      fetchWatchStatus();
                      // 显示成功提示
                      showToast({
                        title: '已下线',
                        icon: 'success',
                        duration: 2000,
                      });
                    } else {
                      // 显示错误提示
                      showToast({
                        title: response?.message || '下线失败',
                        icon: 'error',
                        duration: 2000,
                      });
                    }
                  } catch (error) {
                    // console.error('下线接口调用失败:', error);
                    // 显示错误提示
                    showToast({
                      title: '下线失败',
                      icon: 'error',
                      duration: 2000,
                    });
                  } finally {
                    // 无论成功失败，都取消加载状态
                    setIsStatusLoading(false);
                  }
                }}
              >
                确认下线
              </Button>
              <Text className="status-tip">
                下线后不可继续短信推送核销相关信息
              </Text>
            </View>
          </View>
        </View>
      ) : null}

      <CustomTabBar />
    </View>
  );
}
