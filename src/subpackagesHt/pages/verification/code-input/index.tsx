import { View, Text, Button, Input } from '@tarojs/components';
import { useLoad, navigateTo, showToast } from '@tarojs/taro';
import { useRef, useState } from 'react';
import NavBar from '@ht/components/NavBar';
import request from '@ht/api/apiConfig';
import './index.less';

export default function VerificationCodeInput() {
  // 核销码
  const [verificationCode, setVerificationCode] = useState('');
  const inputRef = useRef<any>(null);

  useLoad(() => {
    // console.log('核销码输入页面加载');
  });

  // 处理输入变化
  const handleInputChange = (e: any) => {
    const value = e.detail.value;
    // 只允许输入数字，最多6位
    if (/^\d*$/.test(value) && value.length <= 6) {
      setVerificationCode(value);
    }
  };

  // 查询订单
  const handleQueryOrder = async () => {
    // 检查是否已输入完整的6位核销码
    if (verificationCode.length !== 6) {
      showToast({
        title: '请输入完整的6位核销码',
        icon: 'none',
        duration: 2000,
      });
      return;
    }

    // 显示加载提示
    showToast({
      title: '正在查询...',
      icon: 'loading',
      duration: 2000,
    });

    // 调用检查券码接口
    const response = await request({
      path: '/api/high-tank/app/productCode/checkPromo',
      method: 'GET',
      query: {
        codeNo: verificationCode,
      },
      isloading: true,
    });

    // console.log('查询结果:', response);

    if (response && response.code === 200) {
      // 根据返回数据长度决定跳转页面
      if (Array.isArray(response.data) && response.data.length > 1) {
        // 多个订单，跳转到订单选择页面
        navigateTo({
          url: `/subpackagesHt/pages/verification/code-input/order-select/index?codeNo=${verificationCode}`,
        });
      } else if (Array.isArray(response.data) && response.data.length === 1) {
        // 只有一个订单，直接跳转到确认页面
        const order = response.data[0];
        navigateTo({
          url: `/subpackagesHt/pages/verification/code-input/confirm/index?productCodeId=${order.productCodeId}`,
        });
      } else if (response.data && response.data.productCodeId) {
        // 单个对象（非数组），直接跳转到确认页面
        navigateTo({
          url: `/subpackagesHt/pages/verification/code-input/confirm/index?productCodeId=${response.data.productCodeId}`,
        });
      } else {
        // 数据格式不符合预期
        showToast({
          title: '找不到对应核销码关联的订单',
          icon: 'none',
          duration: 2000,
        });
      }
    }
  };

  return (
    <View className="verification-code-input-page">
      <NavBar title="高舱休息室核销" />

      <View className="content">
        <View className="input-card">
          <Text className="card-title">请录入核销码</Text>

          <View className="code-input-section">
            <Text className="input-label">核销码</Text>
            <View
              className="code-input-boxes"
              onClick={() => inputRef.current && inputRef.current.focus()}
            >
              {Array(6)
                .fill('')
                .map((_, index) => (
                  <View
                    key={index}
                    className={`code-input-box ${
                      index < verificationCode.length ? 'filled' : ''
                    }`}
                  >
                    <Text className="code-digit">
                      {verificationCode[index] || ''}
                    </Text>
                  </View>
                ))}
              <Input
                className="verification-input-hidden"
                ref={inputRef}
                type="number"
                maxlength={6}
                value={verificationCode}
                onInput={handleInputChange}
                focus
              />
            </View>
          </View>
        </View>

        <Button
          className={`query-button ${
            verificationCode.length === 6 ? 'active' : ''
          }`}
          onClick={handleQueryOrder}
        >
          查询订单
        </Button>
      </View>
    </View>
  );
}
