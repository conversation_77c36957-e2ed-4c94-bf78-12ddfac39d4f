import { View, Text, ScrollView } from '@tarojs/components';
import { useLoad, useRouter, navigateTo, showToast } from '@tarojs/taro';
import { useState } from 'react';
import NavBar from '@ht/components/NavBar';
import request from '@ht/api/apiConfig';
import { getStatusText } from '@ht/utils/orderUtils';
import './index.less';

// 优惠码接口
interface CheckCodePromoVo {
  boardGate?: string;
  codeNo?: string;
  fltDate?: string;
  fltNo?: string;
  orderNo?: string;
  orderStatus?: number;
  passengerName?: string;
  phone?: string;
  price?: string;
  productCodeId?: number;
  productName?: string;
  seatNo?: string;
}

// 订单类型接口
interface OrderItem {
  id: string;
  productName: string;
  price: string;
  status: string;
  passengerName: string;
  phone: string;
  codeNo: string;
  orderNo: string;
  productCodeId: number;
}

export default function OrderSelect() {
  const router = useRouter();
  const { codeNo } = router.params;

  // 订单列表
  const [orderList, setOrderList] = useState<OrderItem[]>([]);

  // 加载中状态
  const [loading, setLoading] = useState(true);

  useLoad(() => {
    // console.log('核销订单选择页面加载');
    // console.log('券码:', codeNo);

    if (codeNo) {
      // 获取订单列表
      fetchOrderList();
    } else {
      showToast({
        title: '券码不能为空',
        icon: 'none',
        duration: 2000,
      });
      setLoading(false);
    }
  });

  // 获取订单列表
  const fetchOrderList = async () => {
    setLoading(true);

    try {
      // 调用检查优惠码接口
      const response = await request({
        path: '/api/high-tank/app/productCode/checkPromo',
        method: 'GET',
        query: {
          codeNo: codeNo,
        },
        isloading: true,
      });

      // console.log('查询结果:', response);

      if (
        response &&
        response.code === 200 &&
        response.data &&
        response.data.length > 0
      ) {
        // 构建订单数据列表
        const orders: OrderItem[] = response.data.map(
          (item: CheckCodePromoVo, index: number) => {
            return {
              id: (index + 1).toString(), // 临时ID
              orderNo: item.orderNo || '',
              productName: item.productName || '',
              price: item.price || '',
              status: getStatusText(item.orderStatus),
              passengerName: item.passengerName || '',
              phone: item.phone || '',
              codeNo: item.codeNo || codeNo || '',
              productCodeId: item.productCodeId || '',
            };
          },
        );

        setOrderList(orders);
      } else {
        // 未找到券码信息
        showToast({
          title: response?.message || '未找到相关券码信息',
          icon: 'none',
          duration: 2000,
        });
        setOrderList([]);
      }
    } catch (error) {
      // console.error('获取订单列表失败:', error);
      showToast({
        title: '获取订单列表失败，请重试',
        icon: 'none',
        duration: 2000,
      });
      setOrderList([]);
    } finally {
      setLoading(false);
    }
  };

  // 处理订单点击
  const handleOrderClick = (order: OrderItem) => {
    // 跳转到核销确认页面
    navigateTo({
      url: `/subpackagesHt/pages/verification/code-input/confirm/index?productCodeId=${order.productCodeId}`,
    });
  };

  // 渲染核销码
  const renderVerificationCode = (code: string) => {
    return (
      <View className="verification-code-boxes">
        {code.split('').map((digit, index) => (
          <View key={index} className="verification-code-box">
            <Text className="verification-code-digit">{digit}</Text>
          </View>
        ))}
      </View>
    );
  };

  return (
    <View className="order-select-page">
      <NavBar title="核销订单选择" showBack />

      <ScrollView className="content" scrollY>
        {loading ? (
          <View className="loading">加载中...</View>
        ) : orderList.length > 0 ? (
          orderList.map(order => (
            <View
              key={order.id}
              className="order-card"
              onClick={() => handleOrderClick(order)}
            >
              <View className="order-header">
                <View className="order-info">
                  <Text className="product-name">{order.productName}</Text>
                  <Text className="product-price">¥ {order.price}</Text>
                </View>
                <Text className="order-status">{order.status}</Text>
              </View>

              <View className="order-row">
                <Text className="row-label">旅客信息</Text>
                <View className="passenger-info">
                  <Text className="passenger-name">{order.passengerName}</Text>
                  <Text className="passenger-phone">{order.phone}</Text>
                </View>
              </View>

              <View className="order-row">
                <Text className="row-label">核销码</Text>
                {renderVerificationCode(order.codeNo)}
              </View>
            </View>
          ))
        ) : (
          <View className="empty-orders">
            <Text className="empty-text">未找到相关订单</Text>
          </View>
        )}
      </ScrollView>
    </View>
  );
}
