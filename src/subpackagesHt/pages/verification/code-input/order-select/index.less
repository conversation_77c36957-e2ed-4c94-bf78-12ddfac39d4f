/* 核销订单选择页面样式 */
.order-select-page {
  min-height: 100vh;
  background-color: #f0f4fa;
  position: relative;
  display: flex;
  flex-direction: column;
}

/* 页面背景渐变 */
.order-select-page::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 240px;
  background: linear-gradient(
    to bottom,
    #cfe1ff 0%,
    #e3eeff 54.08%,
    #f0f4fa 100%
  );
  z-index: -1;
}

/* 内容区域 */
.content {
  flex: 1;
  padding: 16px;
  box-sizing: border-box;
}

/* 订单卡片 */
.order-card {
  background-color: #ffffff;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 12px;
  display: flex;
  flex-direction: column;
  gap: 16px;
  box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.05);
  border: 1px solid #ffffff;
}

/* 订单头部 */
.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.order-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.product-name {
  font-family: 'MiSans', sans-serif;
  font-size: 16px;
  font-weight: 500;
  color: #1d1f20;
  line-height: 1.5em;
}

.product-price {
  font-family: 'MiSans', sans-serif;
  font-size: 14px;
  font-weight: 400;
  color: #4f5170;
  line-height: 1.57em;
}

.order-status {
  font-family: 'MiSans', sans-serif;
  font-size: 12px;
  font-weight: 500;
  color: #0052d9;
  line-height: 1.67em;
}

/* 订单行 */
.order-row {
  display: flex;
  align-items: center;
  gap: 12px;
  width: 100%;
}

.row-label {
  font-family: 'MiSans', sans-serif;
  font-size: 14px;
  font-weight: 400;
  color: #4f5170;
  line-height: 1.57em;
  width: 70px;
}

/* 旅客信息 */
.passenger-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.passenger-name, .passenger-phone {
  font-family: 'MiSans', sans-serif;
  font-size: 14px;
  font-weight: 500;
  color: #1d1f20;
  line-height: 1.57em;
}

/* 核销码 */
.verification-code-boxes {
  display: flex;
  gap: 4px;
}

.verification-code-box {
  width: 30px;
  height: 24px;
  background-color: #f2f4fa;
  border-radius: 6px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.verification-code-digit {
  font-family: 'MiSans', sans-serif;
  font-size: 12px;
  font-weight: 500;
  color: #0052d9;
  line-height: 1.67em;
}

/* 加载中和空状态 */
.loading, .empty-orders {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  width: 100%;
}

.empty-text {
  font-family: 'MiSans', sans-serif;
  font-size: 14px;
  font-weight: 400;
  color: #9aa2ca;
  line-height: 1.57em;
}
