/* 核销确认页面样式 */
.order-confirm-page {
  min-height: 100vh;
  background-color: #f0f4fa;
  position: relative;
  display: flex;
  flex-direction: column;
}

/* 页面背景渐变 */
.order-confirm-page::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 240px;
  background: linear-gradient(
    to bottom,
    #cfe1ff 0%,
    #e3eeff 54.08%,
    #f0f4fa 100%
  );
  z-index: -1;
}

/* 内容区域 */
.content {
  flex: 1;
  padding: 16px;
  box-sizing: border-box;
}

/* 订单卡片 */
.order-card {
  background-color: #ffffff;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
  display: flex;
  flex-direction: column;
  gap: 16px;
  box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.05);
  border: 1px solid #ffffff;
  position: relative;
  overflow: hidden;
}

/* 卡片渐变背景 */
.product-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #0052d9 0%, #ffffff 60%);
  opacity: 0.05;
  z-index: 0;
  pointer-events: none;
}

/* 卡片标题 */
.card-title {
  font-family: 'MiSans', sans-serif;
  font-size: 16px;
  font-weight: 500;
  color: #1d1f20;
  line-height: 1.5em;
  margin-bottom: 4px;
  position: relative;
  z-index: 1;
}

/* 订单头部 */
.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  position: relative;
  z-index: 1;
}

.order-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.product-name {
  font-family: 'MiSans', sans-serif;
  font-size: 16px;
  font-weight: 500;
  color: #1d1f20;
  line-height: 1.5em;
}

.product-price {
  font-family: 'MiSans', sans-serif;
  font-size: 14px;
  font-weight: 400;
  color: #4f5170;
  line-height: 1.57em;
}

.order-status {
  font-family: 'MiSans', sans-serif;
  font-size: 12px;
  font-weight: 500;
  color: #0052d9;
  line-height: 1.67em;
}

/* 订单行 */
.order-row {
  display: flex;
  align-items: center;
  gap: 12px;
  width: 100%;
  position: relative;
  z-index: 1;
}

.row-label {
  font-family: 'MiSans', sans-serif;
  font-size: 14px;
  font-weight: 400;
  color: #4f5170;
  line-height: 1.57em;
  width: 70px;
}

.row-value {
  font-family: 'MiSans', sans-serif;
  font-size: 14px;
  font-weight: 500;
  color: #1d1f20;
  line-height: 1.57em;
}

.order-number {
  font-family: 'MiSans', sans-serif;
  font-size: 14px;
  font-weight: 500;
  color: #1d1f20;
  line-height: 1.57em;
}

/* 旅客信息 */
.passenger-info {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: nowrap;
  white-space: nowrap;
}

.passenger-name,
.passenger-phone {
  font-family: 'MiSans', sans-serif;
  font-size: 14px;
  font-weight: 500;
  color: #1d1f20;
  line-height: 1.57em;
  display: inline-block;
}

/* 核销码 */
.verification-code-boxes {
  display: flex;
  gap: 4px;
}

.verification-code-box {
  width: 30px;
  height: 24px;
  background-color: #f2f4fa;
  border-radius: 6px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.verification-code-digit {
  font-family: 'MiSans', sans-serif;
  font-size: 12px;
  font-weight: 500;
  color: #0052d9;
  line-height: 1.67em;
}

/* 分隔线 */
.divider {
  height: 1px;
  background-color: #efefff;
  width: 100%;
  margin: 4px 0;
  position: relative;
  z-index: 1;
}

/* 产品详情 */
.product-details {
  display: flex;
  justify-content: space-between;
  width: 100%;
  position: relative;
  z-index: 1;
}

.detail-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
  flex: 1;
}

.detail-label {
  font-family: 'MiSans', sans-serif;
  font-size: 14px;
  font-weight: 400;
  color: #4f5170;
  line-height: 1.57em;
  text-align: center;
}

.detail-value {
  font-family: 'MiSans', sans-serif;
  font-size: 14px;
  font-weight: 500;
  color: #1d1f20;
  line-height: 1.57em;
  text-align: center;
}

/* 航班卡片 */
.flight-card {
  background-color: #ffffff;
}

/* 旅客卡片 */
.passenger-card {
  display: flex;
  flex-direction: row;
  align-items: center;
  background-color: #f3f5ff;
  border-radius: 12px;
  padding: 12px 16px;
  margin-bottom: 8px;
  position: relative;
  z-index: 1;
}

/* 确认区域 */
.confirm-section {
  width: 100%;
  background-color: #ffffff;
  border-radius: 12px;
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 16px;
  box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.05);
  margin-bottom: 16px;
}

.confirm-title {
  font-family: 'MiSans', sans-serif;
  font-size: 16px;
  font-weight: 500;
  color: #1d1f20;
  line-height: 1.5em;
}

.confirm-desc {
  font-family: 'MiSans', sans-serif;
  font-size: 14px;
  font-weight: 400;
  color: #737578;
  line-height: 1.57em;
}

/* 底部空间，防止内容被固定按钮遮挡 */
.bottom-space {
  height: 80px;
  width: 100%;
}

/* 底部固定按钮容器 */
.bottom-button-container {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #ffffff;
  padding: 16px;
  box-shadow: 0px -2px 10px rgba(0, 0, 0, 0.05);
  z-index: 100;
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}

.confirm-button {
  width: 100%;
  height: 44px;
  line-height: 44px;
  background-color: #0052d9;
  color: #ffffff;
  font-family: 'MiSans', sans-serif;
  font-size: 16px;
  font-weight: 500;
  border-radius: 12px;
  text-align: center;
  border: none;
  box-shadow: 0px 2px 4px rgba(0, 82, 217, 0.2);
}

.confirm-button.confirming {
  background-color: #a9b2ff;
  color: #ffffff;
}

.status-warning {
  font-family: 'MiSans', sans-serif;
  font-size: 12px;
  font-weight: 400;
  color: #ff4d4f;
  line-height: 1.5em;
  text-align: center;
}
