import { View, Text, But<PERSON>, ScrollView } from '@tarojs/components';
import {
  useLoad,
  useRouter,
  navigateBack,
  navigateTo,
  showToast,
} from '@tarojs/taro';
import { useState } from 'react';
import NavBar from '@ht/components/NavBar';
import request from '@ht/api/apiConfig';
import { getStatusText } from '@ht/utils/orderUtils';
import './index.less';

// 定义订单详情接口
interface OrderDetail {
  boardGate?: string;
  codeNo?: string;
  fltDate?: string;
  fltNo?: string;
  orderNo?: string;
  orderStatus?: number;
  passengerName?: string;
  phone?: string;
  price?: string;
  productCodeId?: number;
  productName?: string;
  seatNo?: string;
  effectTime?: string;
}

export default function OrderConfirm() {
  const router = useRouter();
  const { productCodeId } = router.params;

  // 确认状态
  const [confirming, setConfirming] = useState(false);

  // 订单详情
  const [orderDetail, setOrderDetail] = useState<OrderDetail>({});

  // 加载状态
  const [loading, setLoading] = useState(true);

  // 获取订单详情
  const fetchOrderDetail = async () => {
    if (!productCodeId) {
      showToast({
        title: '缺少必要参数',
        icon: 'none',
        duration: 2000,
      });
      setLoading(false);
      return;
    }

    try {
      setLoading(true);

      // 调用检查优惠码详情接口
      const response = await request({
        path: '/api/high-tank/app/productCode/checkPromoInfo',
        method: 'GET',
        query: {
          productCodeId: productCodeId,
        },
        isloading: true,
      });

      // console.log('订单详情:', response);

      if (response && response.code === 200 && response.data) {
        setOrderDetail(response.data);
      } else {
        showToast({
          title: response?.message || '获取订单详情失败',
          icon: 'none',
          duration: 2000,
        });
      }
    } catch (error) {
      // console.error('获取订单详情失败:', error);
      showToast({
        title: '获取订单详情失败，请重试',
        icon: 'none',
        duration: 2000,
      });
    } finally {
      setLoading(false);
    }
  };

  useLoad(() => {
    // console.log('核销确认页面加载');
    // console.log('订单信息:', router.params);

    // 获取订单详情
    fetchOrderDetail();
  });

  // 渲染核销码
  const renderVerificationCode = (code: string = '') => {
    return (
      <View className="verification-code-boxes">
        {code.split('').map((digit, index) => (
          <View key={index} className="verification-code-box">
            <Text className="verification-code-digit">{digit}</Text>
          </View>
        ))}
      </View>
    );
  };

  // 处理核销确认
  const handleConfirm = async () => {
    if (!orderDetail || !orderDetail.productCodeId) {
      showToast({
        title: '订单信息不完整',
        icon: 'none',
        duration: 2000,
      });
      return;
    }

    setConfirming(true);

    try {
      // 调用核销优惠码接口
      const response = await request({
        path: '/api/high-tank/app/productCode/verifyPromo',
        method: 'POST',
        body: orderDetail,
      });

      // console.log('核销结果:', response);

      if (response && response.code === 200) {
        showToast({
          title: '核销成功',
          icon: 'success',
          duration: 1500,
        });

        // 跳转到核销成功页面
        setTimeout(() => {
          navigateTo({
            url: `/subpackagesHt/pages/verification/code-input/success/index?productCodeId=${orderDetail.productCodeId}`,
          });
        }, 1500);
      } else {
        showToast({
          title: response?.message || '核销失败',
          icon: 'none',
          duration: 2000,
        });
      }
    } catch (error) {
      // console.error('核销失败:', error);
      showToast({
        title: '核销失败，请重试',
        icon: 'none',
        duration: 2000,
      });
    } finally {
      setConfirming(false);
    }
  };

  return (
    <View className="order-confirm-page">
      <NavBar title="核销确认" onBack={() => navigateBack()} />

      <ScrollView className="content" scrollY>
        {loading ? (
          <View className="loading">加载中...</View>
        ) : (
          <>
            {/* 产品信息卡片 */}
            <View className="order-card product-card">
              <Text className="card-title">产品信息</Text>

              <View className="order-header">
                <View className="order-info">
                  <Text className="product-name">
                    {orderDetail.productName || '高舱专享'}
                  </Text>
                </View>
                <Text className="order-status">
                  {getStatusText(orderDetail.orderStatus)}
                </Text>
              </View>

              <View className="order-row">
                <Text className="row-label">核销码</Text>
                {renderVerificationCode(orderDetail.codeNo || '')}
              </View>

              <View className="order-row">
                <Text className="row-label">订单号</Text>
                <Text className="order-number">
                  {orderDetail.orderNo || ''}
                </Text>
              </View>

              <View className="divider"></View>

              <View className="product-details">
                <View className="detail-item">
                  <Text className="detail-label">数量</Text>
                  <Text className="detail-value">1</Text>
                </View>
                <View className="detail-item">
                  <Text className="detail-label">使用时间</Text>
                  <Text className="detail-value">
                    {orderDetail.effectTime}小时/次
                  </Text>
                </View>
                <View className="detail-item">
                  <Text className="detail-label">价格</Text>
                  <Text className="detail-value">
                    ¥ {orderDetail.price || '150'}
                  </Text>
                </View>
              </View>
            </View>

            {/* 航班旅客信息卡片 */}
            {orderDetail.fltNo && (
              <View className="order-card flight-card">
                <Text className="card-title">航班旅客信息</Text>

                <View className="passenger-card">
                  <View className="passenger-info">
                    <Text className="passenger-name">
                      {orderDetail.passengerName || '未知'}
                    </Text>
                    <Text className="passenger-phone">
                      {orderDetail.phone || '未知'}
                    </Text>
                  </View>
                </View>

                <View className="order-row">
                  <Text className="row-label">航班号</Text>
                  <Text className="row-value">{orderDetail.fltNo}</Text>
                </View>

                <View className="order-row">
                  <Text className="row-label">航班日期</Text>
                  <Text className="row-value">{orderDetail.fltDate}</Text>
                </View>

                <View className="order-row">
                  <Text className="row-label">登机口</Text>
                  <Text className="row-value">{orderDetail.boardGate}</Text>
                </View>

                <View className="order-row">
                  <Text className="row-label">座位号</Text>
                  <Text className="row-value">{orderDetail.seatNo}</Text>
                </View>
              </View>
            )}
          </>
        )}
      </ScrollView>

      {/* 固定在底部的确认按钮 */}
      {!loading && (
        <View className="bottom-button-container">
          <Button
            className={`confirm-button ${confirming ? 'confirming' : ''}`}
            onClick={handleConfirm}
            disabled={confirming || orderDetail.orderStatus !== 3}
          >
            {confirming ? '核销中...' : '确认核销'}
          </Button>

          {orderDetail.orderStatus !== 3 && (
            <Text className="status-warning">
              当前订单状态不可核销，只有"待使用"状态的订单才能核销
            </Text>
          )}
        </View>
      )}
    </View>
  );
}
