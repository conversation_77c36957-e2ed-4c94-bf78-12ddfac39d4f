/* 核销码输入页面样式 */
.verification-code-input-page {
  min-height: 100vh;
  background-color: #f0f4fa;
  position: relative;
  display: flex;
  flex-direction: column;
}

/* 页面背景渐变 */
.verification-code-input-page::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 240px;
  background: linear-gradient(
    to bottom,
    #cfe1ff 0%,
    #e3eeff 54.08%,
    #f0f4fa 100%
  );
  z-index: -1;
}

/* 内容区域 */
.content {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40px 16px;
  gap: 40px;
}

/* 输入卡片 */
.input-card {
  width: 100%;
  background-color: #ffffff;
  border-radius: 12px;
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 16px;
  box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.05);
}

.card-title {
  font-family: 'MiSans', sans-serif;
  font-size: 16px;
  font-weight: 500;
  color: #1d1f20;
  line-height: 1.5em;
}

/* 核销码输入区域 */
.code-input-section {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.input-label {
  font-family: 'MiSans', sans-serif;
  font-size: 14px;
  font-weight: 400;
  color: #4f5170;
  line-height: 1.57em;
}

.code-input-boxes {
  display: flex;
  justify-content: space-between;
  gap: 4px;
  width: 100%;
  position: relative;
}

.code-input-box {
  width: 51px;
  height: 60px;
  background-color: #f3f5ff;
  border: 1px solid #a9b2ff;
  border-radius: 6px;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: inset 0 0 6px rgba(255, 255, 255, 0.93);
}

.code-input-box.filled {
  border-color: #a9b2ff;
}

.code-digit {
  font-family: 'MiSans', sans-serif;
  font-size: 20px;
  font-weight: 500;
  color: #0052d9;
  line-height: 1.5em;
}

.verification-input-hidden {
  position: fixed; /* 使用fixed定位 */
  top: -9999px; /* 移出可视区域 */
  left: -9999px;
  opacity: 0;
  z-index: -999; /* 负z-index确保在最底层 */
  color: transparent;
  background: transparent;
  caret-color: transparent;
  text-shadow: 0 0 0 transparent;
  pointer-events: auto; /* 保持可交互性 */
}

/* 查询按钮 */
.query-button {
  width: 100%;
  height: 44px;
  line-height: 44px;
  background-color: #f4f5f5;
  color: #c4c7ca;
  font-family: 'MiSans', sans-serif;
  font-size: 16px;
  font-weight: 400;
  border-radius: 12px;
  text-align: center;
  border: none;
  transition: all 0.3s ease;
}

.query-button.active {
  background-color: #0052d9;
  color: #ffffff;
}
