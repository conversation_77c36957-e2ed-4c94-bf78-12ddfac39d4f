import dayjs from 'dayjs';
import { View, Text, Button, Image } from '@tarojs/components';
import { useLoad, useRouter, reLaunch, showToast } from '@tarojs/taro';
import { useState } from 'react';
import NavBar from '@ht/components/NavBar';
import request from '@ht/api/apiConfig';
import { getImageUrl } from '@/subpackagesHt/utils/image';
import './index.less';

// 定义订单详情接口
interface OrderDetail {
  boardGate?: string;
  codeNo?: string;
  fltDate?: string;
  fltNo?: string;
  orderNo?: string;
  orderStatus?: number;
  passengerName?: string;
  phone?: string;
  price?: string;
  productCodeId?: number;
  productName?: string;
  seatNo?: string;
  effectTime?: string;
}

export default function VerificationSuccess() {
  const router = useRouter();
  const { productCodeId } = router.params;

  // 订单详情
  const [orderDetail, setOrderDetail] = useState<OrderDetail>({});

  // 加载状态
  const [loading, setLoading] = useState(true);

  // 获取订单详情
  const fetchOrderDetail = async () => {
    if (!productCodeId) {
      showToast({
        title: '缺少必要参数',
        icon: 'none',
        duration: 2000,
      });
      setLoading(false);
      return;
    }

    try {
      setLoading(true);

      // 调用检查优惠码详情接口
      const response = await request({
        path: '/api/high-tank/app/productCode/checkPromoInfo',
        method: 'GET',
        query: {
          productCodeId: productCodeId,
        },
        isloading: true,
      });

      // console.log('订单详情:', response);

      if (response && response.code === 200 && response.data) {
        setOrderDetail(response.data);
      } else {
        showToast({
          title: response?.message || '获取订单详情失败',
          icon: 'none',
          duration: 2000,
        });
      }
    } catch (error) {
      // console.error('获取订单详情失败:', error);
      showToast({
        title: '获取订单详情失败，请重试',
        icon: 'none',
        duration: 2000,
      });
    } finally {
      setLoading(false);
    }
  };

  useLoad(() => {
    // console.log('核销成功页面加载');
    // console.log('订单信息:', router.params);

    // 获取订单详情
    fetchOrderDetail();
  });

  // 返回核销首页
  const handleBackToHome = () => {
    // 返回到核销首页
    reLaunch({
      url: '/subpackagesHt/pages/verification/index',
    });
  };

  return (
    <View className="verification-success-page">
      <NavBar
        title="核销成功"
        onBack={() => {
          handleBackToHome();
        }}
      />

      {loading ? (
        <View className="loading">加载中...</View>
      ) : (
        <View className="content">
          <View className="success-icon">
            <Image
              className="icon"
              src={getImageUrl('success_icon.svg')}
              mode="aspectFit"
            />
          </View>

          <View className="success-text">
            <Text className="title">核销成功</Text>
          </View>

          <View className="info-list">
            <View className="info-item">
              <Text className="info-label">使用时间:</Text>
              <Text className="info-value">
                {orderDetail.effectTime}小时/次
              </Text>
            </View>

            <View className="info-item">
              <Text className="info-label">进厅时间:</Text>
              {/* 后端说，这里前端处理 */}
              <Text className="info-value">
                {dayjs().format('YYYY-MM-DD HH:mm:ss')}
              </Text>
            </View>

            <View className="info-item">
              <Text className="info-label">服务到期时间:</Text>
              <Text className="info-value">
                {dayjs()
                  .add(parseInt(orderDetail.effectTime || '0', 10), 'h')
                  .format('YYYY-MM-DD HH:mm:ss')}
              </Text>
            </View>
          </View>

          <Button className="back-button" onClick={handleBackToHome}>
            返回核销首页
          </Button>
        </View>
      )}
    </View>
  );
}
