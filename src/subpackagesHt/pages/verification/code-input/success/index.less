/* 核销成功页面样式 */
.verification-success-page {
  min-height: 100vh;
  background-color: #f0f4fa;
  position: relative;
  display: flex;
  flex-direction: column;
}

/* 页面背景渐变 */
.verification-success-page::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 240px;
  background: linear-gradient(
    to bottom,
    #cfe1ff 0%,
    #e3eeff 54.08%,
    #f0f4fa 100%
  );
  z-index: -1;
}

/* 内容区域 */
.content {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40px 16px;
  gap: 12px;
  box-sizing: border-box;
}

/* 成功图标 */
.success-icon {
  margin-bottom: 12px;
}

.icon {
  width: 64px;
  height: 64px;
}

/* 成功文本 */
.success-text {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  margin-bottom: 40px;
}

.title {
  font-family: 'MiSans', sans-serif;
  font-size: 24px;
  font-weight: 500;
  color: #000000;
  line-height: 1.33em;
}

/* 信息列表 */
.info-list {
  width: 100%;
  max-width: 346px;
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 40px;
}

.info-item {
  background-color: #ffffff;
  border-radius: 12px;
  padding: 12px 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.info-label {
  font-family: 'MiSans', sans-serif;
  font-size: 14px;
  font-weight: 400;
  color: #737578;
  line-height: 1.57em;
}

.info-value {
  font-family: 'MiSans', sans-serif;
  font-size: 14px;
  font-weight: 400;
  color: #1d1f20;
  line-height: 1.57em;
}

/* 返回按钮 */
.back-button {
  width: 313px;
  height: 44px;
  line-height: 44px;
  background-color: #ffffff;
  color: #1d1f20;
  font-family: 'MiSans', sans-serif;
  font-size: 16px;
  font-weight: 500;
  border-radius: 6px;
  text-align: center;
  border: 1px solid #e7e8e9;
  box-shadow: 0px 1px 2px 0px rgba(17, 24, 39, 0.05);
  margin-top: 20px;
}
