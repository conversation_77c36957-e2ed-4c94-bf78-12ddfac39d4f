/* 核销页面样式 */
.verification-page {
  min-height: 100vh;
  background-color: #f0f4fa;
  padding-bottom: 100px; /* 为底部标签栏留出空间 */
  position: relative;
}

/* 页面背景渐变 */
.verification-page::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 240px;
  background: linear-gradient(
    to bottom,
    #cfe1ff 0%,
    #e3eeff 54.08%,
    #f0f4fa 100%
  );
  z-index: -1;
}

/* 核销码显示页面样式 */
.content {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  height: 300px;
  margin-top: 20px;
}

.placeholder-text {
  font-size: 16px;
  color: #999;
}

.verification-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
}

.verification-title {
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

.verification-code {
  font-size: 32px;
  font-weight: 600;
  color: #1663f8;
  letter-spacing: 2px;
}

.verification-tip {
  font-size: 14px;
  color: #666;
  margin-top: 10px;
}

/* 核销选项页面样式 */
.verification-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16px;
  width: 100%;
  box-sizing: border-box;
  margin-top: 24px;
}

.section-header {
  width: 100%;
  max-width: 358px;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.section-title {
  font-size: 16.64px;
  font-weight: 600;
  color: #1d1f20;
  font-family: 'MiSans', sans-serif;
  line-height: 1.5em;
}

/* 上线/下线按钮 */
.status-button {
  padding: 0 16px;
  height: 32px;
  line-height: 32px;
  border-radius: 16px;
  font-family: 'MiSans', sans-serif;
  font-size: 14px;
  font-weight: 500;
  text-align: center;
  border: none;
}

.loading-text {
  color: #999 !important;
}

.online-button {
  background-color: #0052d9;
  color: #ffffff;
}

.offline-button {
  background-color: #15d0af;
  color: #ffffff;
}

.card-group {
  display: flex;
  flex-direction: column;
  gap: 16px;
  width: 100%;
  max-width: 358px;
}

/* 核销卡片 */
.verification-card {
  position: relative;
  height: 93.6px;
  border-radius: 12px;
  overflow: hidden;
  display: flex;
  align-items: center;
  padding: 12px 20px;
  box-sizing: border-box;
  background-color: #ffffff;
  box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.05);
}

/* 青色卡片 - 员工次卡 */
.cyan-card {
  background: #ffffff;
  position: relative;
}

/* 蓝色卡片 - 高舱休息室 */
.blue-card {
  background: #ffffff;
  position: relative;
}

/* 卡片装饰圆圈 */
.card-circle {
  position: absolute;
  width: 40px;
  height: 40px;
  background-color: rgba(255, 255, 255, 0.4);
  border-radius: 50%;
  border: 1px solid;
  border-image: linear-gradient(135deg, #ffffff, rgba(255, 255, 255, 0)) 1;
  filter: blur(2.08px);
}

.left-circle {
  left: -22px;
  top: -24px;
}

.right-circle {
  right: -16px;
  bottom: -16px;
  opacity: 0.2;
}

/* 二维码容器 */
.card-qr-container {
  margin-right: 20px;
}

.qr-code-box {
  position: relative;
  width: 70px;
  height: 70px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.qr-code-v {
  position: absolute;
  width: 100%;
  height: 100%;
  opacity: 1;
}

/* 卡片内容 */
.card-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #1d1f20;
  font-family: 'MiSans', sans-serif;
  line-height: 1.33em;
}

.card-title.blue {
  color: #1d1f20;
}

.card-action {
  display: flex;
  align-items: center;
  gap: 8px;
}

.action-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 6px 12px;
  background-color: #e8f4ff;
  border-radius: 8px;
}

.action-icon.blue {
  background-color: #e8f0ff;
}

.icon {
  width: 16px;
  height: 16px;
}

.action-text {
  font-size: 14px;
  font-weight: 400;
  color: #1663f8;
  font-family: 'MiSans', sans-serif;
  line-height: 1.57em;
  margin-left: 4px;
}

.action-text.blue {
  color: #1663f8;
}

/* 上线/下线弹窗样式 */
.status-dialog {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition:
    opacity 0.3s ease,
    visibility 0.3s ease;
}

.status-dialog.visible {
  opacity: 1;
  visibility: visible;
}

.status-dialog-content {
  width: 329px;
  background-color: #ffffff;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0px 24px 48px -12px rgba(17, 24, 39, 0.25);
}

.status-dialog-header {
  padding: 24px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.status-icon-container {
  display: flex;
  justify-content: center;
  align-items: center;
}

.status-icon {
  width: 40px;
  height: 40px;
  border-radius: 999px;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 8px;
}

.online-icon {
  background-color: #f3f5ff;
}

.offline-icon {
  background-color: #f1fff5;
}

.status-time {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.status-label {
  font-family: 'MiSans', sans-serif;
  font-size: 16px;
  font-weight: 500;
  color: #23242d;
  line-height: 1.5em;
}

.status-time-text {
  font-family: 'MiSans', sans-serif;
  font-size: 20px;
  font-weight: 500;
  color: #23242d;
  line-height: 1.4em;
}

.status-dialog-footer {
  padding: 20px 24px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.offline-button-outside {
  height: 32px;
  line-height: 32px;
  background-color: #9aa2ca;
  color: #efefff;
  font-family: 'MiSans', sans-serif;
  font-size: 14px;
  border-radius: 4px;
  text-align: center;
}

.online-button-outside {
  height: 32px;
  line-height: 32px;
  background-color: #ffffff;
  color: #0052d9;
  font-family: 'MiSans', sans-serif;
  font-size: 14px;
  border-radius: 4px;
  text-align: center;
}

.online-button {
  width: 100%;
  height: 44px;
  line-height: 44px;
  background-color: #0052d9;
  color: #ffffff;
  font-family: 'MiSans', sans-serif;
  font-size: 16px;
  font-weight: 500;
  border-radius: 99px;
  text-align: center;
  border: none;
}

.offline-button {
  width: 100%;
  height: 44px;
  line-height: 44px;
  background-color: #15d0af;
  color: #ffffff;
  font-family: 'MiSans', sans-serif;
  font-size: 16px;
  font-weight: 500;
  border-radius: 99px;
  text-align: center;
  border: none;
}

.status-tip {
  font-family: 'MiSans', sans-serif;
  font-size: 14px;
  font-weight: 400;
  color: #9aa2ca;
  line-height: 1.57em;
  text-align: center;
}
