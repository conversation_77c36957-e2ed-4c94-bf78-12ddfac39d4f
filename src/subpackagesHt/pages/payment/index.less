/* 通用支付页面样式 */
.payment-page {
  min-height: 100vh;
  background-color: #f0f4fa;
  display: flex;
  flex-direction: column;
  position: relative;
}

/* 页面背景渐变 */
.payment-page::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 240px;
  background: linear-gradient(
    to bottom,
    #cfe1ff 0%,
    #e3eeff 54.08%,
    #f0f4fa 100%
  );
  z-index: -1;
}

/* 内容区域 */
.payment-body {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 16px;
  padding-bottom: 80px; /* 为底部按钮留出空间 */
  width: 100%;
  box-sizing: border-box;
}

/* 倒计时模式的内容区域 */
.payment-body-countdown {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 16px;
  padding-bottom: 80px; /* 为底部按钮留出空间 */
  width: 100%;
  box-sizing: border-box;
  align-items: center;
  overflow-y: auto;
}

/* ===== 普通支付模式样式 ===== */

/* 支付金额 - 普通模式 */
.payment-amount-normal {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 24px 0;
}

.amount-label {
  font-size: 16px;
  font-weight: 400;
  color: #737578;
  font-family: 'MiSans', sans-serif;
  line-height: 1.5em;
}

.amount-value {
  display: flex;
  align-items: baseline;
  gap: 4px;
}

.amount-symbol {
  font-size: 20px;
  font-weight: 500;
  color: #1d1f20;
  font-family: 'MiSans', sans-serif;
  line-height: 1.4em;
}

.amount-number {
  font-size: 36px;
  font-weight: 500;
  color: #1d1f20;
  font-family: 'MiSans', sans-serif;
  line-height: 1.22em;
}

/* 支付方式 */
.payment-methods {
  background-color: #ffffff;
  border-radius: 12px;
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.methods-title {
  font-size: 16px;
  font-weight: 500;
  color: #1d1f20;
  font-family: 'MiSans', sans-serif;
  line-height: 1.5em;
}

.methods-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.method-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f4fa;
}

.method-item:last-child {
  border-bottom: none;
}

.method-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.method-icon {
  width: 24px;
  height: 24px;
}

.method-name {
  font-size: 14px;
  font-weight: 400;
  color: #1d1f20;
  font-family: 'MiSans', sans-serif;
  line-height: 1.57em;
}

.method-check {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  border: 1px solid #e7e8e9;
  display: flex;
  justify-content: center;
  align-items: center;
}

.check-icon {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: #1663f8;
}

.method-item.active .method-check {
  border-color: #1663f8;
}

/* 订单信息 */
.order-info {
  background-color: #ffffff;
  border-radius: 12px;
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.info-title {
  font-size: 16px;
  font-weight: 500;
  color: #1d1f20;
  font-family: 'MiSans', sans-serif;
  line-height: 1.5em;
}

.info-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.info-label {
  font-size: 14px;
  font-weight: 400;
  color: #737578;
  font-family: 'MiSans', sans-serif;
  line-height: 1.57em;
}

.info-value {
  font-size: 14px;
  font-weight: 400;
  color: #1d1f20;
  font-family: 'MiSans', sans-serif;
  line-height: 1.57em;
}

/* ===== 倒计时支付模式样式 ===== */

/* 支付信息 */
.payment-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  margin-top: 16px;
  margin-bottom: 8px;
}

.payment-status {
  font-family: 'MiSans', sans-serif;
  font-size: 14px;
  font-weight: 400;
  color: #737578;
  line-height: 1.57em;
}

/* 支付金额 - 倒计时模式 */
.payment-amount-countdown {
  display: flex;
  align-items: baseline;
  gap: 4px;
}

.payment-symbol {
  font-family: 'MiSans', sans-serif;
  font-size: 20px;
  font-weight: 500;
  color: #1d1f20;
  line-height: 1.4em;
}

.payment-value {
  font-family: 'MiSans', sans-serif;
  font-size: 32px;
  font-weight: 500;
  color: #1d1f20;
  line-height: 1.25em;
}

/* 支付卡片 - 倒计时模式 */
.payment-card-countdown {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 8px;
  padding: 16px;
  background-color: #ffffff;
  border-radius: 12px;
  width: 280px;
  box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.05);
}

.airport-name {
  font-family: 'MiSans', sans-serif;
  font-size: 16px;
  font-weight: 500;
  color: #0254a6;
  line-height: 1.5em;
  margin-bottom: 8px;
}

.card-image-container {
  position: relative;
  width: 100%;
  height: 160px;
  border-radius: 8px;
  overflow: hidden;
}

.card-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.card-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    to right,
    rgba(2, 84, 166, 0.8),
    rgba(31, 145, 227, 0.8)
  );
}

.card-tag {
  position: absolute;
  top: 10px;
  left: 10px;
  width: 32px;
  height: 32px;
  background-color: #ffffff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.1);
}

.tag-icon {
  width: 20px;
  height: 20px;
}

/* 支付提示 */
.payment-tip-container {
  margin-top: 16px;
  background-color: #ffffff;
  border-radius: 12px;
  padding: 16px;
  width: 100%;
  text-align: center;
  box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.05);
}

.payment-tip-text {
  font-family: 'MiSans', sans-serif;
  font-size: 14px;
  font-weight: 400;
  color: #737578;
  line-height: 1.57em;
}

/* 倒计时模式的支付方式 */
.payment-methods-countdown {
  width: 100%;
  margin-top: 16px;
  background-color: #ffffff;
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.05);
}

.payment-method-item {
  display: flex;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f4fa;
}

.payment-method-item:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.payment-method-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
}

.payment-method-icon.wechat {
  background-color: #07c160;
}

.payment-method-name {
  flex: 1;
  font-family: 'MiSans', sans-serif;
  font-size: 14px;
  font-weight: 400;
  color: #1d1f20;
  line-height: 1.57em;
}

.payment-method-selected {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  border: 1px solid #1663f8;
  display: flex;
  align-items: center;
  justify-content: center;
}

.selected-circle {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: #1663f8;
}

/* ===== 底部按钮 ===== */

/* 底部支付区域 */
.payment-footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #ffffff;
  padding: 8px 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  z-index: 100;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.04);
}

/* 底部安全区域 */
.payment-footer::after {
  content: '';
  height: env(safe-area-inset-bottom);
  width: 100%;
  position: absolute;
  bottom: -1px;
  left: 0;
  background-color: #ffffff;
  z-index: -1;
}

/* 底部价格区域 */
.footer-price {
  display: flex;
  flex-direction: column;
}

.footer-price-label {
  font-size: 12px;
  font-weight: 400;
  color: #737578;
  font-family: 'MiSans', sans-serif;
  line-height: 1.67em;
}

.footer-price-container {
  display: flex;
  align-items: baseline;
}

.footer-price-symbol {
  font-size: 12px;
  font-weight: 500;
  color: #f84f2a;
  font-family: 'MiSans', sans-serif;
  line-height: 1.67em;
  margin-right: 2px;
}

.footer-price-value {
  font-size: 20px;
  font-weight: 500;
  color: #f84f2a;
  font-family: 'MiSans', sans-serif;
  line-height: 1.4em;
}

/* 支付按钮 */
.payment-button {
  height: 44px;
  background-color: #1663f8;
  border-radius: 99px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  padding: 0 32px;
  box-shadow: 0px 2px 4px rgba(22, 99, 248, 0.2);
}

.button-text {
  font-family: 'MiSans', sans-serif;
  font-size: 16px;
  font-weight: 600;
  color: #ffffff;
  line-height: 1.5em;
}

/* 产品卡片样式 */
.payment-card-product {
  background-color: white;
  padding: 16px;
  border-radius: 12px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.05);
}

.product-name {
  font-family: 'MiSans', sans-serif;
  font-size: 16px;
  font-weight: 500;
  color: #4f5170;
  line-height: 1.5em;
}

.product-image {
  width: 100%;
  height: 120px;
  border-radius: 8px;
  background-color: #f2f4fa;
}

/* 支付二维码相关样式 */
.payment-qrcode-container {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 16px;
  background-color: #ffffff;
  border-radius: 8px;
}

.payment-qrcode {
  width: 200px;
  height: 200px;
  object-fit: contain;
}

.payment-qrcode-loading {
  width: 200px;
  height: 200px;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f5f5f5;
  border-radius: 8px;
  border: 1px dashed #e0e0e0;
}
