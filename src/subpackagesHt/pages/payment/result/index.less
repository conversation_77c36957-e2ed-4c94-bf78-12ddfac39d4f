/* 支付结果页面样式 */
.payment-result {
  min-height: 100vh;
  background-color: #f0f4fa;
  display: flex;
  flex-direction: column;
  position: relative;
}

/* 加载状态 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  width: 100%;
}

.loading-text {
  font-size: 16px;
  color: #666;
  font-family: 'MiSans', sans-serif;
}

/* 页面背景渐变 */
.payment-result::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 240px;
  background: linear-gradient(
    to bottom,
    #cfe1ff 0%,
    #e3eeff 54.08%,
    #f0f4fa 100%
  );
  z-index: -1;
}

/* 成功提示区域 */
.success-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  margin-top: 40px;
  margin-bottom: 40px;
}

.success-icon {
  width: 60px;
  height: 60px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.check-icon {
  width: 100%;
  height: 100%;
}

.success-text {
  font-size: 24px;
  font-weight: 500;
  color: #000000;
  font-family: 'MiSans', sans-serif;
  line-height: 1.33em;
}

/* 失败提示区域 */
.failed-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  margin-top: 40px;
  margin-bottom: 40px;
}

.failed-icon {
  width: 60px;
  height: 60px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.error-icon {
  width: 100%;
  height: 100%;
}

.failed-text {
  font-size: 24px;
  font-weight: 500;
  color: #000000;
  font-family: 'MiSans', sans-serif;
  line-height: 1.33em;
}

/* 订单信息区域 */
.order-info {
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 0 22px;
  width: 100%;
  box-sizing: border-box;
}

.info-item {
  background-color: #ffffff;
  border-radius: 12px;
  padding: 8px 12px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.info-label {
  font-size: 14px;
  font-weight: 400;
  color: #737578;
  font-family: 'MiSans', sans-serif;
  line-height: 1.57em;
}

.info-value {
  font-size: 14px;
  font-weight: 400;
  color: #1d1f20;
  font-family: 'MiSans', sans-serif;
  line-height: 1.57em;
}

.price-container {
  display: flex;
  align-items: baseline;
  gap: 4px;
}

.price-symbol {
  font-size: 14px;
  font-weight: 400;
  color: #1d1f20;
  font-family: 'MiSans', sans-serif;
  line-height: 1.57em;
}

.price-value {
  font-size: 14px;
  font-weight: 400;
  color: #1d1f20;
  font-family: 'MiSans', sans-serif;
  line-height: 1.57em;
}

/* 核销码区域 */
.verification-code-container {
  background-color: #ffffff;
  border-radius: 12px;
  padding: 8px 12px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.code-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.copy-button {
  border: 1px solid #0052d9;
  border-radius: 2px;
  padding: 0 6px;
  cursor: pointer;
}

.copy-text {
  font-size: 12px;
  font-weight: 400;
  color: #0052d9;
  font-family: 'MiSans', sans-serif;
  line-height: 1.67em;
}

/* 多个核销码的样式 */
.code-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 12px;
}

.code-divider {
  height: 1px;
  background-color: #e7e8e9;
  margin: 8px 0;
}

.code-label {
  font-size: 14px;
  font-weight: 500;
  color: #737578;
  font-family: 'MiSans', sans-serif;
}

.code-digits {
  display: flex;
  justify-content: space-between;
  gap: 4px;
}

.code-digit {
  flex: 1;
  height: 60px;
  background-color: #f3f5ff;
  border: 1px solid #a9b2ff;
  border-radius: 6px;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: inset 0px 0px 12px rgba(255, 255, 255, 0.93);
}

.digit-text {
  font-size: 14px;
  font-weight: 400;
  color: #0052d9;
  font-family: 'MiSans', sans-serif;
  line-height: 1.57em;
}

/* 操作按钮区域 */
.action-buttons {
  display: flex;
  justify-content: space-between;
  gap: 12px;
  margin-top: 40px;
  padding: 0 22px;
  width: 100%;
  box-sizing: border-box;
}

.action-button {
  flex: 1;
  height: 44px;
  line-height: 44px;
  background-color: #ffffff;
  color: #1d1f20;
  font-size: 16px;
  font-weight: 500;
  border-radius: 6px;
  text-align: center;
  border: 1px solid #e7e8e9;
  box-shadow: 0px 1px 2px rgba(17, 24, 39, 0.05);
}

.action-button.full-width {
  width: 100%;
}

/* 支付失败按钮样式 */
.failed-actions {
  flex-direction: column;
  gap: 18px;
}

.primary-button {
  background-color: #f84f2a;
  color: #ffffff;
  font-weight: 600;
  border: none;
}

.secondary-button {
  background-color: #ffffff;
  color: #23242d;
  font-weight: 500;
  border: 1px solid #e7e8e9;
}

/* 核销按钮区域 */
.verification-button-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
  padding: 0 22px;
  width: 100%;
  box-sizing: border-box;
}

.verification-button {
  width: 100%;
  height: 44px;
  line-height: 44px;
  background-color: #1663f8;
  color: #ffffff;
  font-size: 16px;
  font-weight: 500;
  border-radius: 22px;
  text-align: center;
  border: none;
}

.verification-button[disabled] {
  background-color: #cccccc;
  color: #999999;
}
