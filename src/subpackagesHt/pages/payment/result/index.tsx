import { View, Text, Image, Button } from '@tarojs/components';
import {
  useLoad,
  useRouter,
  navigateTo,
  showToast,
  redirectTo,
  reLaunch,
} from '@tarojs/taro';
import { useState } from 'react';
import NavBar from '@ht/components/NavBar';
import request from '@ht/api/apiConfig';
import { getImageUrl } from '@ht/utils/image';
import './index.less';

/**
 * 订单状态常量
 * 1-待支付，2-已取消，3-待使用（已支付），4-退款中，5-已退款，6-使用中，7-已使用
 */
const ORDER_STATUS = {
  PENDING_PAYMENT: 1, // 待支付
  CANCELLED: 2, // 已取消
  PENDING_USE: 3, // 待使用（已支付）
  REFUNDING: 4, // 退款中
  REFUNDED: 5, // 已退款
  IN_USE: 6, // 使用中
  USED: 7, // 已使用
};

/**
 * 判断订单是否支付成功
 * @param status 订单状态码
 * @returns 是否支付成功
 */
const isPaymentSuccess = (status: number): boolean => {
  return [
    ORDER_STATUS.PENDING_USE,
    ORDER_STATUS.IN_USE,
    ORDER_STATUS.USED,
  ].includes(status);
};

/**
 * 判断订单是否支付失败
 * @param status 订单状态码
 * @returns 是否支付失败
 */
const isPaymentFailed = (status: number): boolean => {
  return [
    ORDER_STATUS.PENDING_PAYMENT,
    ORDER_STATUS.CANCELLED,
    ORDER_STATUS.REFUNDING,
    ORDER_STATUS.REFUNDED,
  ].includes(status);
};

/**
 * 支付结果页面
 */
export default function PaymentResult() {
  const router = useRouter();
  const { orderNo } = router.params;

  // 订单信息
  const [orderInfo, setOrderInfo] = useState({
    amount: '',
    orderNumber: orderNo || '',
    codeNoList: [] as string[], // 核销码列表，是一个数组
    status: 0, // 订单状态
    productType: 0, // 产品类型
  });

  // 加载状态
  const [loading, setLoading] = useState(false);

  // 获取订单详情
  const fetchOrderDetail = async () => {
    if (!orderNo) {
      showToast({
        title: '订单号不存在',
        icon: 'none',
        duration: 2000,
      });
      return;
    }

    try {
      setLoading(true);
      const res = await request({
        path: `/api/high-tank/app/order/info`,
        query: { orderNo },
        method: 'GET',
        isloading: true,
      });

      // console.log('获取订单详情成功:', res);

      if (res && res.data) {
        // 更新订单信息
        setOrderInfo({
          amount: res.data.price || '',
          orderNumber: res.data.orderNo || orderNo,
          codeNoList: res.data.codeNoList || [], // 使用codeNoList字段
          status: res.data.status || 0,
          productType: res.data.productType || 0, // 添加productType字段
        });
      } else {
        showToast({
          title: '获取订单详情失败',
          icon: 'none',
          duration: 2000,
        });
      }
    } catch (error) {
      // console.error('获取订单详情出错:', error);
      showToast({
        title: '获取订单详情失败',
        icon: 'none',
        duration: 2000,
      });
    } finally {
      setLoading(false);
    }
  };

  useLoad(() => {
    // console.log('支付结果页面加载, orderNo:', orderNo);
    // 页面加载时获取订单详情
    fetchOrderDetail();
  });

  // 查看订单
  const handleViewOrder = () => {
    // console.log('查看订单');
    navigateTo({
      url: `/subpackagesHt/pages/order/detail/index?orderNo=${orderNo}`,
    });
  };

  // 获取页面标题
  const getPageTitle = () => {
    if (loading) return '支付结果';

    if (isPaymentSuccess(orderInfo.status)) {
      return '支付成功';
    } else if (isPaymentFailed(orderInfo.status)) {
      return '支付失败';
    } else {
      return '订单详情';
    }
  };

  // 重新支付
  const handleRepay = () => {
    if (!orderNo) {
      showToast({
        title: '订单号不存在',
        icon: 'none',
        duration: 2000,
      });
      return;
    }

    // 跳转到支付页面
    redirectTo({
      url: `/subpackagesHt/pages/payment/index?orderNo=${orderNo}`,
    });
  };

  return (
    <View className="payment-result">
      <NavBar
        title={getPageTitle()}
        showBack
        onBack={() => {
          reLaunch({
            url: '/subpackagesHt/pages/home/<USER>',
          });
        }}
      />

      {loading ? (
        <View className="loading-container">
          <Text className="loading-text">加载中...</Text>
        </View>
      ) : (
        <>
          {/* 状态内容 - 根据订单状态显示不同的内容 */}
          {isPaymentSuccess(orderInfo.status) ? (
            // 支付成功
            <View className="success-content">
              <View className="success-icon">
                <Image
                  className="check-icon"
                  src={getImageUrl('check_circle_icon.svg')}
                  mode="aspectFit"
                />
              </View>
              <Text className="success-text">支付成功</Text>
            </View>
          ) : (
            // 支付失败
            <View className="failed-content">
              <View className="failed-icon">
                <Image
                  className="error-icon"
                  src={getImageUrl('error_warning_icon.svg')}
                  mode="aspectFit"
                />
              </View>
              <Text className="failed-text">支付失败</Text>
            </View>
          )}

          {/* 订单信息 - 所有状态都显示 */}
          <View className="order-info">
            <View className="info-item">
              <Text className="info-label">支付金额:</Text>
              <View className="price-container">
                <Text className="price-symbol">¥</Text>
                <Text className="price-value">{orderInfo.amount}</Text>
              </View>
            </View>

            <View className="info-item">
              <Text className="info-label">订单号:</Text>
              <Text className="info-value">{orderInfo.orderNumber}</Text>
            </View>

            {/* 核销码 - 只有支付成功、有核销码且productType等于2的订单才显示 */}
            {isPaymentSuccess(orderInfo.status) &&
              orderInfo.productType === 2 &&
              orderInfo.codeNoList &&
              orderInfo.codeNoList.length > 0 && (
                <View className="verification-code-container">
                  {/* 渲染多个核销码 */}
                  {orderInfo.codeNoList.map((code, codeIndex) => (
                    <View key={codeIndex} className="code-item">
                      {codeIndex > 0 && <View className="code-divider"></View>}
                      <Text className="code-label">核销码:</Text>
                      <View className="code-digits">
                        {code.split('').map((digit: string, index: number) => (
                          <View key={index} className="code-digit">
                            <Text className="digit-text">{digit}</Text>
                          </View>
                        ))}
                      </View>
                    </View>
                  ))}
                </View>
              )}
          </View>

          {/* 操作按钮 - 根据订单状态显示不同的按钮 */}
          {isPaymentSuccess(orderInfo.status) ? (
            // 支付成功 - 显示查看订单按钮
            <View className="action-buttons">
              <Button
                className="action-button full-width"
                onClick={handleViewOrder}
              >
                查看订单
              </Button>
            </View>
          ) : (
            // 支付失败 - 显示重新支付和查看订单按钮
            <View className="action-buttons failed-actions">
              <Button
                className="action-button primary-button"
                onClick={handleRepay}
              >
                重新支付
              </Button>
              <Button
                className="action-button secondary-button"
                onClick={handleViewOrder}
              >
                查看订单
              </Button>
            </View>
          )}
        </>
      )}
    </View>
  );
}
