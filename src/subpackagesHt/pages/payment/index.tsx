import { QRCode } from 'taro-code';
import { View, Text } from '@tarojs/components';
import Taro, {
  useLoad,
  useRouter,
  navigateTo,
  useUnload,
  showToast,
} from '@tarojs/taro';
import { useState, useEffect, useRef } from 'react';
import request from '@ht/api/apiConfig';
import NavBar from '../../components/NavBar';
import './index.less';

/**
 * 通用支付页面
 * 支持两种支付模式：
 * 1. 普通支付模式 - 显示支付金额、支付方式和订单信息
 * 2. 倒计时支付模式 - 显示等待支付状态、支付金额、产品卡片和倒计时
 */
// 订单状态类型
interface OrderStatusResponse {
  code: number;
  data: number; // 订单状态：1-待支付，2-已取消，3-待使用（已支付），4-退款中，5-已退款，6-使用中，7-已使用
  message: string;
}

// 支付路径响应类型
interface PayPathResponse {
  code: number;
  data: string; // 支付路径
  message: string;
}

// 订单详情响应类型
interface OrderInfoResponse {
  code: number;
  data: {
    orderNo: string;
    price: string;
    productName: string;
    status: number;
    // 其他订单详情字段
  };
  message: string;
}

export default function PaymentPage() {
  const router = useRouter();
  const { orderNo } = router.params;

  // 支付相关状态
  const [orderInfo, setOrderInfo] = useState({
    price: '-',
    productName: '',
    paymentMode: 'countdown', // 默认使用倒计时模式
  });

  // 支付路径状态
  const [payPath, setPayPath] = useState('');

  // 轮询相关状态
  const pollingTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // 获取订单详情
  const fetchOrderDetail = async (orderNumber: string) => {
    try {
      // console.log('获取订单详情:', orderNumber);

      // 调用订单详情接口
      const res = await request<OrderInfoResponse>({
        path: `/api/high-tank/app/order/info?orderNo=${orderNumber}`,
        method: 'GET',
        isloading: true,
      });

      // console.log('订单详情查询结果:', res);

      if (res.code === 200 && res.data) {
        // 设置订单信息
        setOrderInfo({
          price: res.data.price || '-',
          productName: res.data.productName || '',
          paymentMode: 'countdown',
        });
      } else {
        showToast({
          title: res.message || '获取订单详情失败',
          icon: 'none',
          duration: 2000,
        });
      }
    } catch (error) {
      // console.error('获取订单详情失败:', error);
      showToast({
        title: '获取订单详情失败，请稍后重试',
        icon: 'none',
        duration: 2000,
      });
    }
  };

  // 查询支付路径
  const queryPayPath = async (orderNumber: string) => {
    try {
      // console.log('查询支付路径:', orderNumber);

      const res = await request<PayPathResponse>({
        path: `/api/high-tank/app/order/queryPayPath?orderNo=${orderNumber}`,
        method: 'GET',
        isloading: true,
      });

      // console.log('支付路径查询结果:', res);

      if (res.code === 200 && res.data) {
        const payPathUrl = res.data;

        // 如果返回了支付路径，保存到状态中
        if (payPathUrl) {
          // console.log('获取到支付路径:', payPathUrl);

          // 保存支付路径到状态中
          setPayPath(payPathUrl);
        } else {
          showToast({
            title: '获取支付链接失败',
            icon: 'none',
            duration: 2000,
          });
        }
      } else {
        showToast({
          title: res.message || '获取支付链接失败',
          icon: 'none',
          duration: 2000,
        });
      }
    } catch (error) {
      // console.error('查询支付路径失败:', error);
      showToast({
        title: '获取支付链接失败，请稍后重试',
        icon: 'none',
        duration: 2000,
      });
    }
  };

  // 轮询订单状态
  const pollOrderStatus = async (orderNumber: string) => {
    try {
      // console.log('轮询订单状态:', orderNumber);

      const res = await request<OrderStatusResponse>({
        path: `/api/high-tank/app/order/status?orderNo=${orderNumber}`,
        method: 'GET',
      });

      // console.log('订单状态查询结果:', res);

      if (res.code === 200) {
        // res.data 直接就是状态码
        const status = res.data;

        // 订单状态：1-待支付，2-已取消，3-待使用（已支付），4-退款中，5-已退款，6-使用中，7-已使用
        if (status === 3 || status === 6 || status === 7) {
          // 订单已支付（待使用、使用中或已使用），跳转到支付成功页面
          stopPollingOrderStatus();
          navigateTo({
            url: `/subpackagesHt/pages/payment/result/index?orderNo=${orderNumber}`,
          });
          return;
        } else if (status === 2 || status === 4 || status === 5) {
          // 订单已取消、退款中或已退款，跳转到支付失败页面
          stopPollingOrderStatus();

          showToast({
            title: '支付已取消',
            icon: 'none',
            duration: 2000,
          });

          setTimeout(() => {
            Taro.reLaunch({
              url: '/subpackagesHt/pages/home/<USER>',
            });
          }, 2000);

          // navigateTo({
          //   url: `/subpackagesHt/pages/payment/result/index?orderNo=${orderNumber}`,
          // });
          return;
        }
      }
    } catch (error) {
      // console.error('轮询订单状态失败:', error);
    }

    // 无论成功还是失败，只要订单状态未变更，就继续轮询
    // 检查组件是否仍在轮询中（通过引用而不是状态检查）
    if (pollingTimeoutRef.current !== null) {
      pollingTimeoutRef.current = setTimeout(() => {
        pollOrderStatus(orderNumber);
      }, 3000); // 每秒轮询一次
    }
  };

  // 开始轮询
  const startPollingOrderStatus = (orderNumber: string) => {
    // 防止重复启动轮询
    if (pollingTimeoutRef.current !== null) return;

    // 初始化轮询引用（设置为空的超时对象，以便轮询逻辑能够继续）
    pollingTimeoutRef.current = setTimeout(() => {}, 0);

    // 开始轮询
    pollOrderStatus(orderNumber);
  };

  // 停止轮询
  const stopPollingOrderStatus = () => {
    // 清除当前的超时引用
    if (pollingTimeoutRef.current) {
      clearTimeout(pollingTimeoutRef.current);
      pollingTimeoutRef.current = null;
    }
  };

  // 使用useLoad钩子处理页面加载逻辑
  useLoad(() => {
    // console.log('支付页面加载（useLoad），订单号:', orderNo);

    if (orderNo) {
      // 获取订单详情
      fetchOrderDetail(orderNo);

      // 查询支付路径
      queryPayPath(orderNo);

      // 开始轮询订单状态
      startPollingOrderStatus(orderNo);
    }
  });

  // 使用useUnload钩子确保在页面卸载时停止轮询
  useUnload(() => {
    // console.log('支付页面卸载（useUnload），停止轮询');
    stopPollingOrderStatus();
  });

  // 添加useEffect用于处理orderNo变化和组件卸载
  useEffect(() => {
    // 组件卸载时，确保轮询已停止
    return () => {
      // console.log('支付页面卸载（useEffect清理），停止轮询');
      stopPollingOrderStatus();
    };
  }, []); // 空依赖数组，只在组件卸载时执行清理函数

  return (
    <View className="payment-page">
      {/* 顶部导航栏 */}
      <NavBar
        title="产品支付"
        showBack
        onBack={() => {
          // 在导航前停止轮询
          stopPollingOrderStatus();
          // 使用redirectTo而不是navigateTo，确保当前页面被销毁
          Taro.reLaunch({
            url: '/subpackagesHt/pages/home/<USER>',
          });
        }}
      />

      {/* 内容区域 */}
      <View
        className={
          orderInfo.paymentMode === 'countdown'
            ? 'payment-body-countdown'
            : 'payment-body'
        }
      >
        {/* 支付金额 */}
        <View className="payment-info">
          <Text className="payment-status">等待支付</Text>
          <View className="payment-amount-countdown">
            <Text className="payment-symbol">¥</Text>
            <Text className="payment-value">{orderInfo.price}</Text>
          </View>
        </View>

        {/* 产品卡片 */}
        <View className="payment-card-product">
          <Text className="product-name">{orderInfo.productName}</Text>

          {/* 支付二维码 */}
          <View className="payment-qrcode-container">
            {payPath ? (
              <View className="payment-qrcode">
                <QRCode
                  text={payPath}
                  // size={300}
                  // scale={4}
                  errorCorrectLevel="M"
                  typeNumber={2}
                  style={{ width: '100%', height: '100%' }}
                />
              </View>
            ) : (
              <View className="payment-qrcode-loading">
                <Text>正在加载支付二维码...</Text>
              </View>
            )}
          </View>
        </View>
      </View>
    </View>
  );
}
