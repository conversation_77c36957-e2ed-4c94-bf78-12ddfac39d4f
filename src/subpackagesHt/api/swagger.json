{"openapi": "3.0.3", "info": {"title": "high-tank-services Api Doc", "contact": {"name": "swcares team", "email": "<EMAIL>"}, "version": "Application Version：0.1.0-PAIP-SNAPSHOT-@timestamp@"}, "servers": [{"url": "http://paip-dev.iprd.sw:80", "description": "Inferred Url"}], "tags": [{"name": "UC数据通知", "description": "Uc Notice Controller"}, {"name": "filter-error-controller", "description": "Filter Error Controller"}, {"name": "sso-approval-endpoint", "description": "Sso Approval Endpoint"}, {"name": "产品接口", "description": "Product Controller"}, {"name": "小程序-产品接口", "description": "App Product Controller"}, {"name": "小程序-券码接口", "description": "App Product Code Controller"}, {"name": "小程序-用户信息", "description": "App User Controller"}, {"name": "小程序-订单接口", "description": "App Order Controller"}, {"name": "支付回调", "description": "Notify HT Controller"}, {"name": "数据权限", "description": "Data Permissions Controller"}, {"name": "订单接口", "description": "Order Controller"}, {"name": "退款申请单", "description": "Refund Order Controller"}, {"name": "销售面板接口", "description": "<PERSON><PERSON>"}], "paths": {"/api/high-tank/app/order/add": {"post": {"tags": ["小程序-订单接口"], "summary": "新增次卡订单", "operationId": "addUsingPOST_1", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderAddVo"}}}}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfstring"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/api/high-tank/app/order/add/passenger": {"post": {"tags": ["小程序-订单接口"], "summary": "新增高舱订单", "operationId": "addUsingPOST", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderPassengerAddVo"}}}}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfstring"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/api/high-tank/app/order/cancel": {"get": {"tags": ["小程序-订单接口"], "summary": "订单取消", "operationId": "cancalUsingGET", "parameters": [{"name": "orderNo", "in": "query", "description": "订单号", "required": true, "style": "form", "allowReserved": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfboolean"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/api/high-tank/app/order/info": {"get": {"tags": ["小程序-订单接口"], "summary": "详情", "operationId": "infoUsingGET", "parameters": [{"name": "orderNo", "in": "query", "description": "订单号", "required": true, "style": "form", "allowReserved": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfOrderInfoVo"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/api/high-tank/app/order/list": {"post": {"tags": ["小程序-订单接口"], "summary": "订单列表", "operationId": "listUsingPOST", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderQueryVo"}}}}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfListOfOrderListVO"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/api/high-tank/app/order/list/my": {"post": {"tags": ["小程序-订单接口"], "summary": "订单列表", "operationId": "myListUsingPOST", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderQueryVo"}}}}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfListOfOrderListVO"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/api/high-tank/app/order/offlinePay": {"get": {"tags": ["小程序-订单接口"], "summary": "线下退款", "operationId": "offlinePayUsingGET", "parameters": [{"name": "orderNo", "in": "query", "description": "orderNo", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResult"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/api/high-tank/app/order/queryPayPath": {"get": {"tags": ["小程序-订单接口"], "summary": "查询支付地址", "operationId": "queryPayPathUsingGET", "parameters": [{"name": "orderNo", "in": "query", "description": "订单号", "required": true, "style": "form", "allowReserved": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfstring"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/api/high-tank/app/order/refund_apply": {"post": {"tags": ["小程序-订单接口"], "summary": "订单退款申请", "operationId": "refundApplyUsingPOST", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RefundApplyDto"}}}}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/api/high-tank/app/order/status": {"get": {"tags": ["小程序-订单接口"], "summary": "订单状态", "operationId": "queryStatusUsingGET", "parameters": [{"name": "orderNo", "in": "query", "description": "订单号", "required": true, "style": "form", "allowReserved": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfint"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/api/high-tank/app/productCode/checkCoupon": {"get": {"tags": ["小程序-券码接口"], "summary": "检查券码", "operationId": "checkCodeCouponUsingGET", "parameters": [{"name": "codeNo", "in": "query", "description": "券码", "required": true, "style": "form", "allowReserved": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfCheckCodeCouponVo"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/api/high-tank/app/productCode/checkPromo": {"get": {"tags": ["小程序-券码接口"], "summary": "检查优惠码列表", "operationId": "checkCodePromoUsingGET", "parameters": [{"name": "codeNo", "in": "query", "description": "券码", "required": true, "style": "form", "allowReserved": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfListOfCheckCodePromoVo"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/api/high-tank/app/productCode/checkPromoInfo": {"get": {"tags": ["小程序-券码接口"], "summary": "检查核优惠码详情", "operationId": "checkPromoInfoUsingGET", "parameters": [{"name": "productCodeId", "in": "query", "description": "券码表主键", "required": true, "style": "form", "allowReserved": false, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfCheckCodePromoVo"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/api/high-tank/app/productCode/list/my": {"get": {"tags": ["小程序-券码接口"], "summary": "我的券码列表", "operationId": "myListUsingGET", "parameters": [{"name": "status", "in": "query", "description": "状态，1：正常；2：已使用；3：已失效", "required": true, "style": "form", "allowReserved": false, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfListOfProductCodeVo"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/api/high-tank/app/productCode/productOrderInfo": {"get": {"tags": ["小程序-券码接口"], "summary": "次卡核销员工可用产品信息", "operationId": "productOrderInfoUsingGET", "parameters": [{"name": "userNo", "in": "query", "description": "工号", "required": true, "style": "form", "allowReserved": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfProductOrderInfoVo"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/api/high-tank/app/productCode/verifyCoupon": {"post": {"tags": ["小程序-券码接口"], "summary": "核销券码", "operationId": "verifyCouponUsingPOST", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/VerifyCouponVo"}}}}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfboolean"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/api/high-tank/app/productCode/verifyPromo": {"post": {"tags": ["小程序-券码接口"], "summary": "核销优惠码", "operationId": "verifyPromoUsingPOST", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CheckCodePromoVo"}}}}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfboolean"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/api/high-tank/app/product/info": {"get": {"tags": ["小程序-产品接口"], "summary": "详情", "operationId": "infoUsingGET_1", "parameters": [{"name": "id", "in": "query", "description": "id", "required": true, "style": "form", "allowReserved": false, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfHtProduct对象"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/api/high-tank/app/product/list": {"get": {"tags": ["小程序-产品接口"], "summary": "产品列表", "operationId": "listUsingGET", "parameters": [{"name": "productType", "in": "query", "description": "产品类型，1：员工次卡；2：旅客高舱", "required": true, "style": "form", "allowReserved": false, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfListOfHtProduct对象"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/api/high-tank/app/user/employee_card": {"get": {"tags": ["小程序-用户信息"], "summary": "我的员工卡or个人信息", "operationId": "employeeCardUsingGET", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfEmployeeCardVo"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/api/high-tank/app/user/queryProductUserInfo": {"get": {"tags": ["小程序-用户信息"], "summary": "查询产品使用记录本人", "operationId": "queryProductUserInfoUsingGET", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfListOfProductUseInfoVo"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/api/high-tank/app/user/queryProductUserInfoKin": {"get": {"tags": ["小程序-用户信息"], "summary": "查询产品使用记录亲属", "operationId": "queryProductUserInfoKinUsingGET", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfListOfProductUseInfoVo"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/api/high-tank/app/user/queryWatchStatus": {"get": {"tags": ["小程序-用户信息"], "summary": "查询值班状态", "operationId": "queryWatchStatusUsingGET", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfint"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/api/high-tank/app/user/updateWatch": {"get": {"tags": ["小程序-用户信息"], "summary": "更新值班", "operationId": "updateWatchUsingGET", "parameters": [{"name": "status", "in": "query", "description": "状态，1：上线；2：下线", "required": true, "style": "form", "allowReserved": false, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfboolean"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/api/high-tank/app/user/update_userInfo": {"post": {"tags": ["小程序-用户信息"], "summary": "补全销售用户信息", "operationId": "updateUserInfoUsingPOST", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ReplenishUserInfoDto"}}}}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/api/high-tank/app/user/validateReplenishInfo": {"post": {"tags": ["小程序-用户信息"], "summary": "判断用户是否需要信息不全", "operationId": "validateReplenishInfoUsingPOST", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfReplenishUserInfoDto"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/api/high-tank/data_permissons/save": {"post": {"tags": ["数据权限"], "summary": "保存权限配置", "operationId": "saveUsingPOST", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataPermissionsSaveDto"}}}}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/api/high-tank/error/exthrow": {"get": {"tags": ["filter-error-controller"], "summary": "error", "operationId": "errorUsingGET", "responses": {"200": {"description": "OK"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}, "put": {"tags": ["filter-error-controller"], "summary": "error", "operationId": "errorUsingPUT", "responses": {"200": {"description": "OK"}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}, "post": {"tags": ["filter-error-controller"], "summary": "error", "operationId": "errorUsingPOST", "responses": {"200": {"description": "OK"}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}, "delete": {"tags": ["filter-error-controller"], "summary": "error", "operationId": "errorUsingDELETE", "responses": {"200": {"description": "OK"}, "204": {"description": "No Content"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}}, "security": [{"Authorization": ["global"]}]}, "options": {"tags": ["filter-error-controller"], "summary": "error", "operationId": "errorUsingOPTIONS", "responses": {"200": {"description": "OK"}, "204": {"description": "No Content"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}}, "security": [{"Authorization": ["global"]}]}, "head": {"tags": ["filter-error-controller"], "summary": "error", "operationId": "errorUsingHEAD", "responses": {"200": {"description": "OK"}, "204": {"description": "No Content"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}}, "security": [{"Authorization": ["global"]}]}, "patch": {"tags": ["filter-error-controller"], "summary": "error", "operationId": "errorUsingPATCH", "responses": {"200": {"description": "OK"}, "204": {"description": "No Content"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}}, "security": [{"Authorization": ["global"]}]}, "trace": {"tags": ["filter-error-controller"], "summary": "error", "operationId": "errorUsingTRACE", "responses": {"200": {"description": "OK"}, "204": {"description": "No Content"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}}, "security": [{"Authorization": ["global"]}]}}, "/api/high-tank/notify/chinaPay": {"post": {"tags": ["支付回调"], "summary": "payNotify", "operationId": "payNotifyUsingPOST", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "string"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/api/high-tank/order/excelOrder": {"post": {"tags": ["订单接口"], "summary": "订单列表导出", "operationId": "excelOrderUsingPOST", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderListDto"}}}}, "responses": {"200": {"description": "OK"}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/api/high-tank/order/excelOrderByday": {"get": {"tags": ["订单接口"], "summary": "订单日报导出", "operationId": "excelOrderBydayUsingGET", "responses": {"200": {"description": "OK"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/api/high-tank/order/list": {"post": {"tags": ["订单接口"], "summary": "订单列表查询", "operationId": "listUsingPOST_1", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderListDto"}}}}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/PagedResultOfListOfOrderListVo"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/api/high-tank/order/order_details": {"get": {"tags": ["订单接口"], "summary": "订单详情", "operationId": "detailsUsingGET", "parameters": [{"name": "orderNo", "in": "query", "description": "订单号", "required": true, "style": "form", "allowReserved": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfWebOrderInfoVo"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/api/high-tank/order/updateOrder": {"get": {"tags": ["订单接口"], "summary": "订单信息更新测试", "operationId": "updateOrderUsingGET", "parameters": [{"name": "orderNo", "in": "query", "description": "orderNo", "required": true, "style": "form", "allowReserved": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfboolean"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/api/high-tank/product/add": {"post": {"tags": ["产品接口"], "summary": "新建", "operationId": "saveUsingPOST_1", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/HtProduct对象"}}}}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfobject"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/api/high-tank/product/delete": {"post": {"tags": ["产品接口"], "summary": "删除", "operationId": "deleteUsingPOST", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "additionalProperties": {"type": "array", "items": {"type": "integer", "format": "int64"}}}}}}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfobject"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/api/high-tank/product/info": {"get": {"tags": ["产品接口"], "summary": "详情", "operationId": "getUsingGET", "parameters": [{"name": "id", "in": "query", "description": "id", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfHtProduct对象"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/api/high-tank/product/page": {"post": {"tags": ["产品接口"], "summary": "分页", "operationId": "pageUsingPOST", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductQueryVo"}}}}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/PagedResultOfListOfHtProduct对象"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/api/high-tank/product/update": {"post": {"tags": ["产品接口"], "summary": "修改", "operationId": "updateUsingPOST", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/HtProduct对象"}}}}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfobject"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/api/high-tank/product/updateStatus": {"post": {"tags": ["产品接口"], "summary": "更新状态", "operationId": "updateStatusUsingPOST", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductUpdateStatusVo"}}}}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfobject"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/api/high-tank/product/upload": {"post": {"tags": ["产品接口"], "summary": "上传图片", "description": "上传产品图片，返回图片访问URL", "operationId": "uploadUsingPOST_1", "requestBody": {"content": {"application/json": {"schema": {"type": "string", "format": "binary"}}, "application/octet-stream": {"schema": {"type": "string", "format": "binary"}}}}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfstring"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/api/high-tank/order/refund_apply_page": {"post": {"tags": ["退款申请单"], "summary": "退款申请列表", "operationId": "refundApplyPageUsingPOST", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RefundApplyQueryDto"}}}}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/PagedResultOfListOfHtRefundAuditRecord"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/api/high-tank/order/refund_audit": {"post": {"tags": ["退款申请单"], "summary": "退款申请审核", "operationId": "refundAuditUsingPOST", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RefundAuditDto"}}}}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/api/high-tank/sell/couponPreference": {"get": {"tags": ["销售面板接口"], "summary": "次卡偏好", "operationId": "couponPreferenceUsingGET", "parameters": [{"name": "endTime", "in": "query", "description": "结束日期", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "orderType", "in": "query", "description": "订单类型", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "startTime", "in": "query", "description": "开始日期", "required": false, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfListOfSellsPreferenceVo"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/api/high-tank/sell/couponSellCount": {"get": {"tags": ["销售面板接口"], "summary": "次卡销售量", "operationId": "couponSellCountUsingGET", "parameters": [{"name": "endTime", "in": "query", "description": "结束日期", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "orderType", "in": "query", "description": "订单类型", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "startTime", "in": "query", "description": "开始日期", "required": false, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfint"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/api/high-tank/sell/couponSellSum": {"get": {"tags": ["销售面板接口"], "summary": "次卡销售总额", "operationId": "couponSellSumUsingGET", "parameters": [{"name": "endTime", "in": "query", "description": "结束日期", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "orderType", "in": "query", "description": "订单类型", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "startTime", "in": "query", "description": "开始日期", "required": false, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfint"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/api/high-tank/sell/couponSellsRate": {"get": {"tags": ["销售面板接口"], "summary": "次卡销售率", "operationId": "couponSellsRateUsingGET", "parameters": [{"name": "endTime", "in": "query", "description": "结束日期", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "orderType", "in": "query", "description": "订单类型", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "startTime", "in": "query", "description": "开始日期", "required": false, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfSellRateVo"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/api/high-tank/sell/promoSellCount": {"get": {"tags": ["销售面板接口"], "summary": "高舱销售量", "operationId": "promoSellCountUsingGET", "parameters": [{"name": "endTime", "in": "query", "description": "结束日期", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "orderType", "in": "query", "description": "订单类型", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "startTime", "in": "query", "description": "开始日期", "required": false, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfint"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/api/high-tank/sell/promoSellSum": {"get": {"tags": ["销售面板接口"], "summary": "高舱销售总额", "operationId": "promoSellSumUsingGET", "parameters": [{"name": "endTime", "in": "query", "description": "结束日期", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "orderType", "in": "query", "description": "订单类型", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "startTime", "in": "query", "description": "开始日期", "required": false, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfint"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/api/high-tank/sell/promoSellsRate": {"get": {"tags": ["销售面板接口"], "summary": "高舱销售率", "operationId": "promoSellsRateUsingGET", "parameters": [{"name": "endTime", "in": "query", "description": "结束日期", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "orderType", "in": "query", "description": "订单类型", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "startTime", "in": "query", "description": "开始日期", "required": false, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfSellRateVo"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/api/high-tank/sell/sellsCouponUser": {"get": {"tags": ["销售面板接口"], "summary": "次卡购买前5员工", "operationId": "sellsCouponUserUsingGET", "parameters": [{"name": "endTime", "in": "query", "description": "结束日期", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "orderType", "in": "query", "description": "订单类型", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "startTime", "in": "query", "description": "开始日期", "required": false, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfListOfSellsCouponUserVo"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/api/high-tank/sell/sellsPromoDayData": {"get": {"tags": ["销售面板接口"], "summary": "高舱每天销售订单金额", "operationId": "sellsPromoDayDataUsingGET", "parameters": [{"name": "endTime", "in": "query", "description": "结束日期", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "orderType", "in": "query", "description": "订单类型", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "startTime", "in": "query", "description": "开始日期", "required": false, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfListOfSellsPromoDayDataVo"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/api/high-tank/sell/sellsPromoUser": {"get": {"tags": ["销售面板接口"], "summary": "高舱销售前10员工", "operationId": "sellsPromoUserUsingGET", "parameters": [{"name": "endTime", "in": "query", "description": "结束日期", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "orderType", "in": "query", "description": "订单类型", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "startTime", "in": "query", "description": "开始日期", "required": false, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfListOfSellsPromoUserVo"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/api/high-tank/oauth/confirm_access": {"get": {"tags": ["sso-approval-endpoint"], "summary": "getAccessConfirmation", "operationId": "getAccessConfirmationUsingGET", "parameters": [{"name": "model", "in": "query", "description": "model", "required": false, "style": "form", "schema": {"type": "object"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ModelAndView"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}, "put": {"tags": ["sso-approval-endpoint"], "summary": "getAccessConfirmation", "operationId": "getAccessConfirmationUsingPUT", "parameters": [{"name": "model", "in": "query", "description": "model", "required": false, "style": "form", "schema": {"type": "object"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ModelAndView"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}, "post": {"tags": ["sso-approval-endpoint"], "summary": "getAccessConfirmation", "operationId": "getAccessConfirmationUsingPOST", "parameters": [{"name": "model", "in": "query", "description": "model", "required": false, "style": "form", "schema": {"type": "object"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ModelAndView"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}, "delete": {"tags": ["sso-approval-endpoint"], "summary": "getAccessConfirmation", "operationId": "getAccessConfirmationUsingDELETE", "parameters": [{"name": "model", "in": "query", "description": "model", "required": false, "style": "form", "schema": {"type": "object"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ModelAndView"}}}}, "204": {"description": "No Content"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}}, "security": [{"Authorization": ["global"]}]}, "options": {"tags": ["sso-approval-endpoint"], "summary": "getAccessConfirmation", "operationId": "getAccessConfirmationUsingOPTIONS", "parameters": [{"name": "model", "in": "query", "description": "model", "required": false, "style": "form", "schema": {"type": "object"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ModelAndView"}}}}, "204": {"description": "No Content"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}}, "security": [{"Authorization": ["global"]}]}, "head": {"tags": ["sso-approval-endpoint"], "summary": "getAccessConfirmation", "operationId": "getAccessConfirmationUsingHEAD", "parameters": [{"name": "model", "in": "query", "description": "model", "required": false, "style": "form", "schema": {"type": "object"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ModelAndView"}}}}, "204": {"description": "No Content"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}}, "security": [{"Authorization": ["global"]}]}, "patch": {"tags": ["sso-approval-endpoint"], "summary": "getAccessConfirmation", "operationId": "getAccessConfirmationUsingPATCH", "parameters": [{"name": "model", "in": "query", "description": "model", "required": false, "style": "form", "schema": {"type": "object"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ModelAndView"}}}}, "204": {"description": "No Content"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}}, "security": [{"Authorization": ["global"]}]}, "trace": {"tags": ["sso-approval-endpoint"], "summary": "getAccessConfirmation", "operationId": "getAccessConfirmationUsingTRACE", "parameters": [{"name": "model", "in": "query", "description": "model", "required": false, "style": "form", "schema": {"type": "object"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ModelAndView"}}}}, "204": {"description": "No Content"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}}, "security": [{"Authorization": ["global"]}]}}, "/api/high-tank/uc_notice/ucDataChange": {"post": {"tags": ["UC数据通知"], "summary": "UC数据信息变更", "operationId": "saveUsingPOST_2", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotifySubSystemDTO"}}}}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}}, "components": {"schemas": {"BaseResult": {"title": "BaseResult", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "object"}, "message": {"type": "string"}}}, "BaseResultOfCheckCodeCouponVo": {"title": "BaseResultOfCheckCodeCouponVo", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/CheckCodeCouponVo"}, "message": {"type": "string"}}}, "BaseResultOfCheckCodePromoVo": {"title": "BaseResultOfCheckCodePromoVo", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/CheckCodePromoVo"}, "message": {"type": "string"}}}, "BaseResultOfEmployeeCardVo": {"title": "BaseResultOfEmployeeCardVo", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/EmployeeCardVo"}, "message": {"type": "string"}}}, "BaseResultOfHtProduct对象": {"title": "BaseResultOfHtProduct对象", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/HtProduct对象"}, "message": {"type": "string"}}}, "BaseResultOfListOfCheckCodePromoVo": {"title": "BaseResultOfListOfCheckCodePromoVo", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/CheckCodePromoVo"}}, "message": {"type": "string"}}}, "BaseResultOfListOfHtProduct对象": {"title": "BaseResultOfListOfHtProduct对象", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/HtProduct对象"}}, "message": {"type": "string"}}}, "BaseResultOfListOfOrderListVO": {"title": "BaseResultOfListOfOrderListVO", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/OrderListVO"}}, "message": {"type": "string"}}}, "BaseResultOfListOfProductCodeVo": {"title": "BaseResultOfListOfProductCodeVo", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/ProductCodeVo"}}, "message": {"type": "string"}}}, "BaseResultOfListOfProductUseInfoVo": {"title": "BaseResultOfListOfProductUseInfoVo", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/ProductUseInfoVo"}}, "message": {"type": "string"}}}, "BaseResultOfListOfSellsCouponUserVo": {"title": "BaseResultOfListOfSellsCouponUserVo", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/SellsCouponUserVo"}}, "message": {"type": "string"}}}, "BaseResultOfListOfSellsPreferenceVo": {"title": "BaseResultOfListOfSellsPreferenceVo", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/SellsPreferenceVo"}}, "message": {"type": "string"}}}, "BaseResultOfListOfSellsPromoDayDataVo": {"title": "BaseResultOfListOfSellsPromoDayDataVo", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/SellsPromoDayDataVo"}}, "message": {"type": "string"}}}, "BaseResultOfListOfSellsPromoUserVo": {"title": "BaseResultOfListOfSellsPromoUserVo", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/SellsPromoUserVo"}}, "message": {"type": "string"}}}, "BaseResultOfOrderInfoVo": {"title": "BaseResultOfOrderInfoVo", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/OrderInfoVo"}, "message": {"type": "string"}}}, "BaseResultOfProductOrderInfoVo": {"title": "BaseResultOfProductOrderInfoVo", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/ProductOrderInfoVo"}, "message": {"type": "string"}}}, "BaseResultOfReplenishUserInfoDto": {"title": "BaseResultOfReplenishUserInfoDto", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/ReplenishUserInfoDto"}, "message": {"type": "string"}}}, "BaseResultOfSellRateVo": {"title": "BaseResultOfSellRateVo", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/SellRateVo"}, "message": {"type": "string"}}}, "BaseResultOfWebOrderInfoVo": {"title": "BaseResultOfWebOrderInfoVo", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/WebOrderInfoVo"}, "message": {"type": "string"}}}, "BaseResultOfboolean": {"title": "BaseResultOfboolean", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "boolean"}, "message": {"type": "string"}}}, "BaseResultOfint": {"title": "BaseResultOfint", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "integer", "format": "int32"}, "message": {"type": "string"}}}, "BaseResultOfobject": {"title": "BaseResultOfobject", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "object"}, "message": {"type": "string"}}}, "BaseResultOfstring": {"title": "BaseResultOfstring", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "string"}, "message": {"type": "string"}}}, "CancelInfo": {"title": "CancelInfo", "type": "object", "properties": {"cancelTime": {"type": "string", "description": "取消时间", "format": "date-time"}, "cancelType": {"type": "integer", "description": "取消类型，1：手动取消", "format": "int32"}}}, "CheckCodeCouponVo": {"title": "CheckCodeCouponVo", "type": "object", "properties": {"codeNo": {"type": "string", "description": "券码编号"}, "orderNo": {"type": "string", "description": "订单号"}, "productName": {"type": "string", "description": "产品名称"}}, "description": "‘检查券码’结果展示对象"}, "CheckCodePromoVo": {"title": "CheckCodePromoVo", "type": "object", "properties": {"boardGate": {"type": "string", "description": "登机口"}, "codeNo": {"type": "string", "description": "券码编号"}, "effectTime": {"type": "integer", "description": "产品使用时间", "format": "int32"}, "fltDate": {"type": "string", "description": "航班日期"}, "fltNo": {"type": "string", "description": "航班号"}, "orderNo": {"type": "string", "description": "订单号"}, "orderStatus": {"type": "integer", "description": "订单状态，1：待支付；2：已取消；3：待使用；4：退款中；5：已退款；6：使用中；7：已使用", "format": "int32"}, "passengerName": {"type": "string", "description": "旅客姓名"}, "phone": {"type": "string", "description": "手机号"}, "price": {"type": "string", "description": "价格"}, "productCodeId": {"type": "integer", "description": "券码表主键", "format": "int64"}, "productName": {"type": "string", "description": "产品名称"}, "seatNo": {"type": "string", "description": "座位号"}}, "description": "‘检查优惠码’结果展示对象"}, "DataPermissionsSaveDto": {"title": "DataPermissionsSaveDto", "type": "object", "properties": {"permissionType": {"type": "string", "description": "权限类型"}, "permissionsRoleList": {"type": "array", "description": "允许被查看数据的角色id集合", "items": {"$ref": "#/components/schemas/RoleVo"}}, "role": {"description": "授权角色", "$ref": "#/components/schemas/RoleVo"}}}, "EmployeeCardVo": {"title": "EmployeeCardVo", "type": "object", "properties": {"employeeName": {"type": "string", "description": "用户的真实姓名"}, "gender": {"type": "integer", "description": "人员的性别，1表示男性，2表示女性", "format": "int32"}, "jobNumber": {"type": "string", "description": "人员工号"}, "organizationName": {"type": "string", "description": "人员的性别，1表示男性，2表示女性"}, "userType": {"type": "integer", "description": "用户的类型，比如系统管理员，客户管理员，客户普通用户等等", "format": "int32"}}}, "HtProduct对象": {"title": "HtProduct对象", "type": "object", "properties": {"deleted": {"type": "integer", "description": "删除标识，1：正常；2：删除", "format": "int32"}, "discribe": {"type": "string", "description": "产品描述"}, "effectTime": {"type": "integer", "description": "产品使用时间", "format": "int32"}, "effectType": {"type": "integer", "description": "产品有效期，1：长期有效；2：当日有效", "format": "int32"}, "id": {"type": "integer", "description": "id", "format": "int64"}, "image": {"type": "string", "description": "产品图片"}, "price": {"type": "string", "description": "产品价格"}, "productName": {"type": "string", "description": "产品名称"}, "productType": {"type": "integer", "description": "产品类型，1：员工次卡；2：旅客高舱", "format": "int32"}, "status": {"type": "integer", "description": "状态，1：上架；2：下架", "format": "int32"}, "times": {"type": "integer", "description": "次数", "format": "int32"}}, "description": "产品表"}, "HtRefundAuditRecord": {"title": "HtRefundAuditRecord", "type": "object", "properties": {"applyReason": {"type": "string", "description": "申请原因"}, "applyTime": {"type": "string", "description": "申请时间", "format": "date-time"}, "applyUser": {"type": "string", "description": "申请人"}, "applyUserId": {"type": "string", "description": "申请人ID"}, "applyUserNo": {"type": "string", "description": "申请人工号"}, "auditResult": {"type": "string", "description": "审核结果"}, "auditTime": {"type": "string", "description": "审批时间", "format": "date-time"}, "auditUser": {"type": "string", "description": "审核人"}, "auditUserId": {"type": "string", "description": "审核人ID"}, "auditUserNo": {"type": "string", "description": "审核人工号"}, "createdTime": {"type": "string", "description": "创建时间", "format": "date-time"}, "id": {"type": "string", "description": "id"}, "orderNo": {"type": "string", "description": "订单号"}, "payOrderNo": {"type": "string", "description": "支付单号"}, "refundOrderNo": {"type": "string", "description": "退款单号"}}, "description": "退款申请审核记录表"}, "ModelAndView": {"title": "ModelAndView", "type": "object"}, "NotifySubSystemDTO": {"title": "NotifySubSystemDTO", "type": "object", "properties": {"businessTypes": {"type": "array", "description": "业务类型列表", "items": {"type": "string"}}, "sourceIds": {"type": "array", "description": "资源ID列表", "items": {"type": "integer", "format": "int64"}}, "sourceType": {"type": "string", "description": "资源类型"}, "tenantId": {"type": "integer", "description": "租户ID", "format": "int64"}}, "description": "通知子系统请求参数"}, "OrderAddVo": {"title": "OrderAddVo", "type": "object", "properties": {"passengerName": {"type": "string", "description": "旅客姓名"}, "phone": {"type": "string", "description": "手机号"}, "productId": {"type": "integer", "description": "产品id", "format": "int64"}, "userNo": {"type": "string", "description": "手机号"}}, "description": "次卡订单新增对象"}, "OrderInfoVo": {"title": "OrderInfoVo", "type": "object", "properties": {"boardGate": {"type": "string", "description": "登机口"}, "cancelTime": {"type": "string", "description": "取消时间", "format": "date-time"}, "cancelType": {"type": "integer", "description": "取消类型，1：手动取消；2：自动取消", "format": "int32"}, "codeNoList": {"type": "array", "description": "券码集合", "items": {"type": "string"}}, "createdName": {"type": "string", "description": "创建人姓名"}, "createdNo": {"type": "string", "description": "创建人工号"}, "createdTime": {"type": "string", "description": "创建时间", "format": "date-time"}, "effectTime": {"type": "string", "description": "产品使用时间"}, "fltDate": {"type": "string", "description": "航班日期"}, "fltNo": {"type": "string", "description": "航班号"}, "orderNo": {"type": "string", "description": "订单号"}, "passengerName": {"type": "string", "description": "旅客姓名"}, "payOrderNo": {"type": "string", "description": "支付订单号"}, "payTime": {"type": "string", "description": "支付时间", "format": "date-time"}, "phone": {"type": "string", "description": "手机号"}, "price": {"type": "string", "description": "价格"}, "productName": {"type": "string", "description": "产品名称"}, "productType": {"type": "integer", "description": "产品类型，1：员工次卡；2：旅客高舱", "format": "int32"}, "productUseInfoList": {"type": "array", "description": "产品使用信息展示对象集合", "items": {"$ref": "#/components/schemas/ProductUseInfoVo"}}, "refundInfoList": {"type": "array", "description": "退款信息展示对象集合", "items": {"$ref": "#/components/schemas/RefundInfoVo"}}, "seatNo": {"type": "string", "description": "座位号"}, "status": {"type": "integer", "description": "状态，1：待支付；2：已取消；3：待使用；4：退款中；5：已退款；6：使用中；7：已使用", "format": "int32"}, "times": {"type": "integer", "description": "次数", "format": "int32"}, "useTimes": {"type": "string", "description": "产品使用次数"}, "userNo": {"type": "string", "description": "购买人工号"}}, "description": "订单详情展示对象"}, "OrderItem": {"title": "OrderItem", "type": "object", "properties": {"asc": {"type": "boolean"}, "column": {"type": "string"}}}, "OrderListDto": {"title": "OrderListDto", "type": "object", "properties": {"createdDateEnd": {"type": "string", "description": "创建日期-结束"}, "createdDateStart": {"type": "string", "description": "创建日期-开始"}, "createdName": {"type": "string", "description": "创建人姓名"}, "isAsc": {"type": "string", "description": "排序方式，desc或asc"}, "items": {"type": "array", "description": "排序对象，包含排序列和方式，desc或asc", "items": {"$ref": "#/components/schemas/OrderItem"}}, "obc": {"type": "string", "description": "排序列名(orderByColumn)"}, "orderStatus": {"type": "string", "description": "状态，1：待支付；2：已取消；3：待使用；4：退款中；5：已退款；6：使用中；7：已使用 多线逗号分割"}, "orderType": {"type": "string", "description": "订单类型，1：员工次卡；2：旅客高舱"}, "pageNumber": {"type": "integer", "description": "要查询的页号", "format": "int64", "example": 1}, "pageSize": {"type": "integer", "description": "每页包含的数据的条数", "format": "int64", "example": 10}, "params": {"type": "object"}}}, "OrderListVO": {"title": "OrderListVO", "type": "object", "properties": {"createdName": {"type": "string", "description": "创建人姓名"}, "createdTime": {"type": "string", "description": "创建时间", "format": "date-time"}, "orderNo": {"type": "string", "description": "订单号"}, "price": {"type": "string", "description": "产品价格"}, "productId": {"type": "integer", "description": "产品id", "format": "int64"}, "productName": {"type": "string", "description": "产品名称"}, "productType": {"type": "integer", "description": "产品类型，1：员工次卡；2：旅客高舱", "format": "int32"}, "status": {"type": "integer", "description": "状态，1：待支付；2：已取消；3：待使用；4：退款中；5：已退款；6：使用中；7：已使用", "format": "int32"}}, "description": "订单列表展示对象"}, "OrderListVo": {"title": "OrderListVo", "type": "object", "properties": {"createdTime": {"type": "string", "description": "创建时间"}, "createdUser": {"type": "string", "description": "创建人"}, "effectTime": {"type": "string", "description": "使用时间"}, "effectType": {"type": "string", "description": "有效时长，1：长期有效；2：当日有效"}, "id": {"type": "string", "description": "id"}, "orderNo": {"type": "string", "description": "订单号"}, "orderStatus": {"type": "string", "description": "订单状态"}, "price": {"type": "string", "description": "产品价格"}, "productName": {"type": "string", "description": "产品名称"}, "productType": {"type": "string", "description": "产品类型"}, "productTypeStr": {"type": "string", "description": "产品类型名称"}, "times": {"type": "string", "description": "可用次数 次卡次数"}}}, "OrderPassengerAddVo": {"title": "OrderPassengerAddVo", "type": "object", "properties": {"boardGate": {"maxLength": 5, "minLength": 0, "type": "string", "description": "登机口"}, "fltDate": {"maxLength": 10, "minLength": 0, "type": "string", "description": "航班日期"}, "fltNo": {"maxLength": 10, "minLength": 0, "type": "string", "description": "航班号"}, "passengerName": {"maxLength": 20, "minLength": 0, "type": "string", "description": "旅客姓名"}, "phone": {"type": "string", "description": "手机号"}, "productId": {"type": "integer", "description": "产品id", "format": "int64"}, "seatNo": {"maxLength": 5, "minLength": 0, "type": "string", "description": "座位号"}}, "description": "高舱订单新增对象"}, "OrderQueryVo": {"title": "OrderQueryVo", "type": "object", "properties": {"createdName": {"type": "string", "description": "创建人"}, "createdEndTime": {"type": "string", "description": "创建结束日期"}, "createdStartTime": {"type": "string", "description": "创建开始日期"}, "orderTypeList": {"type": "array", "description": "订单类型，1：员工次卡；2：旅客高舱", "items": {"type": "integer", "format": "int32"}}, "statusList": {"type": "array", "description": "状态，1：待支付；2：已取消；3：待使用；4：退款中；5：已退款；6：使用中；7：已使用", "items": {"type": "integer", "format": "int32"}}}, "description": "订单列表查询对象"}, "PagedResultOfListOfHtProduct对象": {"title": "PagedResultOfListOfHtProduct对象", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "currentPage": {"type": "integer", "description": "当前是第几页的数据", "format": "int64"}, "data": {"type": "array", "description": "当前页包含的数据内容", "items": {"$ref": "#/components/schemas/HtProduct对象"}}, "message": {"type": "string"}, "pageSize": {"type": "integer", "description": "每页包含的数据条数", "format": "int64"}, "totalPages": {"type": "integer", "description": "总共有多少页数据", "format": "int64"}, "totalRecords": {"type": "integer", "description": "总共有多少条数据", "format": "int64"}}}, "PagedResultOfListOfHtRefundAuditRecord": {"title": "PagedResultOfListOfHtRefundAuditRecord", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "currentPage": {"type": "integer", "description": "当前是第几页的数据", "format": "int64"}, "data": {"type": "array", "description": "当前页包含的数据内容", "items": {"$ref": "#/components/schemas/HtRefundAuditRecord"}}, "message": {"type": "string"}, "pageSize": {"type": "integer", "description": "每页包含的数据条数", "format": "int64"}, "totalPages": {"type": "integer", "description": "总共有多少页数据", "format": "int64"}, "totalRecords": {"type": "integer", "description": "总共有多少条数据", "format": "int64"}}}, "PagedResultOfListOfOrderListVo": {"title": "PagedResultOfListOfOrderListVo", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "currentPage": {"type": "integer", "description": "当前是第几页的数据", "format": "int64"}, "data": {"type": "array", "description": "当前页包含的数据内容", "items": {"$ref": "#/components/schemas/OrderListVo"}}, "message": {"type": "string"}, "pageSize": {"type": "integer", "description": "每页包含的数据条数", "format": "int64"}, "totalPages": {"type": "integer", "description": "总共有多少页数据", "format": "int64"}, "totalRecords": {"type": "integer", "description": "总共有多少条数据", "format": "int64"}}}, "ProductCodeVo": {"title": "ProductCodeVo", "type": "object", "properties": {"codeNo": {"type": "string", "description": "券码编号"}, "discribe": {"type": "string", "description": "产品描述"}, "productName": {"type": "string", "description": "产品名称"}, "productType": {"type": "integer", "description": "产品类型，1：员工次卡；2：旅客高舱", "format": "int32"}, "status": {"type": "integer", "description": "状态，1：正常；2：已使用；3：已失效", "format": "int32"}}, "description": "‘我的券码’展示对象"}, "ProductOrderInfoVo": {"title": "ProductOrderInfoVo", "type": "object", "properties": {"passengerName": {"type": "string", "description": "旅客姓名"}, "phone": {"type": "string", "description": "手机号"}, "productOrderList": {"type": "array", "description": "产品订单展示对象集合", "items": {"$ref": "#/components/schemas/ProductOrderVo"}}, "userNo": {"type": "string", "description": "工号"}}, "description": "次卡核销-员工信息-可用产品展示对象"}, "ProductOrderVo": {"title": "ProductOrderVo", "type": "object", "properties": {"createdTime": {"type": "string", "description": "创建日期", "format": "date-time"}, "productName": {"type": "string", "description": "产品名称"}, "times": {"type": "integer", "description": "产品的次数", "format": "int32"}, "useTimes": {"type": "integer", "description": "使用次数", "format": "int32"}}, "description": "次卡核销-可用产品展示对象"}, "ProductQueryVo": {"title": "ProductQueryVo", "type": "object", "properties": {"isAsc": {"type": "string", "description": "排序方式，desc或asc"}, "items": {"type": "array", "description": "排序对象，包含排序列和方式，desc或asc", "items": {"$ref": "#/components/schemas/OrderItem"}}, "obc": {"type": "string", "description": "排序列名(orderByColumn)"}, "pageNumber": {"type": "integer", "description": "要查询的页号", "format": "int64", "example": 1}, "pageSize": {"type": "integer", "description": "每页包含的数据的条数", "format": "int64", "example": 10}, "params": {"type": "object"}, "productName": {"type": "string", "description": "产品名称"}, "productType": {"type": "integer", "description": "产品类型，1：员工次卡；2：旅客高舱", "format": "int32"}}, "description": "产品查询条件"}, "ProductUpdateStatusVo": {"title": "ProductUpdateStatusVo", "type": "object", "properties": {"id": {"type": "integer", "description": "主键", "format": "int64"}, "status": {"type": "integer", "description": "状态，1：上架；2：下架", "format": "int32"}}, "description": "产品状态更新对象"}, "ProductUseInfo": {"title": "ProductUseInfo", "type": "object", "properties": {"boardGate": {"type": "string", "description": "登机口"}, "codeNo": {"type": "string", "description": "券码编号"}, "fltDate": {"type": "string", "description": "航班日期"}, "fltNo": {"type": "string", "description": "航班号"}, "passengerName": {"type": "string", "description": "旅客姓名"}, "phone": {"type": "string", "description": "手机号"}, "seatNo": {"type": "string", "description": "座位号"}, "userNo": {"type": "string", "description": "工号"}, "userType": {"type": "integer", "description": "用户类型，1：购买人；2：使用人", "format": "int32"}, "verifyTime": {"type": "string", "description": "核销时间", "format": "date-time"}, "verifyUserName": {"type": "string", "description": "核销人姓名"}, "verifyUserNo": {"type": "string", "description": "核销人工号"}}}, "ProductUseInfoVo": {"title": "ProductUseInfoVo", "type": "object", "properties": {"boardGate": {"type": "string", "description": "登机口"}, "codeNo": {"type": "string", "description": "券码"}, "fltDate": {"type": "string", "description": "航班日期"}, "fltNo": {"type": "string", "description": "航班号"}, "passengerName": {"type": "string", "description": "旅客姓名"}, "phone": {"type": "string", "description": "手机号"}, "productName": {"type": "string", "description": "产品名称"}, "seatNo": {"type": "string", "description": "座位号"}, "userNo": {"type": "string", "description": "工号"}, "verifyTime": {"type": "string", "description": "核销时间", "format": "date-time"}, "verifyUserName": {"type": "string", "description": "核销人姓名"}, "verifyUserNo": {"type": "string", "description": "核销人工号"}}, "description": "产品使用信息展示对象"}, "RefundApplyDto": {"title": "RefundApplyDto", "type": "object", "properties": {"applyReason": {"type": "string", "description": "退款原因"}, "orderNo": {"type": "string", "description": "业务订单号"}}}, "RefundApplyQueryDto": {"title": "RefundApplyQueryDto", "type": "object", "properties": {"applyDateEnd": {"type": "string", "description": "申请时间结束 yyyy-MM-dd"}, "applyDateStart": {"type": "string", "description": "申请时间开始 yyyy-MM-dd"}, "applyUserName": {"type": "string", "description": "申请人姓名"}, "auditResult": {"type": "string", "description": "审核结果 0待审核 1通过  2不通过  多值查询逗号分割"}, "isAsc": {"type": "string", "description": "排序方式，desc或asc"}, "items": {"type": "array", "description": "排序对象，包含排序列和方式，desc或asc", "items": {"$ref": "#/components/schemas/OrderItem"}}, "obc": {"type": "string", "description": "排序列名(orderByColumn)"}, "orderNo": {"type": "string", "description": "订单号"}, "orderType": {"type": "string", "description": "订单类型"}, "pageNumber": {"type": "integer", "description": "要查询的页号", "format": "int64", "example": 1}, "pageSize": {"type": "integer", "description": "每页包含的数据的条数", "format": "int64", "example": 10}, "params": {"type": "object"}}}, "RefundAuditDto": {"title": "RefundAuditDto", "type": "object", "properties": {"auditResult": {"type": "string", "description": "审核结果 0待审核 1通过  2不通过"}, "refundApplyId": {"type": "string", "description": "退款申请单ID"}}}, "RefundInfoVo": {"title": "RefundInfoVo", "type": "object", "properties": {"applyReason": {"type": "string", "description": "申请原因"}, "applyTime": {"type": "string", "description": "申请时间", "format": "date-time"}, "applyUser": {"type": "string", "description": "申请人"}, "auditResult": {"type": "string", "description": "审核结果"}, "auditTime": {"type": "string", "description": "审批时间", "format": "date-time"}, "auditUser": {"type": "string", "description": "审核人"}, "offlineRefundTime": {"type": "string", "description": "下线退款时间", "format": "date-time"}, "offlineRefundUser": {"type": "string", "description": "下线退款人"}, "offlineRefundUserNo": {"type": "string", "description": "下线退款人工号"}, "refundOrderNo": {"type": "string", "description": "退款单号"}, "refundStatus": {"type": "string", "description": "退款状态"}, "refundSuccessTime": {"type": "string", "description": "退款到账时间", "format": "date-time"}}, "description": "退款信息展示对象"}, "RefundRecodeInfo": {"title": "RefundRecodeInfo", "type": "object", "properties": {"applyReason": {"type": "string", "description": "申请原因"}, "applyTime": {"type": "string", "description": "申请时间", "format": "date-time"}, "applyUser": {"type": "string", "description": "申请人"}, "auditResult": {"type": "string", "description": "审核结果"}, "auditTime": {"type": "string", "description": "审批时间", "format": "date-time"}, "auditUser": {"type": "string", "description": "审核人"}, "id": {"type": "string", "description": "退款审核id"}, "refundOrderNo": {"type": "string", "description": "退款单号"}, "refundStatus": {"type": "string", "description": "1发起退款 待审核  2退款中  3已退款  4退款失败  5关闭"}, "refundSuccessTime": {"type": "string", "description": "退款到账时间", "format": "date-time"}}}, "ReplenishUserInfoDto": {"title": "ReplenishUserInfoDto", "type": "object", "properties": {"bankCardNo": {"type": "string", "description": "银行卡号"}, "cardHolder": {"type": "string", "description": "持卡人"}, "depositBank": {"type": "string", "description": "开户银行"}, "idNo": {"type": "string", "description": "证件号"}}}, "RoleVo": {"title": "RoleVo", "type": "object", "properties": {"roleId": {"type": "string", "description": "角色id"}, "roleName": {"type": "string", "description": "角色名称"}}}, "SellRateVo": {"title": "SellRateVo", "type": "object", "properties": {"refundCount": {"type": "number", "description": "退款数量", "format": "bigdecimal"}, "refundRate": {"type": "number", "description": "退款率", "format": "bigdecimal"}, "verifyRate": {"type": "number", "description": "核销率", "format": "bigdecimal"}}, "description": "销售率结果展示对象"}, "SellsCouponUserVo": {"title": "SellsCouponUserVo", "type": "object", "properties": {"priceSum": {"type": "integer", "description": "价格总数", "format": "int32"}, "userName": {"type": "string", "description": "姓名"}}, "description": "次卡购买员工展示对象"}, "SellsPreferenceVo": {"title": "SellsPreferenceVo", "type": "object", "properties": {"priceSum": {"type": "number", "description": "价格总数", "format": "bigdecimal"}, "productName": {"type": "string", "description": "产品名称"}, "ratio": {"type": "number", "description": "退款数量", "format": "bigdecimal"}}, "description": "次卡偏好结果展示对象"}, "SellsPromoDayDataVo": {"title": "SellsPromoDayDataVo", "type": "object", "properties": {"createTime": {"type": "string", "description": "日期", "format": "date"}, "orderCount": {"type": "integer", "description": "订单数量", "format": "int32"}, "priceSum": {"type": "integer", "description": "金额总数", "format": "int32"}}, "description": "高舱产品每天销售订单展示对象"}, "SellsPromoUserVo": {"title": "SellsPromoUserVo", "type": "object", "properties": {"priceSum": {"type": "number", "description": "金额", "format": "bigdecimal"}, "userName": {"type": "string", "description": "姓名"}}, "description": "高舱销售员工展示对象"}, "VerifyCouponInfoVo": {"title": "VerifyCouponInfoVo", "type": "object", "properties": {"boardGate": {"type": "string", "description": "登机口"}, "codeNo": {"type": "string", "description": "券码编号"}, "fltDate": {"type": "string", "description": "航班日期"}, "fltNo": {"type": "string", "description": "航班号"}, "orderNo": {"type": "string", "description": "订单号"}, "passengerName": {"type": "string", "description": "旅客姓名"}, "phone": {"type": "string", "description": "手机号"}, "seatNo": {"type": "string", "description": "座位号"}, "userNo": {"type": "string", "description": "工号"}, "userNoKin": {"type": "string", "description": "亲属的工号"}}, "description": "次卡‘提交核销’时用户信息和券码信息对象"}, "VerifyCouponVo": {"title": "VerifyCouponVo", "type": "object", "properties": {"verifyCouponInfoVoList": {"type": "array", "description": "用户信息券码信息对象集合", "items": {"$ref": "#/components/schemas/VerifyCouponInfoVo"}}}, "description": "次卡‘提交核销’对象"}, "WebOrderInfoVo": {"title": "WebOrderInfoVo", "type": "object", "properties": {"boardGate": {"type": "string", "description": "登机口"}, "cancelInfo": {"description": "订单取消信息", "$ref": "#/components/schemas/CancelInfo"}, "codeNo": {"type": "array", "description": "核销码或者优惠券码", "items": {"type": "string"}}, "createdName": {"type": "string", "description": "创建人姓名"}, "createdNo": {"type": "string", "description": "创建人工号"}, "createdTime": {"type": "string", "description": "创建时间", "format": "date-time"}, "effectTime": {"type": "string", "description": "产品使用时间"}, "fltDate": {"type": "string", "description": "航班日期"}, "fltNo": {"type": "string", "description": "航班号"}, "offlineRefundTime": {"type": "string", "description": "下线退款时间", "format": "date-time"}, "offlineRefundUser": {"type": "string", "description": "下线退款人"}, "orderNo": {"type": "string", "description": "订单号"}, "passengerName": {"type": "string", "description": "旅客姓名"}, "payOrderNo": {"type": "string", "description": "支付订单号"}, "payTime": {"type": "string", "description": "支付时间", "format": "date-time"}, "phone": {"type": "string", "description": "手机号"}, "price": {"type": "string", "description": "价格"}, "productName": {"type": "string", "description": "产品名称"}, "productType": {"type": "integer", "description": "产品类型，1：员工次卡；2：旅客高舱", "format": "int32"}, "productUseInfos": {"type": "array", "description": "产品使用记录", "items": {"$ref": "#/components/schemas/ProductUseInfo"}}, "refundOrderNo": {"type": "string", "description": "退款单号"}, "refundRecodeInfos": {"type": "array", "description": "退款审批记录", "items": {"$ref": "#/components/schemas/RefundRecodeInfo"}}, "refundStatus": {"type": "string", "description": "退款状态  已退款  退款失败"}, "refundSuccessTime": {"type": "string", "description": "退款到账时间", "format": "date-time"}, "seatNo": {"type": "string", "description": "座位号"}, "status": {"type": "integer", "description": "状态，1：待支付；2：已取消；3：待使用；4：退款中；5：已退款；6：使用中；7：已使用", "format": "int32"}, "times": {"type": "integer", "description": "次数", "format": "int32"}, "useTimes": {"type": "string", "description": "产品使用次数"}, "userNo": {"type": "string", "description": "工号"}}}}, "securitySchemes": {"Authorization": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header"}}}}