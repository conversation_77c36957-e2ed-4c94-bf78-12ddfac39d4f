{"openapi": "3.0.3", "info": {"title": "uc Api Doc", "contact": {"name": "swcares team", "email": "<EMAIL>"}, "version": "Application Version：0.1.0-PAIP-SNAPSHOT-250513014305"}, "servers": [{"url": "http://paip-dev.iprd.sw:80", "description": "Inferred Url"}], "tags": [{"name": "人员管理接口", "description": "Employee Controller"}, {"name": "子系统-用户管理接口", "description": "Ss User Controller"}, {"name": "子系统-组织管理接口", "description": "Ss Organization Controller"}, {"name": "子系统-角色管理接口", "description": "Ss Role Controller"}, {"name": "岗位信息表接口", "description": "Job Position Controller"}, {"name": "权限组信息接口", "description": "Authority Group Controller"}, {"name": "用户中心接口", "description": "User Center Controller"}, {"name": "用户登录资源访问接口", "description": "Login Controller"}, {"name": "用户管理接口", "description": "User Controller"}, {"name": "系统用户、租户用户登录登出接口", "description": "Authority Resource Controller"}, {"name": "组织管理接口", "description": "Organization Controller"}, {"name": "角色管理接口", "description": "Role Controller"}, {"name": "附件操作", "description": "File Attachment Controller"}], "paths": {"/uc/api/uc/authority/group/admin": {"get": {"tags": ["权限组信息接口"], "summary": "通过ID查询权限组管理员列表，URL传参", "operationId": "getGroupAdminByIdUsingGET", "parameters": [{"name": "id", "in": "query", "description": "主键id", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfListOfAuthorityGroupManagerVO"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/uc/api/uc/authority/group/admin/{id}": {"get": {"tags": ["权限组信息接口"], "summary": "通过ID查询权限组管理员列表", "operationId": "getGroupAdminUsingGET", "parameters": [{"name": "id", "in": "path", "description": "主键id", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfListOfAuthorityGroupManagerVO"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/uc/api/uc/authority/group/checkbox/{userId}": {"get": {"tags": ["权限组信息接口"], "summary": "获取复选框类型的权限组列表", "operationId": "getCheckBoxListUsingGET", "parameters": [{"name": "userId", "in": "path", "description": "用户id", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfListOfAuthorityGroupCheckBoxVO"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/uc/api/uc/authority/group/delete": {"post": {"tags": ["权限组信息接口"], "summary": "批量删除系统功能资源", "operationId": "deleteUsingPOST_7", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FeatureResourceDeleteDTO"}}}}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfobject"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/uc/api/uc/authority/group/get": {"get": {"tags": ["权限组信息接口"], "summary": "通过ID查询权限组信息记录，URL传参", "operationId": "getByIdUsingGET_7", "parameters": [{"name": "id", "in": "query", "description": "主键id", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfAuthorityGroupDetailVO"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/uc/api/uc/authority/group/get/{id}": {"get": {"tags": ["权限组信息接口"], "summary": "通过ID查询权限组信息记录", "operationId": "getUsingGET_10", "parameters": [{"name": "id", "in": "path", "description": "主键id", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfAuthorityGroupDetailVO"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/uc/api/uc/authority/group/list": {"post": {"tags": ["权限组信息接口"], "summary": "获取权限组列表", "operationId": "listUsingPOST_1", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfListOfAuthorityGroupVO"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/uc/api/uc/authority/group/page": {"post": {"tags": ["权限组信息接口"], "summary": "条件分页查询权限组信息记录", "operationId": "pageUsingPOST_11", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AuthorityGroupPagedDTO"}}}}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/PagedResultOfListOfAuthorityGroupPageDetailVO"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/uc/api/uc/authority/group/save": {"post": {"tags": ["权限组信息接口"], "summary": "新建权限组信息记录", "operationId": "saveUsingPOST_8", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AuthorityGroupAddDTO"}}}}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfobject"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/uc/api/uc/authority/group/status": {"post": {"tags": ["权限组信息接口"], "summary": "批量修改权限组状态", "operationId": "batchChangeStatusUsingPOST_2", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AuthorityGroupChgStatusDTO"}}}}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfobject"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/uc/api/uc/authority/group/update": {"post": {"tags": ["权限组信息接口"], "summary": "修改权限组信息记录", "operationId": "updateUsingPOST_8", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AuthorityGroupUpdateDTO"}}}}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfobject"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/uc/api/uc/authority/group/update_manager": {"post": {"tags": ["权限组信息接口"], "summary": "修改权限组关联的管理员列表", "operationId": "updateManagerUsingPOST", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AuthorityGroupUpdateManagerDTO"}}}}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfobject"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/uc/api/auth/logout": {"get": {"tags": ["系统用户、租户用户登录登出接口"], "summary": "退出登录接口", "operationId": "logoutUsingGET", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfstring"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/uc/api/auth/menu": {"get": {"tags": ["系统用户、租户用户登录登出接口"], "summary": "获取当前用户菜单", "description": "subSystemCode为子系统编号，多个用英文逗号分隔", "operationId": "menuUsingGET", "parameters": [{"name": "subSystemCode", "in": "query", "description": "subSystemCode", "required": false, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfAuthMenuVO"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/uc/api/auth/principal": {"get": {"tags": ["系统用户、租户用户登录登出接口"], "summary": "user", "operationId": "userUsingGET", "parameters": [{"name": "name", "in": "query", "required": false, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "object"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/uc/api/auth/subSystem": {"get": {"tags": ["系统用户、租户用户登录登出接口"], "summary": "获取当前用户所有子系统", "operationId": "subSystemUsingGET", "parameters": [{"name": "subSystemCode", "in": "query", "description": "subSystemCode", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "terminal", "in": "query", "description": "terminal", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfListOfFeatureSubsystemVO"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/uc/api/auth/userInfo": {"get": {"tags": ["系统用户、租户用户登录登出接口"], "summary": "userInfo", "operationId": "userInfoUsingGET", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfLoginUserDetails"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/uc/api/uc/employee/batch_chg_org": {"post": {"tags": ["人员管理接口"], "summary": "批量修改人员归属机构", "operationId": "batchChgOrgUsingPOST", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/EmployeeOrgDTO"}}}}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfobject"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/uc/api/uc/employee/delete": {"post": {"tags": ["人员管理接口"], "summary": "通过ID删除系统人员记录，URL传参", "operationId": "deleteByIdUsingPOST", "requestBody": {"content": {"application/json": {"schema": {"type": "integer", "format": "int64"}}}}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfobject"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/uc/api/uc/employee/delete/{id}": {"post": {"tags": ["人员管理接口"], "summary": "通过ID删除系统人员记录", "operationId": "deleteUsingPOST_8", "parameters": [{"name": "id", "in": "path", "description": "主键id", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfobject"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/uc/api/uc/employee/emps_modify_org": {"post": {"tags": ["人员管理接口"], "summary": "人员组织机构变更迁移，批量", "operationId": "modifyOrgUsingPOST", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/EmployeeOrgDTO"}}}}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfobject"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/uc/api/uc/employee/get": {"get": {"tags": ["人员管理接口"], "summary": "通过ID查询系统人员记录，URL传参", "operationId": "getByIdUsingGET_8", "parameters": [{"name": "id", "in": "query", "description": "主键id", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfEmployeeVO"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/uc/api/uc/employee/get/{id}": {"get": {"tags": ["人员管理接口"], "summary": "通过ID查询系统人员记录", "operationId": "getUsingGET_11", "parameters": [{"name": "id", "in": "path", "description": "主键id", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfEmployeeVO"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/uc/api/uc/employee/get_by_ids": {"post": {"tags": ["人员管理接口"], "summary": "根据人员Id集，获取指定人员列表", "operationId": "getByIdsUsingPOST_2", "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"type": "integer", "format": "int64"}}}}}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfListOfUserEmployeeVO"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/uc/api/uc/employee/get_by_mgtorg": {"post": {"tags": ["人员管理接口"], "summary": "加载管理员管理范围的人员，返回UserEmployeeVO结构的数据集", "operationId": "getByManagerScopeUsingPOST", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/EmployeePagedDTO"}}}}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfListOfUserEmployeeVO"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/uc/api/uc/employee/get_by_org": {"get": {"tags": ["人员管理接口"], "summary": "根据指定条件查询人员信息，返回EmployeeDetailVO结构的数据集", "operationId": "getEmployeeByOrgUsingGET", "parameters": [{"name": "condition", "in": "query", "description": "模糊搜索条件", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "<PERSON><PERSON><PERSON>", "in": "query", "description": "钉钉账号", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "emailAddress", "in": "query", "description": "人员的电子邮件地址", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "empIds", "in": "query", "description": "人员ID集", "required": false, "style": "pipeDelimited", "schema": {"type": "array", "items": {"type": "integer", "format": "int64"}}}, {"name": "employeeCode", "in": "query", "description": "员工的编号", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "isContainChildren", "in": "query", "description": "通过机构ID进行查询时，是否包含子机构下人员", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "jobNumber", "in": "query", "description": "人员工号", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "jobPosition", "in": "query", "description": "岗位代码", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "orgIds", "in": "query", "description": "机构ID集", "required": false, "style": "pipeDelimited", "schema": {"type": "array", "items": {"type": "integer", "format": "int64"}}}, {"name": "phone", "in": "query", "description": "人员的电话", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "status", "in": "query", "description": "人员当前的状态，例如在职、离职、停职等", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "wx<PERSON><PERSON>id", "in": "query", "description": "微信OpenId", "required": false, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfListOfEmployeeVO"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/uc/api/uc/employee/get_detail": {"get": {"tags": ["人员管理接口"], "summary": "通过ID获取指定人员详细信息，包括用户账号（不包含密码）", "operationId": "getDetailByIdUsingGET", "parameters": [{"name": "id", "in": "query", "description": "主键id", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfEmployeeVO"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/uc/api/uc/employee/page": {"post": {"tags": ["人员管理接口"], "summary": "条件分页查询系统人员记录列表", "operationId": "pageUsingPOST_12", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/EmployeePagedDTO"}}}}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/PagedResultOfListOfEmployeeVO"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/uc/api/uc/employee/save": {"post": {"tags": ["人员管理接口"], "summary": "新增人员信息保存", "operationId": "saveUsingPOST_9", "requestBody": {"content": {"multipart/form-data": {"schema": {"required": ["belongToOrg", "gender", "name", "phone", "status"], "type": "object", "properties": {"belongToOrg": {"type": "integer", "description": "所属机构的id", "format": "int64"}, "birthday": {"type": "string", "description": "人员的出生日期", "format": "date"}, "dingtalk": {"type": "string", "description": "钉钉账号"}, "emailAddress": {"type": "string", "description": "人员的电子邮件地址"}, "employeeCode": {"type": "string", "description": "员工的编号"}, "file": {"type": "string", "description": "file", "format": "binary"}, "gender": {"type": "integer", "description": "人员的性别，1表示男性，2表示女性", "format": "int32"}, "graduateFrom": {"type": "string", "description": "人员的毕业院校"}, "id": {"type": "integer", "description": "id", "format": "int64"}, "idCard": {"type": "string", "description": "身份证号"}, "jobNumber": {"type": "string", "description": "人员工号"}, "jobPosition": {"type": "string", "description": "人员的岗位"}, "jobTitle": {"type": "string", "description": "员工的职务"}, "name": {"type": "string", "description": "人员的姓名"}, "phone": {"type": "string", "description": "人员的电话"}, "photoRul": {"type": "string", "description": "人员头像的地址"}, "politicalStatus": {"type": "string", "description": "人员的政治面貌"}, "status": {"type": "integer", "description": "人员当前的状态，例如在职、离职、停职等", "format": "int32"}, "syncStatus": {"type": "integer", "description": "钉钉同步状态(0-未同步、1-同步成功、2-同步失败、3-已从钉钉删除)", "format": "int32"}, "workTerminal": {"type": "string", "description": "工作航站(多个逗号分隔)"}, "wxOpenid": {"type": "string", "description": "微信OpenId"}}}, "encoding": {"birthday": {"contentType": "text/plain"}, "belongToOrg": {"contentType": "text/plain"}, "gender": {"contentType": "text/plain"}, "wxOpenid": {"contentType": "text/plain"}, "politicalStatus": {"contentType": "text/plain"}, "idCard": {"contentType": "text/plain"}, "jobTitle": {"contentType": "text/plain"}, "jobPosition": {"contentType": "text/plain"}, "employeeCode": {"contentType": "text/plain"}, "dingtalk": {"contentType": "text/plain"}, "emailAddress": {"contentType": "text/plain"}, "workTerminal": {"contentType": "text/plain"}, "phone": {"contentType": "text/plain"}, "name": {"contentType": "text/plain"}, "id": {"contentType": "text/plain"}, "photoRul": {"contentType": "text/plain"}, "graduateFrom": {"contentType": "text/plain"}, "syncStatus": {"contentType": "text/plain"}, "jobNumber": {"contentType": "text/plain"}, "status": {"contentType": "text/plain"}}}}}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfobject"}}, "multipart/form-data": {"schema": {"$ref": "#/components/schemas/BaseResultOfobject"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/uc/api/uc/employee/save_create_user": {"post": {"tags": ["人员管理接口"], "summary": "人员新增同时创建用户", "operationId": "saveOrUserUsingPOST", "requestBody": {"content": {"multipart/form-data": {"schema": {"required": ["employee.belongToOrg", "employee.gender", "employee.name", "employee.phone", "employee.status", "user.employeeInfo.belongToOrg", "user.employeeInfo.gender", "user.employeeInfo.name", "user.employeeInfo.phone", "user.employeeInfo.status", "user.labels[0].sysLabelId", "user.name", "user.status", "user.type"], "type": "object", "properties": {"autoUserName": {"type": "boolean", "description": "系统生成用户名"}, "employee.belongToOrg": {"type": "integer", "description": "所属机构的id", "format": "int64"}, "employee.birthday": {"type": "string", "description": "人员的出生日期", "format": "date"}, "employee.dingtalk": {"type": "string", "description": "钉钉账号"}, "employee.emailAddress": {"type": "string", "description": "人员的电子邮件地址"}, "employee.employeeCode": {"type": "string", "description": "员工的编号"}, "employee.gender": {"type": "integer", "description": "人员的性别，1表示男性，2表示女性", "format": "int32"}, "employee.graduateFrom": {"type": "string", "description": "人员的毕业院校"}, "employee.id": {"type": "integer", "description": "id", "format": "int64"}, "employee.idCard": {"type": "string", "description": "身份证号"}, "employee.jobNumber": {"type": "string", "description": "人员工号"}, "employee.jobPosition": {"type": "string", "description": "人员的岗位"}, "employee.jobTitle": {"type": "string", "description": "员工的职务"}, "employee.name": {"type": "string", "description": "人员的姓名"}, "employee.phone": {"type": "string", "description": "人员的电话"}, "employee.photoRul": {"type": "string", "description": "人员头像的地址"}, "employee.politicalStatus": {"type": "string", "description": "人员的政治面貌"}, "employee.status": {"type": "integer", "description": "人员当前的状态，例如在职、离职、停职等", "format": "int32"}, "employee.syncStatus": {"type": "integer", "description": "钉钉同步状态(0-未同步、1-同步成功、2-同步失败、3-已从钉钉删除)", "format": "int32"}, "employee.workTerminal": {"type": "string", "description": "工作航站(多个逗号分隔)"}, "employee.wxOpenid": {"type": "string", "description": "微信OpenId"}, "file": {"type": "string", "description": "file", "format": "binary"}, "isCreateUser": {"type": "boolean", "description": "是否创建用户"}, "user.employeeId": {"type": "integer", "description": "人员的唯一标识，没有业务含义", "format": "int64"}, "user.employeeInfo.belongToOrg": {"type": "integer", "description": "所属机构的id", "format": "int64"}, "user.employeeInfo.birthday": {"type": "string", "description": "人员的出生日期", "format": "date"}, "user.employeeInfo.dingtalk": {"type": "string", "description": "钉钉账号"}, "user.employeeInfo.emailAddress": {"type": "string", "description": "人员的电子邮件地址"}, "user.employeeInfo.employeeCode": {"type": "string", "description": "员工的编号"}, "user.employeeInfo.gender": {"type": "integer", "description": "人员的性别，1表示男性，2表示女性", "format": "int32"}, "user.employeeInfo.graduateFrom": {"type": "string", "description": "人员的毕业院校"}, "user.employeeInfo.id": {"type": "integer", "description": "id", "format": "int64"}, "user.employeeInfo.idCard": {"type": "string", "description": "身份证号"}, "user.employeeInfo.jobNumber": {"type": "string", "description": "人员工号"}, "user.employeeInfo.jobPosition": {"type": "string", "description": "人员的岗位"}, "user.employeeInfo.jobTitle": {"type": "string", "description": "员工的职务"}, "user.employeeInfo.name": {"type": "string", "description": "人员的姓名"}, "user.employeeInfo.phone": {"type": "string", "description": "人员的电话"}, "user.employeeInfo.photoRul": {"type": "string", "description": "人员头像的地址"}, "user.employeeInfo.politicalStatus": {"type": "string", "description": "人员的政治面貌"}, "user.employeeInfo.status": {"type": "integer", "description": "人员当前的状态，例如在职、离职、停职等", "format": "int32"}, "user.employeeInfo.syncStatus": {"type": "integer", "description": "钉钉同步状态(0-未同步、1-同步成功、2-同步失败、3-已从钉钉删除)", "format": "int32"}, "user.employeeInfo.workTerminal": {"type": "string", "description": "工作航站(多个逗号分隔)"}, "user.employeeInfo.wxOpenid": {"type": "string", "description": "微信OpenId"}, "user.labels[0].sysLabelId": {"type": "integer", "description": "标签ID", "format": "int64"}, "user.labels[0].valueIds": {"type": "array", "description": "标签值ID", "items": {"type": "string"}}, "user.labels[0].values": {"type": "array", "description": "标签值", "items": {"type": "string"}}, "user.manageRoles": {"type": "array", "description": "用户管理的角色", "items": {"type": "integer", "format": "int64"}}, "user.name": {"type": "string", "description": "用户名，可用于登录系统，必须唯一。"}, "user.remark": {"type": "string", "description": "备注"}, "user.resources": {"type": "array", "description": "用户被授予的资源(CA专用)", "items": {"type": "integer", "format": "int64"}}, "user.roles": {"type": "array", "description": "用户被授予的角色", "items": {"type": "integer", "format": "int64"}}, "user.status": {"type": "string", "description": "用户的状态，例如0：停用，1：启用，2：锁定，3：过期等", "enum": ["DISABLED", "ENABLE", "EXPIRE", "LOCK", "NOT_ACTIVATED"]}, "user.type": {"type": "string", "description": "用户的类型，-1-系统管理员，1-企业管理员，2-普通管理员，3-普通用户，4-机构管理员", "enum": ["ADMIN", "COM_ADMIN", "CONSUMER", "ENT_ADMIN", "SYSTEM"]}, "user.userManageOrganization": {"type": "array", "description": "用户管理的机构", "items": {"type": "integer", "format": "int64"}}, "user.userWorkForOrganization": {"type": "array", "description": "用户为哪些机构工作", "items": {"type": "integer", "format": "int64"}}}}, "encoding": {"user.status": {"contentType": "text/plain"}, "user.employeeInfo.photoRul": {"contentType": "text/plain"}, "user.employeeInfo.employeeCode": {"contentType": "text/plain"}, "employee.birthday": {"contentType": "text/plain"}, "user.employeeInfo.wxOpenid": {"contentType": "text/plain"}, "user.manageRoles": {"contentType": "text/plain"}, "employee.status": {"contentType": "text/plain"}, "user.userManageOrganization": {"contentType": "text/plain"}, "user.labels[0].valueIds": {"contentType": "text/plain"}, "employee.phone": {"contentType": "text/plain"}, "employee.dingtalk": {"contentType": "text/plain"}, "employee.jobNumber": {"contentType": "text/plain"}, "employee.id": {"contentType": "text/plain"}, "user.labels[0].sysLabelId": {"contentType": "text/plain"}, "user.employeeInfo.gender": {"contentType": "text/plain"}, "user.employeeInfo.graduateFrom": {"contentType": "text/plain"}, "user.userWorkForOrganization": {"contentType": "text/plain"}, "employee.name": {"contentType": "text/plain"}, "user.employeeInfo.politicalStatus": {"contentType": "text/plain"}, "employee.idCard": {"contentType": "text/plain"}, "employee.photoRul": {"contentType": "text/plain"}, "user.employeeInfo.birthday": {"contentType": "text/plain"}, "isCreateUser": {"contentType": "text/plain"}, "user.employeeInfo.status": {"contentType": "text/plain"}, "user.employeeInfo.id": {"contentType": "text/plain"}, "user.employeeInfo.jobTitle": {"contentType": "text/plain"}, "user.employeeId": {"contentType": "text/plain"}, "user.employeeInfo.emailAddress": {"contentType": "text/plain"}, "employee.jobTitle": {"contentType": "text/plain"}, "user.name": {"contentType": "text/plain"}, "user.employeeInfo.dingtalk": {"contentType": "text/plain"}, "user.employeeInfo.phone": {"contentType": "text/plain"}, "user.labels[0].values": {"contentType": "text/plain"}, "user.employeeInfo.name": {"contentType": "text/plain"}, "employee.emailAddress": {"contentType": "text/plain"}, "user.employeeInfo.belongToOrg": {"contentType": "text/plain"}, "employee.wxOpenid": {"contentType": "text/plain"}, "employee.belongToOrg": {"contentType": "text/plain"}, "employee.jobPosition": {"contentType": "text/plain"}, "user.employeeInfo.jobPosition": {"contentType": "text/plain"}, "employee.gender": {"contentType": "text/plain"}, "user.employeeInfo.idCard": {"contentType": "text/plain"}, "employee.graduateFrom": {"contentType": "text/plain"}, "user.employeeInfo.syncStatus": {"contentType": "text/plain"}, "employee.politicalStatus": {"contentType": "text/plain"}, "employee.workTerminal": {"contentType": "text/plain"}, "user.resources": {"contentType": "text/plain"}, "user.employeeInfo.workTerminal": {"contentType": "text/plain"}, "user.employeeInfo.jobNumber": {"contentType": "text/plain"}, "user.remark": {"contentType": "text/plain"}, "employee.syncStatus": {"contentType": "text/plain"}, "user.type": {"contentType": "text/plain"}, "employee.employeeCode": {"contentType": "text/plain"}, "autoUserName": {"contentType": "text/plain"}, "user.roles": {"contentType": "text/plain"}}}}}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfobject"}}, "multipart/form-data": {"schema": {"$ref": "#/components/schemas/BaseResultOfobject"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/uc/api/uc/employee/save_emp_user": {"post": {"tags": ["人员管理接口"], "summary": "人员新增同时创建用户，批量", "operationId": "saveEmpUserUsingPOST", "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/EmployeeUserDTO"}}}}}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfMapOfstringAndUserEmpIdVO"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/uc/api/uc/employee/save_he": {"post": {"tags": ["人员管理接口"], "summary": "人员新增同时创建用户--不验证用户权限", "operationId": "saveHeUsingPOST", "requestBody": {"content": {"multipart/form-data": {"schema": {"required": ["employee.belongToOrg", "employee.gender", "employee.name", "employee.phone", "employee.status", "user.employeeInfo.belongToOrg", "user.employeeInfo.gender", "user.employeeInfo.name", "user.employeeInfo.phone", "user.employeeInfo.status", "user.labels[0].sysLabelId", "user.name", "user.status", "user.type"], "type": "object", "properties": {"autoUserName": {"type": "boolean", "description": "系统生成用户名"}, "employee.belongToOrg": {"type": "integer", "description": "所属机构的id", "format": "int64"}, "employee.birthday": {"type": "string", "description": "人员的出生日期", "format": "date"}, "employee.dingtalk": {"type": "string", "description": "钉钉账号"}, "employee.emailAddress": {"type": "string", "description": "人员的电子邮件地址"}, "employee.employeeCode": {"type": "string", "description": "员工的编号"}, "employee.gender": {"type": "integer", "description": "人员的性别，1表示男性，2表示女性", "format": "int32"}, "employee.graduateFrom": {"type": "string", "description": "人员的毕业院校"}, "employee.id": {"type": "integer", "description": "id", "format": "int64"}, "employee.idCard": {"type": "string", "description": "身份证号"}, "employee.jobNumber": {"type": "string", "description": "人员工号"}, "employee.jobPosition": {"type": "string", "description": "人员的岗位"}, "employee.jobTitle": {"type": "string", "description": "员工的职务"}, "employee.name": {"type": "string", "description": "人员的姓名"}, "employee.phone": {"type": "string", "description": "人员的电话"}, "employee.photoRul": {"type": "string", "description": "人员头像的地址"}, "employee.politicalStatus": {"type": "string", "description": "人员的政治面貌"}, "employee.status": {"type": "integer", "description": "人员当前的状态，例如在职、离职、停职等", "format": "int32"}, "employee.syncStatus": {"type": "integer", "description": "钉钉同步状态(0-未同步、1-同步成功、2-同步失败、3-已从钉钉删除)", "format": "int32"}, "employee.workTerminal": {"type": "string", "description": "工作航站(多个逗号分隔)"}, "employee.wxOpenid": {"type": "string", "description": "微信OpenId"}, "file": {"type": "string", "description": "file", "format": "binary"}, "isCreateUser": {"type": "boolean", "description": "是否创建用户"}, "user.employeeId": {"type": "integer", "description": "人员的唯一标识，没有业务含义", "format": "int64"}, "user.employeeInfo.belongToOrg": {"type": "integer", "description": "所属机构的id", "format": "int64"}, "user.employeeInfo.birthday": {"type": "string", "description": "人员的出生日期", "format": "date"}, "user.employeeInfo.dingtalk": {"type": "string", "description": "钉钉账号"}, "user.employeeInfo.emailAddress": {"type": "string", "description": "人员的电子邮件地址"}, "user.employeeInfo.employeeCode": {"type": "string", "description": "员工的编号"}, "user.employeeInfo.gender": {"type": "integer", "description": "人员的性别，1表示男性，2表示女性", "format": "int32"}, "user.employeeInfo.graduateFrom": {"type": "string", "description": "人员的毕业院校"}, "user.employeeInfo.id": {"type": "integer", "description": "id", "format": "int64"}, "user.employeeInfo.idCard": {"type": "string", "description": "身份证号"}, "user.employeeInfo.jobNumber": {"type": "string", "description": "人员工号"}, "user.employeeInfo.jobPosition": {"type": "string", "description": "人员的岗位"}, "user.employeeInfo.jobTitle": {"type": "string", "description": "员工的职务"}, "user.employeeInfo.name": {"type": "string", "description": "人员的姓名"}, "user.employeeInfo.phone": {"type": "string", "description": "人员的电话"}, "user.employeeInfo.photoRul": {"type": "string", "description": "人员头像的地址"}, "user.employeeInfo.politicalStatus": {"type": "string", "description": "人员的政治面貌"}, "user.employeeInfo.status": {"type": "integer", "description": "人员当前的状态，例如在职、离职、停职等", "format": "int32"}, "user.employeeInfo.syncStatus": {"type": "integer", "description": "钉钉同步状态(0-未同步、1-同步成功、2-同步失败、3-已从钉钉删除)", "format": "int32"}, "user.employeeInfo.workTerminal": {"type": "string", "description": "工作航站(多个逗号分隔)"}, "user.employeeInfo.wxOpenid": {"type": "string", "description": "微信OpenId"}, "user.labels[0].sysLabelId": {"type": "integer", "description": "标签ID", "format": "int64"}, "user.labels[0].valueIds": {"type": "array", "description": "标签值ID", "items": {"type": "string"}}, "user.labels[0].values": {"type": "array", "description": "标签值", "items": {"type": "string"}}, "user.manageRoles": {"type": "array", "description": "用户管理的角色", "items": {"type": "integer", "format": "int64"}}, "user.name": {"type": "string", "description": "用户名，可用于登录系统，必须唯一。"}, "user.remark": {"type": "string", "description": "备注"}, "user.resources": {"type": "array", "description": "用户被授予的资源(CA专用)", "items": {"type": "integer", "format": "int64"}}, "user.roles": {"type": "array", "description": "用户被授予的角色", "items": {"type": "integer", "format": "int64"}}, "user.status": {"type": "string", "description": "用户的状态，例如0：停用，1：启用，2：锁定，3：过期等", "enum": ["DISABLED", "ENABLE", "EXPIRE", "LOCK", "NOT_ACTIVATED"]}, "user.type": {"type": "string", "description": "用户的类型，-1-系统管理员，1-企业管理员，2-普通管理员，3-普通用户，4-机构管理员", "enum": ["ADMIN", "COM_ADMIN", "CONSUMER", "ENT_ADMIN", "SYSTEM"]}, "user.userManageOrganization": {"type": "array", "description": "用户管理的机构", "items": {"type": "integer", "format": "int64"}}, "user.userWorkForOrganization": {"type": "array", "description": "用户为哪些机构工作", "items": {"type": "integer", "format": "int64"}}}}, "encoding": {"user.status": {"contentType": "text/plain"}, "user.employeeInfo.photoRul": {"contentType": "text/plain"}, "user.employeeInfo.employeeCode": {"contentType": "text/plain"}, "employee.birthday": {"contentType": "text/plain"}, "user.employeeInfo.wxOpenid": {"contentType": "text/plain"}, "user.manageRoles": {"contentType": "text/plain"}, "employee.status": {"contentType": "text/plain"}, "user.userManageOrganization": {"contentType": "text/plain"}, "user.labels[0].valueIds": {"contentType": "text/plain"}, "employee.phone": {"contentType": "text/plain"}, "employee.dingtalk": {"contentType": "text/plain"}, "employee.jobNumber": {"contentType": "text/plain"}, "employee.id": {"contentType": "text/plain"}, "user.labels[0].sysLabelId": {"contentType": "text/plain"}, "user.employeeInfo.gender": {"contentType": "text/plain"}, "user.employeeInfo.graduateFrom": {"contentType": "text/plain"}, "user.userWorkForOrganization": {"contentType": "text/plain"}, "employee.name": {"contentType": "text/plain"}, "user.employeeInfo.politicalStatus": {"contentType": "text/plain"}, "employee.idCard": {"contentType": "text/plain"}, "employee.photoRul": {"contentType": "text/plain"}, "user.employeeInfo.birthday": {"contentType": "text/plain"}, "isCreateUser": {"contentType": "text/plain"}, "user.employeeInfo.status": {"contentType": "text/plain"}, "user.employeeInfo.id": {"contentType": "text/plain"}, "user.employeeInfo.jobTitle": {"contentType": "text/plain"}, "user.employeeId": {"contentType": "text/plain"}, "user.employeeInfo.emailAddress": {"contentType": "text/plain"}, "employee.jobTitle": {"contentType": "text/plain"}, "user.name": {"contentType": "text/plain"}, "user.employeeInfo.dingtalk": {"contentType": "text/plain"}, "user.employeeInfo.phone": {"contentType": "text/plain"}, "user.labels[0].values": {"contentType": "text/plain"}, "user.employeeInfo.name": {"contentType": "text/plain"}, "employee.emailAddress": {"contentType": "text/plain"}, "user.employeeInfo.belongToOrg": {"contentType": "text/plain"}, "employee.wxOpenid": {"contentType": "text/plain"}, "employee.belongToOrg": {"contentType": "text/plain"}, "employee.jobPosition": {"contentType": "text/plain"}, "user.employeeInfo.jobPosition": {"contentType": "text/plain"}, "employee.gender": {"contentType": "text/plain"}, "user.employeeInfo.idCard": {"contentType": "text/plain"}, "employee.graduateFrom": {"contentType": "text/plain"}, "user.employeeInfo.syncStatus": {"contentType": "text/plain"}, "employee.politicalStatus": {"contentType": "text/plain"}, "employee.workTerminal": {"contentType": "text/plain"}, "user.resources": {"contentType": "text/plain"}, "user.employeeInfo.workTerminal": {"contentType": "text/plain"}, "user.employeeInfo.jobNumber": {"contentType": "text/plain"}, "user.remark": {"contentType": "text/plain"}, "employee.syncStatus": {"contentType": "text/plain"}, "user.type": {"contentType": "text/plain"}, "employee.employeeCode": {"contentType": "text/plain"}, "autoUserName": {"contentType": "text/plain"}, "user.roles": {"contentType": "text/plain"}}}}}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfobject"}}, "multipart/form-data": {"schema": {"$ref": "#/components/schemas/BaseResultOfobject"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/uc/api/uc/employee/sync": {"post": {"tags": ["人员管理接口"], "summary": "同步人员信息到钉钉，URL传参", "operationId": "syncByIdUsingPOST", "requestBody": {"content": {"application/json": {"schema": {"type": "integer", "format": "int64"}}}}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfobject"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/uc/api/uc/employee/sync/{id}": {"post": {"tags": ["人员管理接口"], "summary": "同步人员信息到钉钉", "operationId": "syncUsingPOST", "parameters": [{"name": "id", "in": "path", "description": "人员id", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfobject"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/uc/api/uc/employee/update": {"post": {"tags": ["人员管理接口"], "summary": "修改系统人员信息保存", "operationId": "updateUsingPOST_9", "parameters": [{"name": "belongToOrg", "in": "query", "description": "所属机构的id", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "birthday", "in": "query", "description": "人员的出生日期", "required": false, "style": "form", "schema": {"type": "string", "format": "date"}}, {"name": "<PERSON><PERSON><PERSON>", "in": "query", "description": "钉钉账号", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "emailAddress", "in": "query", "description": "人员的电子邮件地址", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "employeeCode", "in": "query", "description": "员工的编号", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "gender", "in": "query", "description": "人员的性别，1表示男性，2表示女性", "required": true, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "graduateFrom", "in": "query", "description": "人员的毕业院校", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "id", "in": "query", "description": "id", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "idCard", "in": "query", "description": "身份证号", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "jobNumber", "in": "query", "description": "人员工号", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "jobPosition", "in": "query", "description": "人员的岗位", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "jobTitle", "in": "query", "description": "员工的职务", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "name", "in": "query", "description": "人员的姓名", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "phone", "in": "query", "description": "人员的电话", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "photoRul", "in": "query", "description": "人员头像的地址", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "politicalStatus", "in": "query", "description": "人员的政治面貌", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "status", "in": "query", "description": "人员当前的状态，例如在职、离职、停职等", "required": true, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "syncStatus", "in": "query", "description": "钉钉同步状态(0-未同步、1-同步成功、2-同步失败、3-已从钉钉删除)", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "workTerminal", "in": "query", "description": "工作航站(多个逗号分隔)", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "wx<PERSON><PERSON>id", "in": "query", "description": "微信OpenId", "required": false, "style": "form", "schema": {"type": "string"}}], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"file": {"type": "string", "description": "file", "format": "binary"}}}}}}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfobject"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/uc/api/uc/fat/delete": {"get": {"tags": ["附件操作"], "summary": "删除文件，URL传参", "operationId": "deleteByIdUsingGET", "parameters": [{"name": "id", "in": "query", "description": "主键id", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfobject"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/uc/api/uc/fat/delete/{id}": {"get": {"tags": ["附件操作"], "summary": "删除文件", "operationId": "deleteUsingGET", "parameters": [{"name": "id", "in": "path", "description": "主键id", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfobject"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/uc/api/uc/fat/download": {"get": {"tags": ["附件操作"], "summary": "下载文件", "operationId": "downloadUsingGET", "parameters": [{"name": "id", "in": "query", "description": "主键id", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfobject"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/uc/api/uc/fat/upload": {"post": {"tags": ["附件操作"], "summary": "上传文件", "description": "参数：file:上传文件,ducket_name:文件目录名,user_name:用户名", "operationId": "uploadUsingPOST_1", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfListOfFileAttachment"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/uc/api/uc/job/position/delete": {"post": {"tags": ["岗位信息表接口"], "summary": "通过ID删除岗位信息表记录", "operationId": "deleteUsingPOST_9", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/JobPositionDeleteDTO"}}}}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfobject"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/uc/api/uc/job/position/get": {"get": {"tags": ["岗位信息表接口"], "summary": "通过ID查询岗位信息表记录", "operationId": "getUsingGET_12", "parameters": [{"name": "id", "in": "query", "description": "主键id", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfJobPositionVO"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/uc/api/uc/job/position/get_by_user": {"get": {"tags": ["岗位信息表接口"], "summary": "通过用户ID查询其所有岗位信息", "operationId": "getByUserIdUsingGET", "parameters": [{"name": "id", "in": "query", "description": "主键id", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfListOfJobPositionVO"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/uc/api/uc/job/position/get_combo": {"get": {"tags": ["岗位信息表接口"], "summary": "获取岗位列表复选框，按类型分组", "operationId": "getComboListByEmployeeUsingGET", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfListOfJobPositionComboVO"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/uc/api/uc/job/position/list": {"post": {"tags": ["岗位信息表接口"], "summary": "条件分页查询岗位信息表记录", "operationId": "listUsingPOST_2", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfListOfJobPositionVO"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/uc/api/uc/job/position/page": {"post": {"tags": ["岗位信息表接口"], "summary": "条件分页查询岗位信息表记录", "operationId": "pageUsingPOST_13", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/JobPositionPagedDTO"}}}}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/PagedResultOfListOfJobPositionVO"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/uc/api/uc/job/position/save": {"post": {"tags": ["岗位信息表接口"], "summary": "新建岗位信息表记录", "operationId": "saveUsingPOST_10", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/JobPositionDTO"}}}}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfJobPosition"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/uc/api/uc/job/position/update": {"post": {"tags": ["岗位信息表接口"], "summary": "修改岗位信息表记录", "operationId": "updateUsingPOST_10", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/JobPositionDTO"}}}}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfJobPosition"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/uc/api/login/amend_pwd": {"get": {"tags": ["用户登录资源访问接口"], "summary": "找回密码--第三步修改密码", "operationId": "amendPwdUsingGET", "parameters": [{"name": "account", "in": "query", "description": "账号：手机、邮箱", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "code", "in": "query", "description": "验证码", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "password", "in": "query", "description": "新密码", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "type", "in": "query", "description": "密码找回类型,1手机，2邮件", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfobject"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/uc/api/login/check_code": {"get": {"tags": ["用户登录资源访问接口"], "summary": "找回密码--第二步验证", "operationId": "checkCodeUsingGET", "parameters": [{"name": "account", "in": "query", "description": "account", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "type", "in": "query", "description": "type", "required": true, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "code", "in": "query", "description": "code", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfobject"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/uc/api/login/login_captcha": {"get": {"tags": ["用户登录资源访问接口"], "summary": "登录发送验证码", "operationId": "loginCaptchaUsingGET", "parameters": [{"name": "account", "in": "query", "description": "account", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "type", "in": "query", "description": "type", "required": true, "style": "form", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfobject"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/uc/api/login/name_exist": {"get": {"tags": ["用户登录资源访问接口"], "summary": "检查用户名是否重复", "description": "用户名、工号、邮箱、手机号", "operationId": "nameExistUsingGET", "parameters": [{"name": "userName", "in": "query", "description": "userName", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "employeeId", "in": "query", "description": "employeeId", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfobject"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/uc/api/login/pwd_strategy": {"get": {"tags": ["用户登录资源访问接口"], "summary": "获取密码策略", "operationId": "passwordStrategyUsingGET", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfobject"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/uc/api/login/send_captcha": {"get": {"tags": ["用户登录资源访问接口"], "summary": "密码找回发送验证码", "operationId": "sendCaptchaUsingGET", "parameters": [{"name": "account", "in": "query", "description": "account", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "type", "in": "query", "description": "type", "required": true, "style": "form", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfobject"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/uc/api/admin/organization/delete/{id}": {"post": {"tags": ["组织管理接口"], "summary": "通过ID删除组织", "operationId": "deleteUsingPOST_10", "parameters": [{"name": "id", "in": "path", "description": "组织id", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfstring"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/uc/api/admin/organization/get/{id}": {"get": {"tags": ["组织管理接口"], "summary": "通过ID查询组织", "operationId": "getUsingGET_13", "parameters": [{"name": "id", "in": "path", "description": "组织id", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfOrganizationVO"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/uc/api/admin/organization/getByIds": {"post": {"tags": ["组织管理接口"], "summary": "根据组织ID集获取对应的组织对象集", "description": "一次参数最大个数为999", "operationId": "getByIdsUsingPOST_3", "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"type": "integer", "format": "int64"}}}}}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfListOfOrganization"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/uc/api/admin/organization/getOrgTree": {"get": {"tags": ["组织管理接口"], "summary": "根据条件获取组织树", "operationId": "getOrgTreeUsingGET_1", "parameters": [{"name": "orgId", "in": "query", "description": "机构id", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "orgType", "in": "query", "description": "是否获取用户的管理机构:0全部1管理机构", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32", "enum": ["0", "1"]}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfListOfOrganizationTreeVO"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/uc/api/admin/organization/page": {"post": {"tags": ["组织管理接口"], "summary": "分页查询组织", "operationId": "pageUsingPOST_14", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrganizationPagedDTO"}}}}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/PagedResultOfListOfOrganizationVO"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/uc/api/admin/organization/save": {"post": {"tags": ["组织管理接口"], "summary": "新建组织", "description": "包括根、公司、部门等等", "operationId": "saveUsingPOST_11", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrganizationDTO"}}}}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfstring"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/uc/api/admin/organization/update": {"post": {"tags": ["组织管理接口"], "summary": "修改组织", "description": "包括根、公司、部门等等", "operationId": "updateUsingPOST_11", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrganizationDTO"}}}}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfstring"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/uc/api/admin/role/authorizationCheck": {"post": {"tags": ["角色管理接口"], "summary": "检查列表中是否存在已授权给用户的角色", "description": "true为已存在，false为不存在，用于角色停用、删除时做校验", "operationId": "checkAuthorizationUsingPOST_1", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RoleAuthorizationCheckDTO"}}}}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfboolean"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/uc/api/admin/role/authorize": {"post": {"tags": ["角色管理接口"], "summary": "为角色分配授权用户", "operationId": "authorizeUserUsingPOST", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RoleAuthorizeUserDTO"}}}}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfobject"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/uc/api/admin/role/delete/{id}": {"post": {"tags": ["角色管理接口"], "summary": "根据ID删除角色", "operationId": "deleteUsingPOST_11", "parameters": [{"name": "id", "in": "path", "description": "角色id", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfstring"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/uc/api/admin/role/get/{id}": {"get": {"tags": ["角色管理接口"], "summary": "通过ID查询角色详细信息", "operationId": "getUsingGET_14", "parameters": [{"name": "id", "in": "path", "description": "主键id", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfRoleDetailVO"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/uc/api/admin/role/getAuthRole/{id}": {"get": {"tags": ["角色管理接口"], "summary": "根据ID查询角色", "description": "需要有数据权限", "operationId": "getAuthRoleUsingGET", "parameters": [{"name": "id", "in": "path", "description": "角色id", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfRoleDetailVO"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/uc/api/admin/role/getAuthorizedUser/{id}": {"get": {"tags": ["角色管理接口"], "summary": "根据角色ID查询已授权用户列表", "operationId": "getRoleAuthorizedUserUsingGET", "parameters": [{"name": "id", "in": "path", "description": "角色id", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfListOfRoleAuthorizedUserVO"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/uc/api/admin/role/getByResource/{id}": {"get": {"tags": ["角色管理接口"], "summary": "根据资源ID获取拥有此资源权限的所有角色", "operationId": "getRolesByResourceIdUsingGET", "parameters": [{"name": "id", "in": "path", "description": "角色id", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfListOfRoleVO"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/uc/api/admin/role/getByUser": {"post": {"tags": ["角色管理接口"], "summary": "获取指定用户已授权角色和当前用户管理角色", "description": "当userId不为空时，返回用户的已授权、未授权角色集合；当userId为空时，返回对应orgId的所有角色集合", "operationId": "getUserRoleListUsingPOST", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RoleUserDTO"}}}}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfRoleUserVO"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/uc/api/admin/role/getManageRoleTree": {"get": {"tags": ["角色管理接口"], "summary": "获取当前用户管理的角色树", "operationId": "getManageRoleTreeUsingGET", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfListOfRoleTreeVO"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/uc/api/admin/role/list": {"post": {"tags": ["角色管理接口"], "summary": "根据ID集合查询角色列表", "operationId": "getByIdListUsingPOST", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RoleSearchDTO"}}}}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfListOfRoleDetailVO"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/uc/api/admin/role/page": {"post": {"tags": ["角色管理接口"], "summary": "分页查询角色", "operationId": "pageUsingPOST_15", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RolePagedDTO"}}}}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/PagedResultOfListOfRoleVO"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/uc/api/admin/role/save": {"post": {"tags": ["角色管理接口"], "summary": "新建角色", "operationId": "saveUsingPOST_12", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RoleAddDTO"}}}}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfRole"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/uc/api/admin/role/status": {"post": {"tags": ["角色管理接口"], "summary": "批量修改角色状态", "operationId": "batchChangeStatusUsingPOST_3", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RoleChangeStatusDTO"}}}}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfstring"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/uc/api/admin/role/update": {"post": {"tags": ["角色管理接口"], "summary": "修改角色", "operationId": "updateUsingPOST_12", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RoleUpdateDTO"}}}}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfstring"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/uc/api/admin/ss/organization/delete/{id}": {"post": {"tags": ["子系统-组织管理接口"], "summary": "通过ID删除组织", "operationId": "deleteUsingPOST_12", "parameters": [{"name": "id", "in": "path", "description": "组织id", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfstring"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/uc/api/admin/ss/organization/get/{id}": {"get": {"tags": ["子系统-组织管理接口"], "summary": "通过ID查询组织", "operationId": "getUsingGET_15", "parameters": [{"name": "id", "in": "path", "description": "组织id", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfOrganizationVO"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/uc/api/admin/ss/organization/getByIds": {"post": {"tags": ["子系统-组织管理接口"], "summary": "根据组织ID集获取对应的组织对象集", "description": "一次参数最大个数为999", "operationId": "getByIdsUsingPOST_4", "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"type": "integer", "format": "int64"}}}}}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfListOfOrganization"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/uc/api/admin/ss/organization/getOrgTree": {"get": {"tags": ["子系统-组织管理接口"], "summary": "根据条件获取组织树", "operationId": "getOrgTreeUsingGET_2", "parameters": [{"name": "orgId", "in": "query", "description": "机构id", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "orgType", "in": "query", "description": "是否获取用户的管理机构:0全部1管理机构", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32", "enum": ["0", "1"]}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfListOfOrganizationTreeVO"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/uc/api/admin/ss/organization/page": {"post": {"tags": ["子系统-组织管理接口"], "summary": "分页查询组织", "operationId": "pageUsingPOST_16", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrganizationPagedDTO"}}}}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/PagedResultOfListOfOrganizationVO"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/uc/api/admin/ss/organization/save": {"post": {"tags": ["子系统-组织管理接口"], "summary": "新建组织", "description": "包括根、公司、部门等等", "operationId": "saveUsingPOST_13", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrganizationDTO"}}}}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfstring"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/uc/api/admin/ss/organization/update": {"post": {"tags": ["子系统-组织管理接口"], "summary": "修改组织", "description": "包括根、公司、部门等等", "operationId": "updateUsingPOST_13", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrganizationDTO"}}}}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfstring"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/uc/api/admin/ss/role/delete/{id}": {"post": {"tags": ["子系统-角色管理接口"], "summary": "根据ID删除角色", "operationId": "deleteUsingPOST_13", "parameters": [{"name": "id", "in": "path", "description": "角色id", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfstring"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/uc/api/admin/ss/role/getAuthRole/{id}": {"get": {"tags": ["子系统-角色管理接口"], "summary": "根据ID查询角色", "description": "需要有数据权限", "operationId": "getAuthRoleUsingGET_1", "parameters": [{"name": "id", "in": "path", "description": "角色id", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfRoleDetailVO"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/uc/api/admin/ss/role/getAuthorizedUser/{id}": {"get": {"tags": ["子系统-角色管理接口"], "summary": "根据角色ID查询已授权用户列表", "operationId": "getRoleAuthorizedUserUsingGET_1", "parameters": [{"name": "id", "in": "path", "description": "角色id", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfListOfRoleAuthorizedUserVO"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/uc/api/admin/ss/role/getByResource/{id}": {"get": {"tags": ["子系统-角色管理接口"], "summary": "根据资源ID获取拥有此资源权限的所有角色", "operationId": "getRolesByResourceIdUsingGET_1", "parameters": [{"name": "id", "in": "path", "description": "角色id", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfListOfRoleVO"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/uc/api/admin/ss/role/getByUser": {"post": {"tags": ["子系统-角色管理接口"], "summary": "获取指定用户已授权角色和当前用户管理角色", "description": "当userId不为空时，返回用户的已授权、未授权角色集合；当userId为空时，返回对应orgId的所有角色集合", "operationId": "getUserRoleListUsingPOST_1", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RoleUserDTO"}}}}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfRoleUserVO"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/uc/api/admin/ss/role/getManageRoleTree": {"get": {"tags": ["子系统-角色管理接口"], "summary": "获取当前用户管理的角色树", "operationId": "getManageRoleTreeUsingGET_1", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfListOfRoleTreeVO"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/uc/api/admin/ss/role/page": {"post": {"tags": ["子系统-角色管理接口"], "summary": "分页查询角色", "operationId": "pageUsingPOST_17", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RolePagedDTO"}}}}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/PagedResultOfListOfRoleVO"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/uc/api/admin/ss/role/save": {"post": {"tags": ["子系统-角色管理接口"], "summary": "新建角色", "operationId": "saveUsingPOST_14", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RoleAddDTO"}}}}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfRole"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/uc/api/admin/ss/role/status": {"post": {"tags": ["子系统-角色管理接口"], "summary": "批量修改角色状态", "operationId": "batchChangeStatusUsingPOST_4", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RoleChangeStatusDTO"}}}}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfstring"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/uc/api/admin/ss/role/update": {"post": {"tags": ["子系统-角色管理接口"], "summary": "修改角色", "operationId": "updateUsingPOST_14", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RoleUpdateDTO"}}}}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfstring"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/uc/api/admin/ss/user/batchAuth": {"post": {"tags": ["子系统-用户管理接口"], "summary": "批量授权角色", "operationId": "authorizeUsingPOST", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserAuthorizeDTO"}}}}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfstring"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/uc/api/admin/ss/user/batchAuthDelete": {"post": {"tags": ["子系统-用户管理接口"], "summary": "批量删除授权角色", "operationId": "authorizeDeleteUsingPOST", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserAuthorizeDTO"}}}}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfstring"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/uc/api/admin/ss/user/belongToOrg": {"post": {"tags": ["子系统-用户管理接口"], "summary": "根据机构ID获取归属机构在这个部门下的用户", "operationId": "belongToOrgUsingPOST", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserBelongToOrgDTO"}}}}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfListOfUserEmployeeVO"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/uc/api/admin/ss/user/changeStatus": {"post": {"tags": ["子系统-用户管理接口"], "summary": "根据用户ID修改用户状态", "description": "1:启用,0:停用,2:解锁", "operationId": "changeStatusUsingPOST_3", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserChangeStatusDTO"}}}}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfstring"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/uc/api/admin/ss/user/delete/{id}": {"post": {"tags": ["子系统-用户管理接口"], "summary": "根据用户ID删除用户", "operationId": "deleteUsingPOST_14", "parameters": [{"name": "id", "in": "path", "description": "用户id", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfstring"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/uc/api/admin/ss/user/get/{id}": {"get": {"tags": ["子系统-用户管理接口"], "summary": "根据用户ID获取用户信息", "operationId": "getUsingGET_16", "parameters": [{"name": "id", "in": "path", "description": "用户id", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfUserDetailVO"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/uc/api/admin/ss/user/getByIds": {"post": {"tags": ["子系统-用户管理接口"], "summary": "根据用户ID集获取用户集", "description": "一次最多支持999个", "operationId": "getByIdsUsingPOST_5", "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"type": "integer", "format": "int64"}}}}}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfListOfUserAllVO"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/uc/api/admin/ss/user/modifyPwd": {"post": {"tags": ["子系统-用户管理接口"], "summary": "修改用户密码", "description": "修改自己或其他用户的密码，需要输入原密码", "operationId": "modifyPasswordUsingPOST", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserChangePasswordDTO"}}}}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfstring"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/uc/api/admin/ss/user/page": {"post": {"tags": ["子系统-用户管理接口"], "summary": "分页查询用户", "operationId": "pageUsingPOST_18", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserPagedDTO"}}}}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/PagedResultOfListOfUserVO"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/uc/api/admin/ss/user/resetPwd/{id}": {"post": {"tags": ["子系统-用户管理接口"], "summary": "重置用户密码", "operationId": "resetPasswordUsingPOST", "parameters": [{"name": "id", "in": "path", "description": "用户ID", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfstring"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/uc/api/admin/ss/user/save": {"post": {"tags": ["子系统-用户管理接口"], "summary": "新建用户", "operationId": "saveUsingPOST_15", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserAddDTO"}}}}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfstring"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/uc/api/admin/ss/user/update": {"post": {"tags": ["子系统-用户管理接口"], "summary": "修改用户", "operationId": "updateUsingPOST_15", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserUpdateDTO"}}}}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfstring"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/uc/api/userCenter/changeAvatar": {"post": {"tags": ["用户中心接口"], "summary": "修改用户头像", "operationId": "changeAvatarByIdUsingPOST", "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"avatar": {"type": "string", "description": "avatar", "format": "binary"}, "id": {"type": "integer", "description": "id", "format": "int64"}}}, "encoding": {"id": {"contentType": "text/plain"}}}}}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfFileAttachment"}}, "multipart/form-data": {"schema": {"$ref": "#/components/schemas/BaseResultOfFileAttachment"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/uc/api/userCenter/changeAvatar/{id}": {"post": {"tags": ["用户中心接口"], "summary": "修改用户头像", "operationId": "changeAvatarUsingPOST", "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"avatar": {"type": "string", "description": "avatar", "format": "binary"}}}}}}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfFileAttachment"}}, "multipart/form-data": {"schema": {"$ref": "#/components/schemas/BaseResultOfFileAttachment"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/uc/api/userCenter/getUserInfo": {"get": {"tags": ["用户中心接口"], "summary": "获取当前用户个人信息", "operationId": "getUserInfoUsingGET", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfUserCenterTenantVO"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/uc/api/admin/user/batchAuth": {"post": {"tags": ["用户管理接口"], "summary": "批量授权角色", "operationId": "authorizeUsingPOST_1", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserAuthorizeDTO"}}}}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfstring"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/uc/api/admin/user/batchAuthDelete": {"post": {"tags": ["用户管理接口"], "summary": "批量删除授权角色", "operationId": "authorizeDeleteUsingPOST_1", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserAuthorizeDTO"}}}}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfstring"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/uc/api/admin/user/belongToOrg": {"post": {"tags": ["用户管理接口"], "summary": "根据机构ID获取归属机构在这个部门下的用户", "operationId": "belongToOrgUsingPOST_1", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserBelongToOrgDTO"}}}}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfListOfUserEmployeeVO"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/uc/api/admin/user/changeStatus": {"post": {"tags": ["用户管理接口"], "summary": "根据用户ID修改用户状态", "description": "1:启用,0:停用,2:解锁", "operationId": "changeStatusUsingPOST_4", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserChangeStatusDTO"}}}}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfstring"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/uc/api/admin/user/changeStatusByName": {"post": {"tags": ["用户管理接口"], "summary": "根据用户名修改用户状态", "description": "1:启用,0:停用,2:解锁", "operationId": "changeStatusByNameUsingPOST", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserChangeStatusByNameDTO"}}}}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfobject"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/uc/api/admin/user/choosePage": {"post": {"tags": ["用户管理接口"], "summary": "条件分页查询（选择用户时使用）", "operationId": "choosePageUsingPOST", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserPagedDTO"}}}}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/PagedResultOfListOfUserEmployeeVO"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/uc/api/admin/user/delete/{id}": {"post": {"tags": ["用户管理接口"], "summary": "根据用户ID删除用户", "operationId": "deleteUsingPOST_15", "parameters": [{"name": "id", "in": "path", "description": "用户id", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfstring"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/uc/api/admin/user/entAdmin": {"get": {"tags": ["用户管理接口"], "summary": "获取企业管理员", "description": "在header中传租户ID：tenantId", "operationId": "entAdminUsingGET", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfUserEnterpriseAdminVO"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/uc/api/admin/user/get/{id}": {"get": {"tags": ["用户管理接口"], "summary": "根据用户ID获取用户信息", "operationId": "getUsingGET_17", "parameters": [{"name": "id", "in": "path", "description": "用户id", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfUserDetailVO"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/uc/api/admin/user/getByIdCard": {"get": {"tags": ["用户管理接口"], "summary": "根据身份证查用户信息", "operationId": "getByIdCardUsingGET", "parameters": [{"name": "idCard", "in": "query", "description": "idCard", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfUserVO"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/uc/api/admin/user/getByIds": {"post": {"tags": ["用户管理接口"], "summary": "根据用户ID集获取用户集", "description": "一次最多支持999个", "operationId": "getByIdsUsingPOST_6", "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"type": "integer", "format": "int64"}}}}}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfListOfUserAllVO"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/uc/api/admin/user/getByPos": {"post": {"tags": ["用户管理接口"], "summary": "根据岗位代码获取用户", "operationId": "getByPosUsingPOST", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserForPositionDTO"}}}}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfListOfUserVO"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/uc/api/admin/user/modifyPwd": {"post": {"tags": ["用户管理接口"], "summary": "修改用户密码", "description": "修改自己或其他用户的密码，需要输入原密码", "operationId": "modifyPasswordUsingPOST_1", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserChangePasswordDTO"}}}}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfstring"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/uc/api/admin/user/page": {"post": {"tags": ["用户管理接口"], "summary": "分页查询用户", "operationId": "pageUsingPOST_19", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserPagedDTO"}}}}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/PagedResultOfListOfUserVO"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/uc/api/admin/user/pageCa": {"post": {"tags": ["用户管理接口"], "summary": "分页查询用户（企业管理员使用）", "operationId": "pageCaUsingPOST", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserPagedCADTO"}}}}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/PagedResultOfListOfUserVO"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/uc/api/admin/user/resetPwd/{id}": {"post": {"tags": ["用户管理接口"], "summary": "重置用户密码", "operationId": "resetPasswordUsingPOST_1", "parameters": [{"name": "id", "in": "path", "description": "用户ID", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfstring"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/uc/api/admin/user/resetPwdByName": {"post": {"tags": ["用户管理接口"], "summary": "根据用户名重置用户密码", "operationId": "resetPasswordByNameUsingPOST", "parameters": [{"name": "username", "in": "query", "description": "用户名", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfstring"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/uc/api/admin/user/save": {"post": {"tags": ["用户管理接口"], "summary": "新建用户", "operationId": "saveUsingPOST_16", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserAddDTO"}}}}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfstring"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/uc/api/admin/user/update": {"post": {"tags": ["用户管理接口"], "summary": "修改用户", "operationId": "updateUsingPOST_16", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserUpdateDTO"}}}}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfstring"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}, "/uc/api/admin/user/workForOrg": {"post": {"tags": ["用户管理接口"], "summary": "根据机构ID获取工作机构在这个部门的用户", "operationId": "workForOrgUsingPOST", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserWorkForOrgDTO"}}}}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResultOfListOfUserEmployeeVO"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}]}}}, "components": {"schemas": {"AuthMenuVO": {"title": "AuthMenuVO", "type": "object", "properties": {"menu": {"type": "array", "description": "有权限的菜单树", "items": {"$ref": "#/components/schemas/FeatureResourceTreeVO"}}, "perms": {"type": "object", "additionalProperties": {"type": "array", "items": {"type": "string"}}, "description": "有权限的控件"}}, "description": "获取登录用户菜单返回对象"}, "AuthorityGroupAddDTO": {"title": "AuthorityGroupAddDTO", "type": "object", "properties": {"authorityGroupName": {"type": "string", "description": "权限组名称"}, "remark": {"type": "string", "description": "备注描述"}, "resourceIdList": {"type": "array", "description": "权限id集合", "items": {"type": "integer", "format": "int64"}}, "status": {"type": "integer", "description": "状态，0-停用，1-启用【1:启用, 0:停用, 2:锁定, 3:过期, 4:未启用】", "format": "int32", "enum": [0, 1, 2, 3, 4]}, "userIdList": {"type": "array", "description": "管理员id集合", "items": {"type": "integer", "format": "int64"}}}, "description": "权限组新增对象"}, "AuthorityGroupCheckBoxVO": {"title": "AuthorityGroupCheckBoxVO", "type": "object", "properties": {"authorityGroupName": {"type": "string", "description": "权限组名称"}, "flag": {"type": "boolean", "description": "是否选中", "example": false}, "id": {"type": "integer", "description": "id", "format": "int64"}, "remark": {"type": "string", "description": "备注描述"}, "status": {"type": "integer", "description": "状态，0-停用，1-启用【1:启用, 0:停用, 2:锁定, 3:过期, 4:未启用】", "format": "int32", "enum": [0, 1, 2, 3, 4]}}, "description": "权限组复选框对象"}, "AuthorityGroupChgStatusDTO": {"title": "AuthorityGroupChgStatusDTO", "required": ["groupIdList", "status"], "type": "object", "properties": {"groupIdList": {"type": "array", "description": "权限组ID集合", "items": {"type": "integer", "format": "int64"}}, "status": {"type": "integer", "description": "状态值,1-启用或0-停用【1:启用, 0:停用, 2:锁定, 3:过期, 4:未启用】", "format": "int32", "enum": [0, 1, 2, 3, 4]}}, "description": "批量修改权限组状态的对象"}, "AuthorityGroupDetailVO": {"title": "AuthorityGroupDetailVO", "type": "object", "properties": {"authorityGroupName": {"type": "string", "description": "权限组名称"}, "id": {"type": "integer", "description": "id", "format": "int64"}, "managerList": {"type": "array", "description": "管理员列表", "items": {"$ref": "#/components/schemas/AuthorityGroupManagerVO"}}, "remark": {"type": "string", "description": "备注描述"}, "roleList": {"type": "array", "description": "角色列表", "items": {"$ref": "#/components/schemas/RoleAuthorityGroupVO"}}, "status": {"type": "integer", "description": "状态，0-停用，1-启用【1:启用, 0:停用, 2:锁定, 3:过期, 4:未启用】", "format": "int32", "enum": [0, 1, 2, 3, 4]}}, "description": "权限组详细信息"}, "AuthorityGroupManagerVO": {"title": "AuthorityGroupManagerVO", "type": "object", "properties": {"employeeName": {"type": "string", "description": "对应人员姓名"}, "id": {"type": "integer", "description": "用户id", "format": "int64"}, "name": {"type": "string", "description": "用户名"}}, "description": "权限组已分配的管理员对象"}, "AuthorityGroupPageDetailVO": {"title": "AuthorityGroupPageDetailVO", "type": "object", "properties": {"adminCount": {"type": "integer", "description": "管理员数量", "format": "int32"}, "authorityGroupName": {"type": "string", "description": "权限组名称"}, "id": {"type": "integer", "description": "id", "format": "int64"}, "remark": {"type": "string", "description": "备注描述"}, "roleCount": {"type": "integer", "description": "角色数量", "format": "int32"}, "status": {"type": "integer", "description": "状态，0-停用，1-启用【1:启用, 0:停用, 2:锁定, 3:过期, 4:未启用】", "format": "int32", "enum": [0, 1, 2, 3, 4]}}, "description": "权限组分页列表信息"}, "AuthorityGroupPagedDTO": {"title": "AuthorityGroupPagedDTO", "type": "object", "properties": {"isAsc": {"type": "string", "description": "排序方式，desc或asc"}, "items": {"type": "array", "description": "排序对象，包含排序列和方式，desc或asc", "items": {"$ref": "#/components/schemas/OrderItem"}}, "name": {"type": "string", "description": "权限组名称，支持模糊查询"}, "obc": {"type": "string", "description": "排序列名(orderByColumn)"}, "pageNumber": {"type": "integer", "description": "要查询的页号", "format": "int64", "example": 1}, "pageSize": {"type": "integer", "description": "每页包含的数据的条数", "format": "int64", "example": 10}, "params": {"type": "object"}, "status": {"type": "integer", "description": "权限组状态【1:启用, 0:停用, 2:锁定, 3:过期, 4:未启用】", "format": "int32", "enum": [0, 1, 2, 3, 4]}}, "description": "权限组信息"}, "AuthorityGroupUpdateDTO": {"title": "AuthorityGroupUpdateDTO", "type": "object", "properties": {"authorityGroupName": {"type": "string", "description": "权限组名称"}, "id": {"type": "integer", "description": "权限组id", "format": "int64"}, "remark": {"type": "string", "description": "备注描述"}, "resourceIdList": {"type": "array", "description": "权限id集合", "items": {"type": "integer", "format": "int64"}}, "status": {"type": "integer", "description": "状态，0-停用，1-启用【1:启用, 0:停用, 2:锁定, 3:过期, 4:未启用】", "format": "int32", "enum": [0, 1, 2, 3, 4]}, "userIdList": {"type": "array", "description": "管理员id集合", "items": {"type": "integer", "format": "int64"}}}, "description": "权限组更新对象"}, "AuthorityGroupUpdateManagerDTO": {"title": "AuthorityGroupUpdateManagerDTO", "type": "object", "properties": {"id": {"type": "integer", "description": "权限组id", "format": "int64"}, "userIdList": {"type": "array", "description": "管理员id集合", "items": {"type": "integer", "format": "int64"}}}, "description": "权限组关联管理员对象"}, "AuthorityGroupVO": {"title": "AuthorityGroupVO", "type": "object", "properties": {"authorityGroupName": {"type": "string", "description": "权限组名称"}, "id": {"type": "integer", "description": "id", "format": "int64"}, "remark": {"type": "string", "description": "备注描述"}, "status": {"type": "integer", "description": "状态，0-停用，1-启用【1:启用, 0:停用, 2:锁定, 3:过期, 4:未启用】", "format": "int32", "enum": [0, 1, 2, 3, 4]}}, "description": "权限组信息"}, "BaseResultOfAuthMenuVO": {"title": "BaseResultOfAuthMenuVO", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/AuthMenuVO"}, "message": {"type": "string"}}}, "BaseResultOfAuthorityGroupDetailVO": {"title": "BaseResultOfAuthorityGroupDetailVO", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/AuthorityGroupDetailVO"}, "message": {"type": "string"}}}, "BaseResultOfEmployeeVO": {"title": "BaseResultOfEmployeeVO", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/EmployeeVO"}, "message": {"type": "string"}}}, "BaseResultOfFileAttachment": {"title": "BaseResultOfFileAttachment", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/FileAttachment"}, "message": {"type": "string"}}}, "BaseResultOfJobPosition": {"title": "BaseResultOfJobPosition", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/JobPosition"}, "message": {"type": "string"}}}, "BaseResultOfJobPositionVO": {"title": "BaseResultOfJobPositionVO", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/JobPositionVO"}, "message": {"type": "string"}}}, "BaseResultOfListOfAuthorityGroupCheckBoxVO": {"title": "BaseResultOfListOfAuthorityGroupCheckBoxVO", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/AuthorityGroupCheckBoxVO"}}, "message": {"type": "string"}}}, "BaseResultOfListOfAuthorityGroupManagerVO": {"title": "BaseResultOfListOfAuthorityGroupManagerVO", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/AuthorityGroupManagerVO"}}, "message": {"type": "string"}}}, "BaseResultOfListOfAuthorityGroupVO": {"title": "BaseResultOfListOfAuthorityGroupVO", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/AuthorityGroupVO"}}, "message": {"type": "string"}}}, "BaseResultOfListOfEmployeeVO": {"title": "BaseResultOfListOfEmployeeVO", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/EmployeeVO"}}, "message": {"type": "string"}}}, "BaseResultOfListOfFeatureSubsystemVO": {"title": "BaseResultOfListOfFeatureSubsystemVO", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/FeatureSubsystemVO"}}, "message": {"type": "string"}}}, "BaseResultOfListOfFileAttachment": {"title": "BaseResultOfListOfFileAttachment", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/FileAttachment"}}, "message": {"type": "string"}}}, "BaseResultOfListOfJobPositionComboVO": {"title": "BaseResultOfListOfJobPositionComboVO", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/JobPositionComboVO"}}, "message": {"type": "string"}}}, "BaseResultOfListOfJobPositionVO": {"title": "BaseResultOfListOfJobPositionVO", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/JobPositionVO"}}, "message": {"type": "string"}}}, "BaseResultOfListOfOrganization": {"title": "BaseResultOfListOfOrganization", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/Organization"}}, "message": {"type": "string"}}}, "BaseResultOfListOfOrganizationTreeVO": {"title": "BaseResultOfListOfOrganizationTreeVO", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/OrganizationTreeVO"}}, "message": {"type": "string"}}}, "BaseResultOfListOfRoleAuthorizedUserVO": {"title": "BaseResultOfListOfRoleAuthorizedUserVO", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/RoleAuthorizedUserVO"}}, "message": {"type": "string"}}}, "BaseResultOfListOfRoleDetailVO": {"title": "BaseResultOfListOfRoleDetailVO", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/RoleDetailVO"}}, "message": {"type": "string"}}}, "BaseResultOfListOfRoleTreeVO": {"title": "BaseResultOfListOfRoleTreeVO", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/RoleTreeVO"}}, "message": {"type": "string"}}}, "BaseResultOfListOfRoleVO": {"title": "BaseResultOfListOfRoleVO", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/RoleVO"}}, "message": {"type": "string"}}}, "BaseResultOfListOfUserAllVO": {"title": "BaseResultOfListOfUserAllVO", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/UserAllVO"}}, "message": {"type": "string"}}}, "BaseResultOfListOfUserEmployeeVO": {"title": "BaseResultOfListOfUserEmployeeVO", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/UserEmployeeVO"}}, "message": {"type": "string"}}}, "BaseResultOfListOfUserVO": {"title": "BaseResultOfListOfUserVO", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/UserVO"}}, "message": {"type": "string"}}}, "BaseResultOfLoginUserDetails": {"title": "BaseResultOfLoginUserDetails", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/LoginUserDetails"}, "message": {"type": "string"}}}, "BaseResultOfMapOfstringAndUserEmpIdVO": {"title": "BaseResultOfMapOfstringAndUserEmpIdVO", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/UserEmpIdVO"}}, "message": {"type": "string"}}}, "BaseResultOfOrganizationVO": {"title": "BaseResultOfOrganizationVO", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/OrganizationVO"}, "message": {"type": "string"}}}, "BaseResultOfRole": {"title": "BaseResultOfRole", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/Role"}, "message": {"type": "string"}}}, "BaseResultOfRoleDetailVO": {"title": "BaseResultOfRoleDetailVO", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/RoleDetailVO"}, "message": {"type": "string"}}}, "BaseResultOfRoleUserVO": {"title": "BaseResultOfRoleUserVO", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/RoleUserVO"}, "message": {"type": "string"}}}, "BaseResultOfUserCenterTenantVO": {"title": "BaseResultOfUserCenterTenantVO", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/UserCenterTenantVO"}, "message": {"type": "string"}}}, "BaseResultOfUserDetailVO": {"title": "BaseResultOfUserDetailVO", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/UserDetailVO"}, "message": {"type": "string"}}}, "BaseResultOfUserEnterpriseAdminVO": {"title": "BaseResultOfUserEnterpriseAdminVO", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/UserEnterpriseAdminVO"}, "message": {"type": "string"}}}, "BaseResultOfUserVO": {"title": "BaseResultOfUserVO", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/UserVO"}, "message": {"type": "string"}}}, "BaseResultOfboolean": {"title": "BaseResultOfboolean", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "boolean"}, "message": {"type": "string"}}}, "BaseResultOfobject": {"title": "BaseResultOfobject", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "object"}, "message": {"type": "string"}}}, "BaseResultOfstring": {"title": "BaseResultOfstring", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "string"}, "message": {"type": "string"}}}, "EmployeeDTO": {"title": "EmployeeDTO", "required": ["belongToOrg", "gender", "name"], "type": "object", "properties": {"belongToOrg": {"type": "integer", "description": "所属机构的id", "format": "int64"}, "birthday": {"type": "string", "description": "人员的出生日期", "format": "date"}, "dingtalk": {"type": "string", "description": "钉钉账号"}, "emailAddress": {"type": "string", "description": "人员的电子邮件地址"}, "employeeCode": {"type": "string", "description": "员工的编号"}, "gender": {"type": "integer", "description": "人员的性别，1表示男性，2表示女性", "format": "int32"}, "graduateFrom": {"type": "string", "description": "人员的毕业院校"}, "id": {"type": "integer", "description": "id", "format": "int64"}, "idCard": {"type": "string", "description": "身份证号"}, "jobNumber": {"type": "string", "description": "人员工号"}, "jobPosition": {"type": "string", "description": "人员的岗位"}, "jobTitle": {"type": "string", "description": "员工的职务"}, "name": {"type": "string", "description": "人员的姓名"}, "phone": {"type": "string", "description": "人员的电话"}, "photoRul": {"type": "string", "description": "人员头像的地址"}, "politicalStatus": {"type": "string", "description": "人员的政治面貌"}, "status": {"type": "integer", "description": "人员当前的状态，例如在职、离职、停职等", "format": "int32"}, "syncStatus": {"type": "integer", "description": "钉钉同步状态(0-未同步、1-同步成功、2-同步失败、3-已从钉钉删除)", "format": "int32"}, "workTerminal": {"type": "string", "description": "工作航站(多个逗号分隔)", "example": "CTU,PEK"}, "wxOpenid": {"type": "string", "description": "微信OpenId"}}, "description": "系统所管理的人员"}, "EmployeeOrgDTO": {"title": "EmployeeOrgDTO", "required": ["employeeIds", "orgId"], "type": "object", "properties": {"employeeIds": {"type": "array", "description": "所有需要修改机构的人员ID", "items": {"type": "integer", "format": "int64"}}, "orgId": {"type": "integer", "description": "目标机构ID", "format": "int64"}}, "description": "批量修改人员所属机构的数据传输对象"}, "EmployeePagedDTO": {"title": "EmployeePagedDTO", "type": "object", "description": "系统所管理的人员"}, "EmployeeUserDTO": {"title": "EmployeeUserDTO", "type": "object", "properties": {"autoUserName": {"type": "boolean", "description": "系统生成用户名", "example": false}, "employee": {"description": "人员信息对象", "$ref": "#/components/schemas/EmployeeDTO"}, "isCreateUser": {"type": "boolean", "description": "是否创建用户", "example": false}, "user": {"description": "用户信息对象", "$ref": "#/components/schemas/UserAddDTO"}}}, "EmployeeVO": {"title": "EmployeeVO", "required": ["belongToOrg", "gender", "name"], "type": "object", "properties": {"belongToOrg": {"type": "integer", "description": "所属机构的id", "format": "int64"}, "birthday": {"type": "string", "description": "人员的出生日期", "format": "date"}, "dingtalk": {"type": "string", "description": "钉钉账号"}, "emailAddress": {"type": "string", "description": "人员的电子邮件地址"}, "employeeCode": {"type": "string", "description": "员工的编号"}, "gender": {"type": "integer", "description": "人员的性别，1表示男性，2表示女性", "format": "int32"}, "graduateFrom": {"type": "string", "description": "人员的毕业院校"}, "id": {"type": "integer", "description": "id", "format": "int64"}, "idCard": {"type": "string", "description": "身份证号"}, "jobNumber": {"type": "string", "description": "人员工号"}, "jobPosition": {"type": "string", "description": "人员的岗位"}, "jobTitle": {"type": "string", "description": "员工的职务"}, "name": {"type": "string", "description": "人员的姓名"}, "organizationName": {"type": "string", "description": "所属机构名称"}, "phone": {"type": "string", "description": "人员的电话"}, "photoRul": {"type": "string", "description": "人员头像的地址"}, "politicalStatus": {"type": "string", "description": "人员的政治面貌"}, "positionName": {"type": "string", "description": "岗位名称"}, "positionNameList": {"type": "array", "description": "岗位名称集合", "items": {"type": "string"}}, "status": {"type": "integer", "description": "人员当前的状态，例如在职、离职、停职等", "format": "int32"}, "syncStatus": {"type": "integer", "description": "钉钉同步状态(0-未同步、1-同步成功、2-同步失败、3-已从钉钉删除)", "format": "int32"}, "workTerminal": {"type": "string", "description": "工作航站(多个逗号分隔)", "example": "CTU,PEK"}, "workTerminalList": {"type": "array", "description": "工作场站名称", "items": {"type": "string"}}, "wxOpenid": {"type": "string", "description": "微信OpenId"}}, "description": "系统所管理的人员"}, "FeatureResourceDeleteDTO": {"title": "FeatureResourceDeleteDTO", "required": ["idList"], "type": "object", "properties": {"idList": {"type": "array", "description": "权限组ID集合", "items": {"type": "integer", "format": "int64"}}}, "description": "系统功能资源删除参数接收对象，包括资源ID数组"}, "FeatureResourceTreeVO": {"title": "FeatureResourceTreeVO", "type": "object", "properties": {"children": {"type": "array", "description": "子节点", "items": {"$ref": "#/components/schemas/FeatureResourceTreeVO"}}, "icon": {"type": "string", "description": "资源图标的url"}, "name": {"type": "string", "description": "资源的名称"}, "routeUrl": {"type": "string", "description": "资源的路由地址，这里指的是前端页面的路由地址"}, "sortOrder": {"type": "integer", "description": "资源的排序，代表兄弟资源之间显示的先后顺序", "format": "int32"}, "subsystemCode": {"type": "string", "description": "子系统编码"}, "type": {"type": "integer", "description": "资源的类型，包括1:子系统、2：菜单、3：操作【1:子系统, 2:菜单, 3:操作】", "format": "int32", "enum": [1, 2, 3]}}, "description": "系统资源树返回展示对象"}, "FeatureSubsystemVO": {"title": "FeatureSubsystemVO", "type": "object", "properties": {"clientName": {"type": "string", "description": "终端类型名称"}, "clientType": {"type": "integer", "description": "终端类型", "format": "int32"}, "createdBy": {"type": "string", "description": "创建者"}, "createdTime": {"type": "string", "description": "创建时间", "format": "date-time"}, "icon": {"type": "string", "description": "图标"}, "id": {"type": "integer", "description": "id", "format": "int64"}, "name": {"type": "string", "description": "子系统名称"}, "routeUrl": {"type": "string", "description": "访问地址"}, "subsystemCode": {"type": "string", "description": "子系统编码"}, "updatedBy": {"type": "string", "description": "更新者"}, "updatedTime": {"type": "string", "description": "更新时间", "format": "date-time"}}, "description": "子系统类型资源对象，用于菜单管理的条件查询下拉框取值"}, "FileAttachment": {"title": "FileAttachment", "type": "object", "properties": {"bucketName": {"type": "string", "description": "使用minio时文件存储的桶名称"}, "businessId": {"type": "integer", "description": "归属业务ID", "format": "int64"}, "businessType": {"type": "string", "description": "归属业务类型"}, "fileCode": {"type": "string", "description": "文件编号"}, "fileExtension": {"type": "string", "description": "文件扩展名"}, "fileMeta": {"type": "string", "description": "文件信息"}, "fileName": {"type": "string", "description": "文件名"}, "filePath": {"type": "string", "description": "文件路径"}, "fileSize": {"type": "integer", "description": "文件大小", "format": "int64"}, "fileType": {"type": "string", "description": "文件类型"}, "id": {"type": "integer", "description": "id", "format": "int64"}, "originalName": {"type": "string", "description": "文件原名"}, "remarks": {"type": "string", "description": "备注"}}, "description": "文件记录表实体类"}, "JobPosition": {"title": "JobPosition", "type": "object", "properties": {"id": {"type": "integer", "description": "id", "format": "int64"}, "positionCode": {"type": "string", "description": "岗位代码"}, "positionName": {"type": "string", "description": "岗位名称"}, "positionType": {"type": "integer", "description": "岗位类型", "format": "int32"}, "remark": {"type": "string", "description": "岗位描述"}}, "description": "岗位信息表"}, "JobPositionComboVO": {"title": "JobPositionComboVO", "type": "object", "properties": {"jobPositionVOList": {"type": "array", "description": "岗位集合", "items": {"$ref": "#/components/schemas/JobPositionVO"}}, "jobType": {"type": "integer", "description": "岗位类型值", "format": "int32"}, "jobTypeName": {"type": "string", "description": "岗位类型名称"}}, "description": "岗位下拉选择框对象"}, "JobPositionDTO": {"title": "JobPositionDTO", "type": "object", "properties": {"id": {"type": "integer", "description": "id", "format": "int64"}, "positionCode": {"type": "string", "description": "岗位代码"}, "positionName": {"type": "string", "description": "岗位名称"}, "positionType": {"type": "integer", "description": "岗位类型", "format": "int32"}, "remark": {"type": "string", "description": "岗位描述"}}, "description": "岗位信息表"}, "JobPositionDeleteDTO": {"title": "JobPositionDeleteDTO", "type": "object", "properties": {"idList": {"type": "array", "description": "id集合", "items": {"type": "integer", "format": "int64"}}}, "description": "岗位删除对象"}, "JobPositionPagedDTO": {"title": "JobPositionPagedDTO", "type": "object", "properties": {"isAsc": {"type": "string", "description": "排序方式，desc或asc"}, "items": {"type": "array", "description": "排序对象，包含排序列和方式，desc或asc", "items": {"$ref": "#/components/schemas/OrderItem"}}, "obc": {"type": "string", "description": "排序列名(orderByColumn)"}, "pageNumber": {"type": "integer", "description": "要查询的页号", "format": "int64", "example": 1}, "pageSize": {"type": "integer", "description": "每页包含的数据的条数", "format": "int64", "example": 10}, "params": {"type": "object"}, "positionCode": {"type": "string", "description": "岗位代码"}, "positionName": {"type": "string", "description": "岗位名称"}, "positionType": {"type": "integer", "description": "岗位类型", "format": "int32"}}, "description": "岗位信息表"}, "JobPositionVO": {"title": "JobPositionVO", "type": "object", "properties": {"id": {"type": "integer", "description": "id", "format": "int64"}, "positionCode": {"type": "string", "description": "岗位代码"}, "positionName": {"type": "string", "description": "岗位名称"}, "positionType": {"type": "integer", "description": "岗位类型", "format": "int32"}, "remark": {"type": "string", "description": "岗位描述"}}, "description": "岗位信息表"}, "LabelVO": {"title": "LabelVO", "type": "object", "properties": {"id": {"type": "integer", "description": "id", "format": "int64"}, "labelValues": {"type": "array", "description": "标签所有选项", "items": {"$ref": "#/components/schemas/LabelValueCandidateVO"}}, "name": {"type": "string", "description": "标签的名称"}, "selected": {"type": "boolean", "description": "用户是否有该标签", "example": false}, "type": {"type": "integer", "description": "标签项的类型，是单选标签还是多选标签", "format": "int32"}}, "description": "系统中可用的标签项"}, "LabelValueCandidateVO": {"title": "LabelValueCandidateVO", "type": "object", "properties": {"id": {"type": "integer", "description": "id", "format": "int64"}, "name": {"type": "string", "description": "标签值的显示名称"}, "selected": {"type": "boolean", "description": "用户是否有该标签值", "example": false}, "value": {"type": "string", "description": "可选值"}}, "description": "系统中标签的可选值"}, "LoginUserDetails": {"title": "LoginUserDetails", "type": "object"}, "OrderItem": {"title": "OrderItem", "type": "object", "properties": {"asc": {"type": "boolean"}, "column": {"type": "string"}}}, "Organization": {"title": "Organization", "type": "object", "properties": {"address": {"maxLength": 32, "minLength": 0, "type": "string", "description": "机构的地址"}, "businessType": {"type": "integer", "description": "机构所从事的业务的类别", "format": "int64"}, "code": {"maxLength": 8, "minLength": 0, "type": "string", "description": "机构的编码，例如PSC"}, "id": {"type": "integer", "description": "id", "format": "int64"}, "industryType": {"type": "integer", "description": "机构所属的行业", "format": "int64"}, "introduction": {"maxLength": 65, "minLength": 0, "type": "string", "description": "机构的简介"}, "name": {"maxLength": 21, "minLength": 0, "type": "string", "description": "组织机构的全称"}, "nameEn": {"maxLength": 64, "minLength": 0, "type": "string", "description": "机构的英文名称"}, "nameFullPath": {"type": "string", "description": "name全路径"}, "parentId": {"type": "integer", "description": "父机构ID", "format": "int64"}, "personInCharge": {"maxLength": 20, "minLength": 0, "type": "string", "description": "机构的负责人"}, "phone": {"maxLength": 20, "minLength": 0, "type": "string", "description": "机构的电话"}, "serialNo": {"maxLength": 32, "minLength": 0, "type": "string", "description": "机构的代码，由代码生成"}, "shortName": {"maxLength": 16, "minLength": 0, "type": "string", "description": "机构名称的简称"}, "sortOrder": {"type": "integer", "description": "排序号", "format": "int32"}, "syncStatus": {"type": "integer", "description": "钉钉同步状态(0-未同步、1-同步成功、2-同步失败、3-已从钉钉删除)", "format": "int32"}, "type": {"type": "integer", "description": "机构的类型", "format": "int32"}}, "description": "组织机构，包括根、公司、部门等等"}, "OrganizationDTO": {"title": "OrganizationDTO", "type": "object", "properties": {"address": {"maxLength": 32, "minLength": 0, "type": "string", "description": "机构的地址"}, "businessType": {"type": "integer", "description": "机构所从事的业务的类别", "format": "int64"}, "code": {"maxLength": 8, "minLength": 0, "type": "string", "description": "机构的编码，例如PSC"}, "id": {"type": "integer", "description": "id", "format": "int64"}, "industryType": {"type": "integer", "description": "机构所属的行业", "format": "int64"}, "introduction": {"maxLength": 65, "minLength": 0, "type": "string", "description": "机构的简介"}, "name": {"maxLength": 21, "minLength": 0, "type": "string", "description": "组织机构的全称"}, "nameEn": {"maxLength": 64, "minLength": 0, "type": "string", "description": "机构的英文名称"}, "nameFullPath": {"type": "string", "description": "name全路径"}, "parentId": {"type": "integer", "description": "父机构ID", "format": "int64"}, "personInCharge": {"maxLength": 20, "minLength": 0, "type": "string", "description": "机构的负责人"}, "phone": {"maxLength": 20, "minLength": 0, "type": "string", "description": "机构的电话"}, "serialNo": {"maxLength": 32, "minLength": 0, "type": "string", "description": "机构的代码，由代码生成"}, "shortName": {"maxLength": 16, "minLength": 0, "type": "string", "description": "机构名称的简称"}, "sortOrder": {"type": "integer", "description": "排序号", "format": "int32"}, "syncStatus": {"type": "integer", "description": "钉钉同步状态(0-未同步、1-同步成功、2-同步失败、3-已从钉钉删除)", "format": "int32"}, "type": {"type": "integer", "description": "机构的类型", "format": "int32"}}, "description": "组织机构，包括根、公司、部门等等"}, "OrganizationPagedDTO": {"title": "OrganizationPagedDTO", "type": "object", "properties": {"code": {"type": "string", "description": "机构的编码，例如PSC"}, "id": {"type": "integer", "description": "组织ID", "format": "int64"}, "isAsc": {"type": "string", "description": "排序方式，desc或asc"}, "items": {"type": "array", "description": "排序对象，包含排序列和方式，desc或asc", "items": {"$ref": "#/components/schemas/OrderItem"}}, "name": {"type": "string", "description": "组织机构的全称"}, "nameEn": {"type": "string", "description": "机构的英文名称"}, "obc": {"type": "string", "description": "排序列名(orderByColumn)"}, "pageNumber": {"type": "integer", "description": "要查询的页号", "format": "int64", "example": 1}, "pageSize": {"type": "integer", "description": "每页包含的数据的条数", "format": "int64", "example": 10}, "params": {"type": "object"}, "shortName": {"type": "string", "description": "机构名称的简称"}, "type": {"type": "integer", "description": "机构的类型", "format": "int32"}}, "description": "机构分页对象"}, "OrganizationTreeVO": {"title": "OrganizationTreeVO", "type": "object", "properties": {"code": {"type": "string", "description": "机构的编码，例如PSC"}, "id": {"type": "integer", "description": "id", "format": "int64"}, "isCA": {"type": "integer", "description": "当前节点是否存在CA管理员", "format": "int32"}, "isFirst": {"type": "integer", "description": "是否一级机构 1是0否", "format": "int32"}, "name": {"type": "string", "description": "组织机构的全称"}, "nameEn": {"type": "string", "description": "机构的英文名称"}, "option": {"type": "string", "description": "是否可以操作标识0否1是"}, "parentId": {"type": "integer", "description": "父机构ID", "format": "int64"}, "serialNo": {"type": "string", "description": "机构的代码，由代码生成"}, "shortName": {"type": "string", "description": "机构名称的简称"}, "sortOrder": {"type": "integer", "description": "排序号", "format": "int32"}, "type": {"type": "integer", "description": "机构的类型", "format": "int32"}}, "description": "组织机构树返回展示对象"}, "OrganizationVO": {"title": "OrganizationVO", "type": "object", "properties": {"address": {"maxLength": 32, "minLength": 0, "type": "string", "description": "机构的地址"}, "businessType": {"type": "integer", "description": "机构所从事的业务的类别", "format": "int64"}, "child": {"type": "array", "description": "机构子节点", "items": {"$ref": "#/components/schemas/OrganizationVO"}}, "code": {"maxLength": 8, "minLength": 0, "type": "string", "description": "机构的编码，例如PSC"}, "id": {"type": "integer", "description": "id", "format": "int64"}, "industryType": {"type": "integer", "description": "机构所属的行业", "format": "int64"}, "introduction": {"maxLength": 65, "minLength": 0, "type": "string", "description": "机构的简介"}, "name": {"maxLength": 21, "minLength": 0, "type": "string", "description": "组织机构的全称"}, "nameEn": {"maxLength": 64, "minLength": 0, "type": "string", "description": "机构的英文名称"}, "nameFullPath": {"type": "string", "description": "name全路径"}, "parentId": {"type": "integer", "description": "父机构ID", "format": "int64"}, "peopleNum": {"type": "integer", "description": "人数", "format": "int32"}, "personInCharge": {"maxLength": 20, "minLength": 0, "type": "string", "description": "机构的负责人"}, "phone": {"maxLength": 20, "minLength": 0, "type": "string", "description": "机构的电话"}, "serialNo": {"maxLength": 32, "minLength": 0, "type": "string", "description": "机构的代码，由代码生成"}, "shortName": {"maxLength": 16, "minLength": 0, "type": "string", "description": "机构名称的简称"}, "sortOrder": {"type": "integer", "description": "排序号", "format": "int32"}, "syncStatus": {"type": "integer", "description": "钉钉同步状态(0-未同步、1-同步成功、2-同步失败、3-已从钉钉删除)", "format": "int32"}, "type": {"type": "integer", "description": "机构的类型", "format": "int32"}}, "description": "组织机构，包括根、公司、部门等等"}, "PagedResultOfListOfAuthorityGroupPageDetailVO": {"title": "PagedResultOfListOfAuthorityGroupPageDetailVO", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "currentPage": {"type": "integer", "description": "当前是第几页的数据", "format": "int64"}, "data": {"type": "array", "description": "当前页包含的数据内容", "items": {"$ref": "#/components/schemas/AuthorityGroupPageDetailVO"}}, "message": {"type": "string"}, "pageSize": {"type": "integer", "description": "每页包含的数据条数", "format": "int64"}, "totalPages": {"type": "integer", "description": "总共有多少页数据", "format": "int64"}, "totalRecords": {"type": "integer", "description": "总共有多少条数据", "format": "int64"}}}, "PagedResultOfListOfEmployeeVO": {"title": "PagedResultOfListOfEmployeeVO", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "currentPage": {"type": "integer", "description": "当前是第几页的数据", "format": "int64"}, "data": {"type": "array", "description": "当前页包含的数据内容", "items": {"$ref": "#/components/schemas/EmployeeVO"}}, "message": {"type": "string"}, "pageSize": {"type": "integer", "description": "每页包含的数据条数", "format": "int64"}, "totalPages": {"type": "integer", "description": "总共有多少页数据", "format": "int64"}, "totalRecords": {"type": "integer", "description": "总共有多少条数据", "format": "int64"}}}, "PagedResultOfListOfJobPositionVO": {"title": "PagedResultOfListOfJobPositionVO", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "currentPage": {"type": "integer", "description": "当前是第几页的数据", "format": "int64"}, "data": {"type": "array", "description": "当前页包含的数据内容", "items": {"$ref": "#/components/schemas/JobPositionVO"}}, "message": {"type": "string"}, "pageSize": {"type": "integer", "description": "每页包含的数据条数", "format": "int64"}, "totalPages": {"type": "integer", "description": "总共有多少页数据", "format": "int64"}, "totalRecords": {"type": "integer", "description": "总共有多少条数据", "format": "int64"}}}, "PagedResultOfListOfOrganizationVO": {"title": "PagedResultOfListOfOrganizationVO", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "currentPage": {"type": "integer", "description": "当前是第几页的数据", "format": "int64"}, "data": {"type": "array", "description": "当前页包含的数据内容", "items": {"$ref": "#/components/schemas/OrganizationVO"}}, "message": {"type": "string"}, "pageSize": {"type": "integer", "description": "每页包含的数据条数", "format": "int64"}, "totalPages": {"type": "integer", "description": "总共有多少页数据", "format": "int64"}, "totalRecords": {"type": "integer", "description": "总共有多少条数据", "format": "int64"}}}, "PagedResultOfListOfRoleVO": {"title": "PagedResultOfListOfRoleVO", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "currentPage": {"type": "integer", "description": "当前是第几页的数据", "format": "int64"}, "data": {"type": "array", "description": "当前页包含的数据内容", "items": {"$ref": "#/components/schemas/RoleVO"}}, "message": {"type": "string"}, "pageSize": {"type": "integer", "description": "每页包含的数据条数", "format": "int64"}, "totalPages": {"type": "integer", "description": "总共有多少页数据", "format": "int64"}, "totalRecords": {"type": "integer", "description": "总共有多少条数据", "format": "int64"}}}, "PagedResultOfListOfUserEmployeeVO": {"title": "PagedResultOfListOfUserEmployeeVO", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "currentPage": {"type": "integer", "description": "当前是第几页的数据", "format": "int64"}, "data": {"type": "array", "description": "当前页包含的数据内容", "items": {"$ref": "#/components/schemas/UserEmployeeVO"}}, "message": {"type": "string"}, "pageSize": {"type": "integer", "description": "每页包含的数据条数", "format": "int64"}, "totalPages": {"type": "integer", "description": "总共有多少页数据", "format": "int64"}, "totalRecords": {"type": "integer", "description": "总共有多少条数据", "format": "int64"}}}, "PagedResultOfListOfUserVO": {"title": "PagedResultOfListOfUserVO", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "currentPage": {"type": "integer", "description": "当前是第几页的数据", "format": "int64"}, "data": {"type": "array", "description": "当前页包含的数据内容", "items": {"$ref": "#/components/schemas/UserVO"}}, "message": {"type": "string"}, "pageSize": {"type": "integer", "description": "每页包含的数据条数", "format": "int64"}, "totalPages": {"type": "integer", "description": "总共有多少页数据", "format": "int64"}, "totalRecords": {"type": "integer", "description": "总共有多少条数据", "format": "int64"}}}, "Role": {"title": "Role", "type": "object", "properties": {"description": {"type": "string", "description": "角色的描述信息"}, "id": {"type": "integer", "description": "id", "format": "int64"}, "name": {"type": "string", "description": "角色的名称，必须唯一"}, "parentId": {"type": "integer", "description": "父角色ID", "format": "int64"}, "path": {"type": "string", "description": "从根节点到当前节点的路径;不包含当前节点，例如：/1/2/3"}, "status": {"type": "integer", "description": "角色的状态，例如启用，停用等【1:启用, 0:停用, 2:锁定, 3:过期, 4:未启用】", "format": "int32", "enum": [0, 1, 2, 3, 4]}}, "description": "用于授权的角色"}, "RoleAddDTO": {"title": "RoleAddDTO", "type": "object", "properties": {"description": {"maxLength": 64, "minLength": 0, "type": "string", "description": "角色的描述信息"}, "featureResourceId": {"type": "array", "description": "角色分配的资源权限", "items": {"type": "integer", "format": "int64"}}, "name": {"maxLength": 32, "minLength": 1, "type": "string", "description": "角色的名称，同一个父角色下必须唯一"}, "parentId": {"type": "integer", "description": "父角色ID", "format": "int64"}, "status": {"type": "integer", "description": "角色状态;1:启用,0:停用【1:启用, 0:停用, 2:锁定, 3:过期, 4:未启用】", "format": "int32", "enum": [0, 1, 2, 3, 4]}, "userIdList": {"type": "array", "description": "管理员id集合", "items": {"type": "integer", "format": "int64"}}}, "description": "用于授权的角色"}, "RoleAuthorityGroupVO": {"title": "RoleAuthorityGroupVO", "type": "object", "properties": {"id": {"type": "integer", "description": "角色id", "format": "int64"}, "name": {"type": "string", "description": "角色名"}}, "description": "权限组下的角色对象"}, "RoleAuthorizationCheckDTO": {"title": "RoleAuthorizationCheckDTO", "required": ["roleIdList"], "type": "object", "properties": {"roleIdList": {"type": "array", "description": "角色ID集合", "items": {"type": "integer", "format": "int64"}}}, "description": "角色授权检查的接收对象，包括角色id集合"}, "RoleAuthorizeUserDTO": {"title": "RoleAuthorizeUserDTO", "required": ["arrUserId", "roleId"], "type": "object", "properties": {"arrUserId": {"type": "array", "description": "用户ID数组", "items": {"type": "integer", "format": "int64"}}, "roleId": {"type": "integer", "description": "角色ID", "format": "int64"}}, "description": "用于为角色分配授权用户"}, "RoleAuthorizedUserVO": {"title": "RoleAuthorizedUserVO", "type": "object", "properties": {"employeeName": {"type": "string", "description": "用户对应人员的姓名"}, "id": {"type": "integer", "description": "用户id", "format": "int64"}, "jobNumber": {"type": "string", "description": "用户工号"}, "name": {"type": "string", "description": "用户名"}}, "description": "角色已分配用户对象"}, "RoleChangeStatusDTO": {"title": "RoleChangeStatusDTO", "required": ["arrId", "status"], "type": "object", "properties": {"arrId": {"type": "array", "description": "角色ID集合", "items": {"type": "integer", "format": "int64"}}, "status": {"type": "integer", "description": "状态值,1-启用或0-停用【1:启用, 0:停用, 2:锁定, 3:过期, 4:未启用】", "format": "int32", "enum": [0, 1, 2, 3, 4]}}, "description": "用于批量修改角色状态的对象"}, "RoleDetailVO": {"title": "RoleDetailVO", "type": "object", "properties": {"description": {"type": "string", "description": "角色的描述信息"}, "id": {"type": "integer", "description": "id", "format": "int64"}, "managerList": {"type": "array", "description": "管理员列表", "items": {"$ref": "#/components/schemas/RoleManagerVO"}}, "name": {"type": "string", "description": "角色的名称，必须唯一"}, "parentId": {"type": "integer", "description": "父角色ID", "format": "int64"}, "parentName": {"type": "string", "description": "父角色名称"}, "parentStatus": {"type": "integer", "description": "父角色状态【1:启用, 0:停用, 2:锁定, 3:过期, 4:未启用】", "format": "int32", "enum": [0, 1, 2, 3, 4]}, "path": {"type": "string", "description": "从根节点到当前节点的路径;不包含当前节点，例如：/1/2/3"}, "status": {"type": "integer", "description": "角色的状态，例如启用，停用等【1:启用, 0:停用, 2:锁定, 3:过期, 4:未启用】", "format": "int32", "enum": [0, 1, 2, 3, 4]}}, "description": "角色详情展示数据对象"}, "RoleGroupVO": {"title": "RoleGroupVO", "type": "object", "properties": {"id": {"type": "integer", "description": "id", "format": "int64"}, "parentName": {"type": "string", "description": "父角色名称"}, "roleName": {"type": "string", "description": "角色名称"}}, "description": "带有父角色信息的角色对象"}, "RoleManagerVO": {"title": "RoleManagerVO", "type": "object", "properties": {"employeeName": {"type": "string", "description": "对应人员姓名"}, "id": {"type": "integer", "description": "用户id", "format": "int64"}, "name": {"type": "string", "description": "用户名"}, "roleId": {"type": "integer", "description": "角色id", "format": "int64"}}, "description": "角色已分配的管理员对象"}, "RolePagedDTO": {"title": "RolePagedDTO", "type": "object", "properties": {"createTimestampEnd": {"type": "integer", "description": "创建时间结束(时间戳,精确到秒)", "format": "int64"}, "createTimestampStart": {"type": "integer", "description": "创建时间开始(时间戳,精确到秒)", "format": "int64"}, "isAsc": {"type": "string", "description": "排序方式，desc或asc"}, "items": {"type": "array", "description": "排序对象，包含排序列和方式，desc或asc", "items": {"$ref": "#/components/schemas/OrderItem"}}, "name": {"type": "string", "description": "角色名或角色描述，支持模糊查询"}, "obc": {"type": "string", "description": "排序列名(orderByColumn)"}, "pageNumber": {"type": "integer", "description": "要查询的页号", "format": "int64", "example": 1}, "pageSize": {"type": "integer", "description": "每页包含的数据的条数", "format": "int64", "example": 10}, "params": {"type": "object"}, "parentId": {"type": "integer", "description": "父角色ID", "format": "int64"}, "status": {"type": "integer", "description": "角色的状态，例如1：启用，0：停用", "format": "int32", "enum": [0, 1]}}, "description": "用于授权的角色"}, "RoleSearchDTO": {"title": "RoleSearchDTO", "required": ["idList"], "type": "object", "properties": {"idList": {"type": "array", "description": "角色ID集合", "items": {"type": "integer", "format": "int64"}}}, "description": "用于批量查询角色信息的对象"}, "RoleTreeVO": {"title": "RoleTreeVO", "type": "object", "properties": {"auth": {"type": "boolean", "description": "是否可以操作，true:是,false:否", "example": false}, "id": {"type": "integer", "description": "id", "format": "int64"}, "name": {"type": "string", "description": "角色名称"}, "parentId": {"type": "integer", "description": "父角色ID", "format": "int64"}}, "description": "角色树返回展示对象"}, "RoleUpdateDTO": {"title": "RoleUpdateDTO", "type": "object", "properties": {"description": {"maxLength": 64, "minLength": 0, "type": "string", "description": "角色的描述信息"}, "featureResourceId": {"type": "array", "description": "角色分配的资源权限", "items": {"type": "integer", "format": "int64"}}, "id": {"type": "integer", "description": "角色ID", "format": "int64"}, "name": {"maxLength": 32, "minLength": 0, "type": "string", "description": "角色的名称，同一个父角色下必须唯一"}, "status": {"type": "integer", "description": "角色状态;1:启用,0:停用【1:启用, 0:停用, 2:锁定, 3:过期, 4:未启用】", "format": "int32", "enum": [0, 1, 2, 3, 4]}, "userIdList": {"type": "array", "description": "管理员id集合", "items": {"type": "integer", "format": "int64"}}}, "description": "角色更新对象"}, "RoleUserDTO": {"title": "RoleUserDTO", "type": "object", "properties": {"userId": {"type": "integer", "description": "用户id", "format": "int64"}}, "description": "用于查询用户或机构的已/未授权角色列表的对象"}, "RoleUserVO": {"title": "RoleUserVO", "type": "object", "properties": {"allRoleList": {"type": "array", "description": "所选机构的全部角色列表", "items": {"$ref": "#/components/schemas/RoleGroupVO"}}, "authorizedRoleList": {"type": "array", "description": "已授权角色列表", "items": {"$ref": "#/components/schemas/RoleGroupVO"}}}, "description": "编辑用户时使用的角色列表，分为未授权角色集和已授权角色集"}, "RoleVO": {"title": "RoleVO", "type": "object", "properties": {"description": {"type": "string", "description": "角色的描述信息"}, "id": {"type": "integer", "description": "id", "format": "int64"}, "name": {"type": "string", "description": "角色的名称，必须唯一"}, "parentId": {"type": "integer", "description": "父角色ID", "format": "int64"}, "parentName": {"type": "string", "description": "父角色名称"}, "parentStatus": {"type": "integer", "description": "父角色状态【1:启用, 0:停用, 2:锁定, 3:过期, 4:未启用】", "format": "int32", "enum": [0, 1, 2, 3, 4]}, "path": {"type": "string", "description": "从根节点到当前节点的路径;不包含当前节点，例如：/1/2/3"}, "status": {"type": "integer", "description": "角色的状态，例如启用，停用等【1:启用, 0:停用, 2:锁定, 3:过期, 4:未启用】", "format": "int32", "enum": [0, 1, 2, 3, 4]}}, "description": "角色列表展示数据对象"}, "UserAddDTO": {"title": "UserAddDTO", "type": "object", "properties": {"employeeId": {"type": "integer", "description": "人员的唯一标识，没有业务含义", "format": "int64"}, "employeeInfo": {"description": "人员信息", "$ref": "#/components/schemas/EmployeeDTO"}, "labels": {"type": "array", "description": "用户拥有的标签", "items": {"$ref": "#/components/schemas/UserLabelJoinDTO"}}, "manageRoles": {"type": "array", "description": "用户管理的角色", "items": {"type": "integer", "format": "int64"}}, "name": {"type": "string", "description": "用户名，可用于登录系统，必须唯一。"}, "remark": {"type": "string", "description": "备注"}, "resources": {"type": "array", "description": "用户被授予的资源(CA专用)", "items": {"type": "integer", "format": "int64"}}, "roles": {"type": "array", "description": "用户被授予的角色", "items": {"type": "integer", "format": "int64"}}, "status": {"type": "integer", "description": "用户的状态，例如0：停用，1：启用，2：锁定，3：过期等【1:启用, 0:停用, 2:锁定, 3:过期, 4:未启用】", "format": "int32", "enum": [0, 1, 2, 3, 4]}, "type": {"type": "integer", "description": "用户的类型，-1-系统管理员，1-企业管理员，2-普通管理员，3-普通用户，4-机构管理员【-1:系统管理员, 1:企业管理员, 2:普通管理员, 3:普通用户, 4:机构管理员】", "format": "int32", "enum": [-1, 1, 2, 3, 4]}, "userManageOrganization": {"type": "array", "description": "用户管理的机构", "items": {"type": "integer", "format": "int64"}}, "userWorkForOrganization": {"type": "array", "description": "用户为哪些机构工作", "items": {"type": "integer", "format": "int64"}}}, "description": "登录并使用系统的用户"}, "UserAllVO": {"title": "UserAllVO", "type": "object", "properties": {"avatar": {"type": "string", "description": "用户的头像图片url"}, "employee": {"description": "用户对应的员工对象", "$ref": "#/components/schemas/EmployeeVO"}, "employeeId": {"type": "integer", "description": "人员的唯一标识，没有业务含义", "format": "int64"}, "id": {"type": "integer", "description": "id", "format": "int64"}, "name": {"type": "string", "description": "用户名，可用于登录系统，必须唯一。"}, "organization": {"description": "用户对应的员工的部门对象", "$ref": "#/components/schemas/OrganizationVO"}, "passwordStatus": {"type": "integer", "description": "用户的密码状态，1标识是初始状态，需要修改密码【1:初始化, 2:已修改】", "format": "int32", "enum": [1, 2]}, "remark": {"type": "string", "description": "备注"}, "status": {"type": "integer", "description": "用户的状态，例如0：停用，1：启用，2：锁定，3：过期等【1:启用, 0:停用, 2:锁定, 3:过期, 4:未启用】", "format": "int32", "enum": [0, 1, 2, 3, 4]}, "type": {"type": "integer", "description": "用户的类型，-1-系统管理员，1-企业管理员，2-普通管理员，3-普通用户，4-机构管理员【-1:系统管理员, 1:企业管理员, 2:普通管理员, 3:普通用户, 4:机构管理员】", "format": "int32", "enum": [-1, 1, 2, 3, 4]}, "workStatus": {"type": "integer", "description": "null【1:上班, 2:值班, 3:休假】", "format": "int32", "enum": [1, 2, 3]}}, "description": "用户完整信息"}, "UserAuthorizeDTO": {"title": "UserAuthorizeDTO", "type": "object", "properties": {"labels": {"type": "array", "description": "用户拥有的标签", "items": {"$ref": "#/components/schemas/UserLabelJoinDTO"}}, "roleIds": {"type": "array", "description": "角色ID", "items": {"type": "integer", "format": "int64"}}, "userIds": {"type": "array", "description": "用户ID", "items": {"type": "integer", "format": "int64"}}}, "description": "用户授权DTO"}, "UserBelongToOrgDTO": {"title": "UserBelongToOrgDTO", "type": "object", "properties": {"children": {"type": "boolean", "description": "是否包含子部门", "example": false}, "name": {"type": "string", "description": "用户名或姓名或工号"}, "organizationId": {"type": "integer", "description": "归属机构ID", "format": "int64"}, "status": {"type": "integer", "description": "用户的状态，例如0：停用，1：启用，2：锁定，3：过期等【1:启用, 0:停用, 2:锁定, 3:过期, 4:未启用】", "format": "int32", "enum": [0, 1, 2, 3, 4]}, "type": {"type": "integer", "description": "用户的类型，-1-系统管理员，1-企业管理员，2-普通管理员，3-普通用户，4-机构管理员【-1:系统管理员, 1:企业管理员, 2:普通管理员, 3:普通用户, 4:机构管理员】", "format": "int32", "enum": [-1, 1, 2, 3, 4]}}, "description": "获取归属部门用户DTO"}, "UserCenterTenantVO": {"title": "UserCenterTenantVO", "type": "object", "properties": {"companyName": {"type": "string", "description": "公司名称"}, "contactor": {"type": "string", "description": "租户联系人的姓名"}, "contactorEmail": {"type": "string", "description": "租户联系人的邮箱"}, "contactorPhone": {"type": "string", "description": "租户联系人的电话"}, "emp": {"description": "人员信息", "$ref": "#/components/schemas/EmployeeVO"}, "remainingNumberOfDays": {"type": "integer", "description": "产品许可剩余天数", "format": "int64"}, "tenantCode": {"type": "string", "description": "租户编码"}, "tenantId": {"type": "integer", "description": "租户ID", "format": "int64"}, "tenantName": {"type": "string", "description": "租户名称"}, "tenantStatus": {"type": "integer", "description": "租户状态", "format": "int32"}, "type": {"type": "integer", "description": "用户的类型，-1-系统管理员，1-企业管理员，2-普通管理员，3-普通用户，4-机构管理员【-1:系统管理员, 1:企业管理员, 2:普通管理员, 3:普通用户, 4:机构管理员】", "format": "int32", "enum": [-1, 1, 2, 3, 4]}, "user": {"description": "系统用户", "$ref": "#/components/schemas/UserDetailVO"}, "validFrom": {"type": "string", "description": "租户租约有效期的起始日期", "format": "date"}, "validTo": {"type": "string", "description": "租户租约有效期的结束日期", "format": "date"}}, "description": "用户中心租户admin账号信息"}, "UserChangePasswordDTO": {"title": "UserChangePasswordDTO", "type": "object", "properties": {"newPassword": {"type": "string", "description": "新密码"}, "oldPassword": {"type": "string", "description": "原始密码"}, "userId": {"type": "integer", "description": "用户ID", "format": "int64"}}, "description": "用户修改密码DTO"}, "UserChangeStatusByNameDTO": {"title": "UserChangeStatusByNameDTO", "type": "object", "properties": {"status": {"type": "integer", "description": "用户的状态，例如0：停用，1：启用，2：锁定，3：过期等【1:启用, 0:停用, 2:锁定, 3:过期, 4:未启用】", "format": "int32", "enum": [0, 1, 2, 3, 4]}, "userName": {"type": "string", "description": "用户名"}}, "description": "用户修改状态DTO"}, "UserChangeStatusDTO": {"title": "UserChangeStatusDTO", "type": "object", "properties": {"ids": {"type": "array", "description": "用户ID", "items": {"type": "integer", "format": "int64"}}, "status": {"type": "integer", "description": "用户的状态，例如0：停用，1：启用，2：锁定，3：过期等【1:启用, 0:停用, 2:锁定, 3:过期, 4:未启用】", "format": "int32", "enum": [0, 1, 2, 3, 4]}}, "description": "用户修改状态DTO"}, "UserDetailVO": {"title": "UserDetailVO", "type": "object", "properties": {"avatar": {"type": "string", "description": "用户的头像图片url"}, "emailAddress": {"type": "string", "description": "人员的电子邮件地址"}, "employeeCode": {"type": "string", "description": "人员编码"}, "employeeId": {"type": "integer", "description": "人员的唯一标识，没有业务含义", "format": "int64"}, "employeeName": {"type": "string", "description": "人员姓名"}, "gender": {"type": "integer", "description": "人员的性别，1表示男性，2表示女性", "format": "int32"}, "id": {"type": "integer", "description": "id", "format": "int64"}, "idCard": {"type": "string", "description": "身份证号"}, "jobNumber": {"type": "string", "description": "人员工号"}, "jobPosition": {"type": "string", "description": "岗位代码(多个逗号分隔)", "example": "0001,01AB"}, "labels": {"type": "array", "description": "所有可用标签，用户有的用selected=true表示", "items": {"$ref": "#/components/schemas/LabelVO"}}, "manageOrgs": {"type": "array", "items": {"$ref": "#/components/schemas/OrganizationVO"}}, "manageRoleIds": {"type": "array", "description": "用户管理的角色", "items": {"type": "integer", "format": "int64"}}, "manageRoles": {"type": "array", "description": "用户管理角色对象集", "items": {"$ref": "#/components/schemas/RoleVO"}}, "name": {"type": "string", "description": "用户名，可用于登录系统，必须唯一。"}, "nameFullPath": {"type": "string", "description": "归属机构全路径"}, "organizationId": {"type": "integer", "description": "归属机构ID", "format": "int64"}, "organizationName": {"type": "string", "description": "归属机构"}, "organizationType": {"type": "integer", "description": "归属机构ID【0:集团, 1:公司, 2:部门】", "format": "int32", "enum": [0, 1, 2]}, "passwordStatus": {"type": "integer", "description": "用户的密码状态，1标识是初始状态，需要修改密码【1:初始化, 2:已修改】", "format": "int32", "enum": [1, 2]}, "path": {"type": "string", "description": "从根节点到当前节点的路径(不包含当前节点)， 例如(/r-1/c-13/d-25).  由代码自动生成，不展示给用户。用于后端查询或者生成树的时候使用"}, "phone": {"type": "string", "description": "人员的电话"}, "positionList": {"type": "array", "description": "岗位集合", "items": {"$ref": "#/components/schemas/JobPositionVO"}}, "remark": {"type": "string", "description": "备注"}, "resources": {"type": "array", "description": "用户被授予的资源(CA专用)", "items": {"type": "integer", "format": "int64"}}, "role": {"type": "array", "items": {"$ref": "#/components/schemas/RoleVO"}}, "roles": {"type": "array", "description": "用户的角色", "items": {"$ref": "#/components/schemas/RoleGroupVO"}}, "status": {"type": "integer", "description": "用户的状态，例如0：停用，1：启用，2：锁定，3：过期等【1:启用, 0:停用, 2:锁定, 3:过期, 4:未启用】", "format": "int32", "enum": [0, 1, 2, 3, 4]}, "type": {"type": "integer", "description": "用户的类型，-1-系统管理员，1-企业管理员，2-普通管理员，3-普通用户，4-机构管理员【-1:系统管理员, 1:企业管理员, 2:普通管理员, 3:普通用户, 4:机构管理员】", "format": "int32", "enum": [-1, 1, 2, 3, 4]}, "userManageOrganization": {"type": "array", "description": "用户管理的机构", "items": {"type": "integer", "format": "int64"}}, "userWorkForOrganization": {"type": "array", "description": "用户为哪些机构工作", "items": {"type": "integer", "format": "int64"}}, "workOrgs": {"type": "array", "items": {"$ref": "#/components/schemas/OrganizationVO"}}, "workStatus": {"type": "integer", "description": "null【1:上班, 2:值班, 3:休假】", "format": "int32", "enum": [1, 2, 3]}, "workTerminal": {"type": "string", "description": "工作航站(多个逗号分隔)", "example": "CTU,PEK"}, "wxOpenid": {"type": "string", "description": "微信OpenId"}}}, "UserEmpIdVO": {"title": "UserEmpIdVO", "type": "object", "properties": {"createdBy": {"type": "string", "description": "创建者"}, "createdTime": {"type": "string", "description": "创建时间", "format": "date-time"}, "empId": {"type": "integer", "description": "人员ID", "format": "int64"}, "id": {"type": "integer", "description": "id", "format": "int64"}, "updatedBy": {"type": "string", "description": "更新者"}, "updatedTime": {"type": "string", "description": "更新时间", "format": "date-time"}, "userId": {"type": "integer", "description": "用户ID", "format": "int64"}}, "description": "批量导入时用"}, "UserEmployeeVO": {"title": "UserEmployeeVO", "type": "object", "properties": {"createdBy": {"type": "string", "description": "创建者"}, "dingtalk": {"type": "string", "description": "钉钉账号"}, "employeeCode": {"type": "string", "description": "人员编码"}, "employeeName": {"type": "string", "description": "员工的姓名"}, "id": {"type": "integer", "format": "int64"}, "jobNumber": {"type": "string", "description": "人员工号"}, "name": {"type": "string", "description": "姓名或登录名"}, "nameFullPath": {"type": "string", "description": "机构全路径名称"}, "oneLevelId": {"type": "integer", "description": "所属机构的一级机构ID", "format": "int64"}, "orgId": {"type": "integer", "description": "所属机构ID", "format": "int64"}, "organizationName": {"type": "string", "description": "员工所属部门名称"}, "status": {"type": "integer", "description": "用户的状态，例如0：停用，1：启用，2：锁定，3：过期等【1:启用, 0:停用, 2:锁定, 3:过期, 4:未启用】", "format": "int32", "enum": [0, 1, 2, 3, 4]}, "type": {"type": "integer", "description": "用户的类型，-1-系统管理员，1-企业管理员，2-普通管理员，3-普通用户，4-机构管理员【-1:系统管理员, 1:企业管理员, 2:普通管理员, 3:普通用户, 4:机构管理员】", "format": "int32", "enum": [-1, 1, 2, 3, 4]}, "workTerminal": {"type": "string", "description": "工作航站(多个逗号分隔)", "example": "CTU,PEK"}, "wxOpenid": {"type": "string", "description": "微信Openid"}}}, "UserEnterpriseAdminVO": {"title": "UserEnterpriseAdminVO", "type": "object", "properties": {"id": {"type": "integer", "description": "id", "format": "int64"}, "name": {"type": "string", "description": "用户名，可用于登录系统，必须唯一。"}}, "description": "企业管理员VO"}, "UserForPositionDTO": {"title": "UserForPositionDTO", "type": "object", "properties": {"jobPositionCodeList": {"type": "array", "description": "岗位代码集合", "items": {"type": "string"}}}, "description": "用岗位查询角色信息的对象"}, "UserLabelJoinDTO": {"title": "UserLabelJoinDTO", "type": "object", "properties": {"sysLabelId": {"type": "integer", "description": "标签ID", "format": "int64"}, "valueIds": {"type": "array", "description": "标签值ID", "items": {"type": "string"}}, "values": {"type": "array", "description": "标签值", "items": {"type": "string"}}}, "description": "用户拥有的标签"}, "UserPagedCADTO": {"title": "UserPagedCADTO", "type": "object", "properties": {"isAsc": {"type": "string", "description": "排序方式，desc或asc"}, "items": {"type": "array", "description": "排序对象，包含排序列和方式，desc或asc", "items": {"$ref": "#/components/schemas/OrderItem"}}, "manageId": {"type": "array", "description": "管理机构ID", "items": {"type": "integer", "format": "int64"}}, "name": {"type": "string", "description": "用户名或姓名"}, "obc": {"type": "string", "description": "排序列名(orderByColumn)"}, "pageNumber": {"type": "integer", "description": "要查询的页号", "format": "int64", "example": 1}, "pageSize": {"type": "integer", "description": "每页包含的数据的条数", "format": "int64", "example": 10}, "params": {"type": "object"}, "status": {"type": "integer", "description": "用户的状态，例如0：停用，1：启用，2：锁定，3：过期等【1:启用, 0:停用, 2:锁定, 3:过期, 4:未启用】", "format": "int32", "enum": [0, 1, 2, 3, 4]}, "type": {"type": "integer", "description": "机构的类型【0:集团, 1:公司, 2:部门】", "format": "int32", "enum": [0, 1, 2]}}, "description": "CA管理员用户"}, "UserPagedDTO": {"title": "UserPagedDTO", "type": "object", "properties": {"createTimeEnd": {"type": "string", "description": "创建时间结束", "format": "date-time"}, "createTimeStart": {"type": "string", "description": "创建时间开始", "format": "date-time"}, "employeeOrganizationId": {"type": "array", "description": "人员所属机构ID", "items": {"type": "integer", "format": "int64"}}, "inManage": {"type": "boolean", "description": "是否判断在管理范围内", "example": false}, "isAsc": {"type": "string", "description": "排序方式，desc或asc"}, "items": {"type": "array", "description": "排序对象，包含排序列和方式，desc或asc", "items": {"$ref": "#/components/schemas/OrderItem"}}, "name": {"type": "string", "description": "用户名、姓名、人员邮箱"}, "obc": {"type": "string", "description": "排序列名(orderByColumn)"}, "pageNumber": {"type": "integer", "description": "要查询的页号", "format": "int64", "example": 1}, "pageSize": {"type": "integer", "description": "每页包含的数据的条数", "format": "int64", "example": 10}, "params": {"type": "object"}, "status": {"type": "integer", "description": "用户的状态，例如0：停用，1：启用，2：锁定，3：过期等【1:启用, 0:停用, 2:锁定, 3:过期, 4:未启用】", "format": "int32", "enum": [0, 1, 2, 3, 4]}, "type": {"type": "integer", "description": "用户的类型，-1-系统管理员，1-企业管理员，2-普通管理员，3-普通用户，4-机构管理员【-1:系统管理员, 1:企业管理员, 2:普通管理员, 3:普通用户, 4:机构管理员】", "format": "int32", "enum": [-1, 1, 2, 3, 4]}}, "description": "登录并使用系统的用户"}, "UserUpdateDTO": {"title": "UserUpdateDTO", "type": "object", "properties": {"employeeInfo": {"description": "人员信息", "$ref": "#/components/schemas/EmployeeDTO"}, "id": {"type": "integer", "description": "用户ID", "format": "int64"}, "labels": {"type": "array", "description": "用户拥有的标签", "items": {"$ref": "#/components/schemas/UserLabelJoinDTO"}}, "manageRoles": {"type": "array", "description": "用户管理的角色", "items": {"type": "integer", "format": "int64"}}, "remark": {"type": "string", "description": "备注"}, "resources": {"type": "array", "description": "用户被授予的资源(CA专用)", "items": {"type": "integer", "format": "int64"}}, "roles": {"type": "array", "description": "用户被授予的角色", "items": {"type": "integer", "format": "int64"}}, "status": {"type": "integer", "description": "用户的状态，例如0：停用，1：启用，2：锁定，3：过期等【1:启用, 0:停用, 2:锁定, 3:过期, 4:未启用】", "format": "int32", "enum": [0, 1, 2, 3, 4]}, "type": {"type": "integer", "description": "用户的类型，-1-系统管理员，1-企业管理员，2-普通管理员，3-普通用户，4-机构管理员【-1:系统管理员, 1:企业管理员, 2:普通管理员, 3:普通用户, 4:机构管理员】", "format": "int32", "enum": [-1, 1, 2, 3, 4]}, "userManageOrganization": {"type": "array", "description": "用户管理的机构", "items": {"type": "integer", "format": "int64"}}, "userWorkForOrganization": {"type": "array", "description": "用户为哪些机构工作", "items": {"type": "integer", "format": "int64"}}}, "description": "登录并使用系统的用户"}, "UserVO": {"title": "UserVO", "type": "object", "properties": {"avatar": {"type": "string", "description": "用户的头像图片url"}, "emailAddress": {"type": "string", "description": "人员的电子邮件地址"}, "employeeCode": {"type": "string", "description": "人员编码"}, "employeeId": {"type": "integer", "description": "人员的唯一标识，没有业务含义", "format": "int64"}, "employeeName": {"type": "string", "description": "人员姓名"}, "gender": {"type": "integer", "description": "人员的性别，1表示男性，2表示女性", "format": "int32"}, "id": {"type": "integer", "description": "id", "format": "int64"}, "idCard": {"type": "string", "description": "身份证号"}, "jobNumber": {"type": "string", "description": "人员工号"}, "jobPosition": {"type": "string", "description": "岗位代码(多个逗号分隔)", "example": "0001,01AB"}, "name": {"type": "string", "description": "用户名，可用于登录系统，必须唯一。"}, "nameFullPath": {"type": "string", "description": "归属机构全路径"}, "organizationId": {"type": "integer", "description": "归属机构ID", "format": "int64"}, "organizationName": {"type": "string", "description": "归属机构"}, "organizationType": {"type": "integer", "description": "归属机构ID【0:集团, 1:公司, 2:部门】", "format": "int32", "enum": [0, 1, 2]}, "passwordStatus": {"type": "integer", "description": "用户的密码状态，1标识是初始状态，需要修改密码【1:初始化, 2:已修改】", "format": "int32", "enum": [1, 2]}, "path": {"type": "string", "description": "从根节点到当前节点的路径(不包含当前节点)， 例如(/r-1/c-13/d-25).  由代码自动生成，不展示给用户。用于后端查询或者生成树的时候使用"}, "phone": {"type": "string", "description": "人员的电话"}, "positionList": {"type": "array", "description": "岗位集合", "items": {"$ref": "#/components/schemas/JobPositionVO"}}, "remark": {"type": "string", "description": "备注"}, "status": {"type": "integer", "description": "用户的状态，例如0：停用，1：启用，2：锁定，3：过期等【1:启用, 0:停用, 2:锁定, 3:过期, 4:未启用】", "format": "int32", "enum": [0, 1, 2, 3, 4]}, "type": {"type": "integer", "description": "用户的类型，-1-系统管理员，1-企业管理员，2-普通管理员，3-普通用户，4-机构管理员【-1:系统管理员, 1:企业管理员, 2:普通管理员, 3:普通用户, 4:机构管理员】", "format": "int32", "enum": [-1, 1, 2, 3, 4]}, "workStatus": {"type": "integer", "description": "null【1:上班, 2:值班, 3:休假】", "format": "int32", "enum": [1, 2, 3]}, "workTerminal": {"type": "string", "description": "工作航站(多个逗号分隔)", "example": "CTU,PEK"}, "wxOpenid": {"type": "string", "description": "微信OpenId"}}, "description": "登录并使用系统的用户"}, "UserWorkForOrgDTO": {"title": "UserWorkForOrgDTO", "type": "object", "properties": {"children": {"type": "boolean", "description": "是否包含子部门", "example": false}, "jobPosition": {"type": "string", "description": "人员的岗位代码"}, "jobTitle": {"type": "integer", "description": "员工的职务", "format": "int32"}, "name": {"type": "string", "description": "用户名或姓名或工号"}, "organizationId": {"type": "integer", "description": "归属机构ID", "format": "int64"}, "status": {"type": "integer", "description": "用户的状态，例如0：停用，1：启用，2：锁定，3：过期等【1:启用, 0:停用, 2:锁定, 3:过期, 4:未启用】", "format": "int32", "enum": [0, 1, 2, 3, 4]}, "type": {"type": "integer", "description": "用户的类型，-1-系统管理员，1-企业管理员，2-普通管理员，3-普通用户，4-机构管理员【-1:系统管理员, 1:企业管理员, 2:普通管理员, 3:普通用户, 4:机构管理员】", "format": "int32", "enum": [-1, 1, 2, 3, 4]}}, "description": "获取工作部门用户DTO"}}, "securitySchemes": {"Authorization": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header"}}}}