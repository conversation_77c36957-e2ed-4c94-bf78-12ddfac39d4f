// request.ts
import Taro from '@tarojs/taro';
import { TOKENBASENAME } from '@/utils';
import { loginOut } from '@/utils/tools';

export const AUTHORIZATION = `HT-MINI-${TOKENBASENAME}`;

/**
 * 退出登录方法
 * 清除所有本地存储数据，并跳转到登录页面
 * @param isExpired 是否因为登录过期而退出，默认为 false
 */
export const logout = (isExpired: boolean = false) => {
  // 根据退出原因显示不同的提示
  Taro.showToast({
    title: isExpired ? '登录已过期，请重新登录' : '退出登录成功',
    icon: 'none',
    duration: 2000,
  });

  // 延迟跳转到登录页面，确保提示能够显示
  setTimeout(() => {
    // 跳转到登录页面
    loginOut();
  }, 1500);
};

interface RequestOptions {
  path: string;
  method?: keyof Taro.request.Method;
  type?: any;
  body?: any;
  query?: any;
  format?: any;
  header?: any;
  responseType?: any;
  isFile?: boolean;
  errorIsToast?: boolean;
}

// const BASE_URL = 'http://**************:8002'; // 赵侃
// const BASE_URL = 'http://************:8002'; // 侯川
const BASE_URL = process.env.TARO_APP_HT_MINI_API; // dev
const BASE_URL_UC = process.env.TARO_APP_HT_MINI_API; // UC服务地址

// 假设你的token存储在本地存储中
const getToken = () => {
  return { Authorization: `Bearer ${Taro.getStorageSync(AUTHORIZATION)}` }; // 携带token}
};

// eslint-disable-next-line @typescript-eslint/no-unused-vars
const request = async <T = any, _E = any>(
  options: RequestOptions & {
    isloading?: boolean;
  },
): Promise<T> => {
  // 设置请求默认值
  const {
    path,
    method = 'GET',
    body,
    query,
    type,
    isloading = false,
    responseType,
    isFile = false,
    errorIsToast = true,
    ...params
  } = options;

  try {
    // 请求拦截器
    isloading && Taro.showLoading({ title: '加载中...' });

    // 根据 path 选择对应的 BASE_URL
    const baseUrl = path.includes('/uc/api') ? BASE_URL_UC : BASE_URL;

    // 发起请求
    const response = await Taro.request({
      url: baseUrl + path, // 请求地址
      method: method, // 请求方法
      data: body || query, // 发送的数据
      header: {
        'content-type': type || 'application/json', // 默认头部
        ...getToken(),
        ...((params as any)?.header ?? {}), // 自定义头部
      },
      responseType: responseType,
      ...params,
    });
    isloading && Taro.hideLoading();

    // 处理返回
    if (response.statusCode === 200) {
      // 如果是文件下载请求，直接返回数据
      if (isFile) {
        return response.data;
      }

      // 普通请求检查code
      if (response.data?.code === 200) {
        return response.data;
      }
    }

    if (response.statusCode === 401 || response.data?.code === 401) {
      // 处理 401 未授权错误
      logout(true); // 传递 isExpired 参数为 true，表示登录过期
      return response?.data ?? {};
    }

    // 登录接口（子系统不存在login，后期可删除）
    if (response.data.access_token) {
      return response.data;
    }

    // 处理其他错误
    errorIsToast && handleError(response);
    return response?.data ?? {};
  } catch (error) {
    // 错误处理
    handleError(error);
    return error;
  }
};

const handleError = (error: any) => {
  // console.log('🚀🚀🚀 ~ error:', error);

  // 检查是否是 401 未授权错误
  if (error && (error.statusCode === 401 || error.data?.code === 401)) {
    // 调用退出登录方法，传递 isExpired 参数为 true，表示登录过期
    logout(true);
    return; // 不再继续抛出错误
  }

  const errorMessage =
    error?.data?.message || error?.message || '请求失败，请稍后重试';

  // if (error && error.statusCode) {
  //   // 根据实际API返回的错误信息进行调整
  //   errorMessage = `请求错误 ${error.statusCode}`;
  // }

  Taro.showToast({
    title: errorMessage,
    icon: 'none',
    duration: 2000,
  });

  throw new Error(errorMessage);
};

export default request;
