import Taro from '@tarojs/taro';

export * from './contanst';
export * from './eventEmit';

/**
 * 保存数据到本地存储
 * @param key 存储键名
 * @param data 存储数据
 * @param isObject 是否为对象类型
 */
export const saveDataToLocalStorage = (
  key: string,
  data: any,
  isObject = false,
) => {
  if (isObject) {
    Taro.setStorageSync(key, JSON.stringify(data));
  } else {
    Taro.setStorageSync(key, data);
  }
};

/**
 * 获取本地存储数据
 * @param key 存储键名
 * @returns 存储数据
 */
export const getDataFromLocalStorage = (key: string) => {
  return Taro.getStorageSync(key);
};
