// @ts-ignore
import Taro from '@tarojs/taro';
import CryptoES from 'crypto-es';

export const encodeFuc = (str: string): string => {
  let data = '';
  if (str) {
    data = CryptoES.AES.encrypt(
      str,
      CryptoES.enc.Utf8.parse('eupsi#2020swcare'),
      {
        mode: CryptoES.mode.CBC,
        iv: CryptoES.enc.Utf8.parse('s$eupsi@2020trav'),
        padding: CryptoES.pad.Pkcs7,
      },
    ).toString();
  }
  return data;
};

export const loginOut = () => {
  //删除所有的缓存
  Taro.clearStorageSync();
  Taro.reLaunch({
    url: '/pages/login/index',
  });
};

// 数据字典转为antd的map结构
export const dictToMap = (dict: any[]) => {
  return dict.reduce((pre: any, cur: any) => {
    pre[cur.dictValue] = cur.dictLabel;
    return pre;
  }, {});
};

export class AES {
  //密钥
  static key = CryptoES.enc.Utf8.parse('eupsi#2020swcare');
  //密钥偏移量
  static iv = CryptoES.enc.Utf8.parse('s$eupsi@2020trav');

  static encrypt = <T>(str: T): string => {
    let data = '';
    if (str) {
      const srcs = CryptoES.enc.Utf8.parse(String(str));
      const encrypted = CryptoES.AES.encrypt(srcs, AES.key, {
        iv: AES.iv,
        mode: CryptoES.mode.CBC,
        padding: CryptoES.pad.Pkcs7,
      });
      data = encrypted.toString();
    }
    return data;
  };
  static decrypt = <T>(str: T): string => {
    let data = '';
    if (str) {
      const decrypt = CryptoES.AES.decrypt(String(str), AES.key, {
        iv: AES.iv,
        mode: CryptoES.mode.CBC,
        padding: CryptoES.pad.Pkcs7,
      });
      data = decrypt.toString(CryptoES.enc.Utf8);
    }
    return data;
  };
}
