/** @type {import('tailwindcss').Config} */
module.exports = {
  // 这里给出了一份 taro 通用示例，具体要根据你自己项目的目录结构进行配置
  // 比如你使用 vue3 项目，你就需要把 vue 这个格式也包括进来
  // 不在 content glob表达式中包括的文件，在里面编写tailwindcss class，是不会生成对应的css工具类的
  //TODO: 具体px需要一一对应，其他的均需带上子项目base！！！！！！！
  //TODO: 不使用tailwindcss的项目，需要增加排除目录
  content: [
    './public/index.html',
    './src/**/*.{html,js,ts,jsx,tsx}',
    '!./src/subpackagesHt/**/*', // 排除subpackagesHt目录下的所有文件
  ],
  theme: {
    extend: {
      colors: {
        //锂电池项目
        'sqcli-hint-color': '#737578',
        'sqcli-primary-color': '#1872F0',
        'sqcli-primary-text-color': '#1D1F20',
        'sqcli-placeholder-color': '#C4C7CA',
        'sqcli-error-dark': '#DC2626',
      },
      width: {
        12: '12px',
        40: '40px',
      },
      padding: {
        16: '16px',
        12: '12px',
      },
      margin: {
        16: '16px',
      },
      height: {
        46: '46px',
      },
    },
  },
  // 其他配置项 ...
  corePlugins: {
    // 小程序不需要 preflight，因为这主要是给 h5 的，如果你要同时开发多端，你应该使用 process.env.TARO_ENV 环境变量来控制它
    preflight: false,
  },
};
